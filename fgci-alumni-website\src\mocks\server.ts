import { setupServer } from 'msw/node';
import { handlers } from './handlers';

// This configures a request mocking server with the given request handlers.
export const server = setupServer(...handlers);

// Establish API mocking before all tests.
export function setupMockServer() {
  // Enable API mocking before tests run.
  if (typeof beforeAll !== 'undefined') {
    beforeAll(() => {
      server.listen({
        onUnhandledRequest: 'warn'
      });
    });
  }

  // Reset any request handlers that we may add during the tests,
  // so they don't affect other tests.
  if (typeof afterEach !== 'undefined') {
    afterEach(() => {
      server.resetHandlers();
    });
  }

  // Clean up after the tests are finished.
  if (typeof afterAll !== 'undefined') {
    afterAll(() => {
      server.close();
    });
  }
}

export { server as mswServer };