'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface FormCheckboxProps {
  label: string;
  name: string;
  checked: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  touched?: boolean;
  className?: string;
  helpText?: string;
  description?: string;
}

export function FormCheckbox({
  label,
  name,
  checked,
  onChange,
  onBlur,
  required = false,
  disabled = false,
  errors = [],
  touched = false,
  className,
  helpText,
  description,
}: FormCheckboxProps) {
  const hasError = touched && errors.length > 0;
  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;
  const helpId = `${fieldId}-help`;
  const descId = `${fieldId}-desc`;

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            type="checkbox"
            id={fieldId}
            name={name}
            checked={checked}
            onChange={onChange}
            onBlur={onBlur}
            required={required}
            disabled={disabled}
            className={cn(
              'w-4 h-4 rounded border transition-colors',
              'focus:ring-2 focus:ring-maroon-500 focus:ring-offset-2',
              'disabled:cursor-not-allowed disabled:opacity-50',
              hasError
                ? 'border-red-500 text-red-600 focus:ring-red-500'
                : 'border-gray-300 text-maroon-600'
            )}
            aria-invalid={hasError}
            aria-describedby={cn(
              hasError && errorId,
              helpText && helpId,
              description && descId
            )}
          />
        </div>
        <div className="ml-3 text-sm">
          <label 
            htmlFor={fieldId} 
            className={cn(
              'font-medium cursor-pointer',
              disabled ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700'
            )}
          >
            {label}
            {required && (
              <span className="text-red-500 ml-1" aria-label="required">
                *
              </span>
            )}
          </label>
          {description && (
            <p id={descId} className="text-gray-600 mt-1">
              {description}
            </p>
          )}
        </div>
      </div>
      
      {helpText && !hasError && (
        <p id={helpId} className="text-sm text-gray-600 ml-7">
          {helpText}
        </p>
      )}
      
      {hasError && (
        <div id={errorId} className="ml-7 space-y-1" role="alert">
          {errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600">
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}