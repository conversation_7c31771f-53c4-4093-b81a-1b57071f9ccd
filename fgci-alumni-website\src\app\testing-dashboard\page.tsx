'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, Button } from '@/components/ui';
import { 
  BeakerIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PlayIcon,
  DocumentTextIcon,
  ChartBarIcon,
  BugAntIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

interface TestResult {
  name: string;
  status: 'passed' | 'failed' | 'running' | 'pending';
  duration?: number;
  error?: string;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  coverage?: number;
}

export default function TestingDashboardPage() {
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testResults, setTestResults] = useState<TestSuite[]>([
    {
      name: 'Unit Tests',
      coverage: 85,
      tests: [
        { name: 'Button Component', status: 'passed', duration: 120 },
        { name: 'Card Component', status: 'passed', duration: 95 },
        { name: 'Form Validation', status: 'passed', duration: 200 },
        { name: 'Animation Utils', status: 'passed', duration: 150 },
        { name: 'SEO Utils', status: 'passed', duration: 80 },
      ]
    },
    {
      name: 'Integration Tests',
      coverage: 78,
      tests: [
        { name: 'Homepage Integration', status: 'passed', duration: 1500 },
        { name: 'Navigation Flow', status: 'passed', duration: 2200 },
        { name: 'Search Functionality', status: 'passed', duration: 1800 },
        { name: 'Form Submission', status: 'failed', duration: 1200, error: 'Network timeout' },
      ]
    },
    {
      name: 'E2E Tests',
      coverage: 72,
      tests: [
        { name: 'User Journey - Alumni Search', status: 'passed', duration: 5000 },
        { name: 'User Journey - Event Registration', status: 'passed', duration: 4500 },
        { name: 'Mobile Responsiveness', status: 'passed', duration: 3200 },
        { name: 'Accessibility Compliance', status: 'passed', duration: 2800 },
      ]
    },
    {
      name: 'Performance Tests',
      coverage: 90,
      tests: [
        { name: 'Lighthouse Performance', status: 'passed', duration: 8000 },
        { name: 'Core Web Vitals', status: 'passed', duration: 6000 },
        { name: 'Bundle Size Analysis', status: 'passed', duration: 2000 },
        { name: 'Image Optimization', status: 'passed', duration: 3000 },
      ]
    }
  ]);

  const runAllTests = async () => {
    setIsRunningTests(true);
    
    // Simulate running tests
    const updatedResults = testResults.map(suite => ({
      ...suite,
      tests: suite.tests.map(test => ({
        ...test,
        status: 'running' as const
      }))
    }));
    
    setTestResults(updatedResults);
    
    // Simulate test completion
    setTimeout(() => {
      const finalResults = testResults.map(suite => ({
        ...suite,
        tests: suite.tests.map(test => ({
          ...test,
          status: Math.random() > 0.1 ? 'passed' as const : 'failed' as const,
          duration: Math.floor(Math.random() * 3000) + 500
        }))
      }));
      
      setTestResults(finalResults);
      setIsRunningTests(false);
    }, 3000);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return <CheckCircleIcon className="w-4 h-4 text-green-600" />;
      case 'failed':
        return <XCircleIcon className="w-4 h-4 text-red-600" />;
      case 'running':
        return <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      case 'pending':
        return <ClockIcon className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'passed':
        return 'text-green-600';
      case 'failed':
        return 'text-red-600';
      case 'running':
        return 'text-blue-600';
      case 'pending':
        return 'text-gray-400';
    }
  };

  const getTotalStats = () => {
    const allTests = testResults.flatMap(suite => suite.tests);
    const passed = allTests.filter(test => test.status === 'passed').length;
    const failed = allTests.filter(test => test.status === 'failed').length;
    const total = allTests.length;
    const avgCoverage = testResults.reduce((sum, suite) => sum + (suite.coverage || 0), 0) / testResults.length;
    
    return { passed, failed, total, avgCoverage };
  };

  const stats = getTotalStats();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container-custom py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
              Testing Dashboard
            </h1>
            <p className="body-large text-gray-600 mb-6">
              Comprehensive testing suite for the FGC Ikom Alumni website including 
              unit tests, integration tests, E2E tests, and performance monitoring.
            </p>
            
            <div className="flex justify-center">
              <Button 
                onClick={runAllTests}
                disabled={isRunningTests}
                variant="primary"
                size="lg"
                icon={<PlayIcon className="w-5 h-5" />}
              >
                {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container-custom py-12">
        {/* Test Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card variant="elevated">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <CheckCircleIcon className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-2xl font-bold text-green-600">{stats.passed}</div>
              <div className="text-sm text-gray-600">Tests Passed</div>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <XCircleIcon className="w-6 h-6 text-red-600" />
              </div>
              <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
              <div className="text-sm text-gray-600">Tests Failed</div>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <BeakerIcon className="w-6 h-6 text-blue-600" />
              </div>
              <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
              <div className="text-sm text-gray-600">Total Tests</div>
            </CardContent>
          </Card>

          <Card variant="elevated">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <ChartBarIcon className="w-6 h-6 text-purple-600" />
              </div>
              <div className="text-2xl font-bold text-purple-600">{stats.avgCoverage.toFixed(0)}%</div>
              <div className="text-sm text-gray-600">Avg Coverage</div>
            </CardContent>
          </Card>
        </div>

        {/* Test Suites */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {testResults.map((suite, index) => (
            <Card key={index} variant="elevated">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {suite.name === 'Unit Tests' && <DocumentTextIcon className="w-5 h-5" />}
                    {suite.name === 'Integration Tests' && <BugAntIcon className="w-5 h-5" />}
                    {suite.name === 'E2E Tests' && <ShieldCheckIcon className="w-5 h-5" />}
                    {suite.name === 'Performance Tests' && <ChartBarIcon className="w-5 h-5" />}
                    {suite.name}
                  </CardTitle>
                  {suite.coverage && (
                    <div className="text-sm text-gray-600">
                      {suite.coverage}% coverage
                    </div>
                  )}
                </div>
                {suite.coverage && (
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${suite.coverage}%` }}
                    />
                  </div>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {suite.tests.map((test, testIndex) => (
                    <div key={testIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(test.status)}
                        <div>
                          <div className="font-medium text-gray-900">{test.name}</div>
                          {test.error && (
                            <div className="text-sm text-red-600">{test.error}</div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-sm font-medium ${getStatusColor(test.status)}`}>
                          {test.status.charAt(0).toUpperCase() + test.status.slice(1)}
                        </div>
                        {test.duration && (
                          <div className="text-xs text-gray-500">
                            {test.duration}ms
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Testing Tools & Commands */}
        <div className="mt-12">
          <Card variant="elevated">
            <CardHeader>
              <CardTitle>Testing Commands & Tools</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Unit & Integration Tests</h3>
                  <div className="space-y-2 text-sm font-mono bg-gray-100 p-4 rounded-lg">
                    <div>npm run test</div>
                    <div>npm run test:watch</div>
                    <div>npm run test:coverage</div>
                    <div>npm run test:ci</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">E2E Tests</h3>
                  <div className="space-y-2 text-sm font-mono bg-gray-100 p-4 rounded-lg">
                    <div>npx playwright test</div>
                    <div>npx playwright test --ui</div>
                    <div>npx playwright test --headed</div>
                    <div>npx playwright show-report</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Performance Tests</h3>
                  <div className="space-y-2 text-sm font-mono bg-gray-100 p-4 rounded-lg">
                    <div>npm run lighthouse</div>
                    <div>npm run bundle-analyzer</div>
                    <div>npm run perf:audit</div>
                  </div>
                </div>

                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Accessibility Tests</h3>
                  <div className="space-y-2 text-sm font-mono bg-gray-100 p-4 rounded-lg">
                    <div>npm run a11y:test</div>
                    <div>npm run a11y:audit</div>
                    <div>axe-core integration</div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Testing Best Practices</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Write tests before implementing features (TDD)</li>
                  <li>• Maintain high test coverage (&gt;80%)</li>
                  <li>• Test user interactions, not implementation details</li>
                  <li>• Use semantic queries for better accessibility testing</li>
                  <li>• Mock external dependencies and APIs</li>
                  <li>• Run tests in CI/CD pipeline</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
