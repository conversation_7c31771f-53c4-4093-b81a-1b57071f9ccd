'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, Button, Avatar } from '@/components/ui';
import { LoadingStates } from '@/components/ui/LoadingStates';
import { Breadcrumbs, StructuredData } from '@/components/seo';
import { 
  MapPinIcon, 
  UsersIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  PhoneIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { useChapter } from '@/hooks/useChapters';
import { ChapterGallery } from '@/components/chapters/ChapterGallery';
import { generatePersonStructuredData, generateEventStructuredData } from '@/lib/seo';
import Link from 'next/link';

interface ChapterDetailPageClientProps {
  slug: string;
}

export default function ChapterDetailPageClient({ slug }: ChapterDetailPageClientProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'executives' | 'activities' | 'gallery'>('overview');

  // Fetch chapter data with React Query
  const { 
    data: chapter, 
    isLoading, 
    error, 
    refetch 
  } = useChapter(slug);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getActivityTypeColor = (type: string) => {
    switch (type) {
      case 'meeting':
        return 'bg-blue-100 text-blue-800';
      case 'social':
        return 'bg-green-100 text-green-800';
      case 'fundraiser':
        return 'bg-purple-100 text-purple-800';
      case 'memorial':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Generate structured data for the chapter
  const chapterStructuredData = chapter ? {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: chapter.name,
    description: chapter.description,
    url: `https://www.fgcikomalumni.org.ng/chapters/${chapter.slug}`,
    location: chapter.region ? {
      '@type': 'Place',
      name: chapter.region
    } : undefined,
    foundingDate: chapter.establishedDate,
    numberOfEmployees: chapter.memberCount,
    contactPoint: chapter.contactEmail ? {
      '@type': 'ContactPoint',
      email: chapter.contactEmail,
      contactType: 'customer service'
    } : undefined,
    member: chapter.exco.map(exec => generatePersonStructuredData({
      name: exec.name,
      jobTitle: exec.role,
      alumniOf: 'Federal Government College Ikom',
      image: exec.photo,
      email: exec.email
    }))
  } : null;

  // Generate breadcrumbs
  const breadcrumbs = chapter ? [
    { name: 'Chapters', url: '/chapters' },
    { name: chapter.name, url: `/chapters/${chapter.slug}` }
  ] : [];

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container-custom py-12">
          <Breadcrumbs items={breadcrumbs} />
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="heading-3 text-xl text-gray-900 mb-2">Failed to Load Chapter</h3>
            <p className="text-gray-600 mb-4">
              We're having trouble loading this chapter. Please try again.
            </p>
            <div className="flex gap-4 justify-center">
              <Button
                variant="primary"
                onClick={() => refetch()}
              >
                Try Again
              </Button>
              <Link href="/chapters">
                <Button variant="outline">
                  <ArrowLeftIcon className="w-4 h-4 mr-2" />
                  Back to Chapters
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <LoadingStates.ChapterDetail />
      </div>
    );
  }

  // Not found state
  if (!chapter) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Chapter Not Found</h1>
          <p className="text-gray-600 mb-6">The chapter you&apos;re looking for doesn&apos;t exist.</p>
          <Link href="/chapters">
            <Button variant="primary">
              <ArrowLeftIcon className="w-4 h-4 mr-2" />
              Back to Chapters
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <>
      {chapterStructuredData && (
        <StructuredData data={chapterStructuredData} id="chapter-page" />
      )}
      
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="relative h-96 overflow-hidden">
          <img
            src={chapter.heroImage}
            alt={chapter.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/40" />
          <div className="absolute inset-0 flex items-center">
            <div className="container-custom">
              <Breadcrumbs items={breadcrumbs} className="mb-4 text-white" />
              <div className="text-white max-w-3xl">
                <Link href="/chapters" className="inline-flex items-center text-white/80 hover:text-white mb-4 transition-colors">
                  <ArrowLeftIcon className="w-4 h-4 mr-2" />
                  Back to Chapters
                </Link>
                <h1 className="heading-1 text-4xl sm:text-5xl mb-4 text-white">
                  {chapter.name}
                </h1>
                <div className="flex items-center gap-4 text-white/90 mb-4">
                  <div className="flex items-center">
                    <MapPinIcon className="w-5 h-5 mr-2" />
                    {chapter.region}
                  </div>
                  <div className="flex items-center">
                    <UsersIcon className="w-5 h-5 mr-2" />
                    {chapter.memberCount} members
                  </div>
                  <div className="flex items-center">
                    <CalendarDaysIcon className="w-5 h-5 mr-2" />
                    Est. {new Date(chapter.establishedDate || '').getFullYear()}
                  </div>
                </div>
                <p className="body-large text-white/90">
                  {chapter.summary}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white border-b sticky top-0 z-10">
          <div className="container-custom">
            <nav className="flex space-x-8">
              {[
                { id: 'overview', label: 'Overview' },
                { id: 'executives', label: 'Executive Committee' },
                { id: 'activities', label: 'Activities' },
                { id: 'gallery', label: 'Gallery' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'overview' | 'executives' | 'activities' | 'gallery')}
                  className={cn(
                    'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
                    activeTab === tab.id
                      ? 'border-maroon-700 text-maroon-700'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  )}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="container-custom py-12">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Main Content */}
              <div className="lg:col-span-2">
                <Card variant="elevated" className="mb-8">
                  <CardHeader>
                    <CardTitle>About {chapter.name}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-700 leading-relaxed">
                      {chapter.description}
                    </p>
                  </CardContent>
                </Card>

                {/* Recent Activities */}
                <Card variant="elevated">
                  <CardHeader>
                    <CardTitle>Recent Activities</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {chapter.activities.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">No recent activities</p>
                    ) : (
                      <div className="space-y-4">
                        {chapter.activities.slice(0, 3).map((activity) => (
                          <div key={activity.id} className="border-l-4 border-maroon-200 pl-4">
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="font-semibold text-gray-900">{activity.title}</h4>
                              <span className={cn(
                                'px-2 py-1 text-xs font-medium rounded-full',
                                getActivityTypeColor(activity.type)
                              )}>
                                {activity.type}
                              </span>
                            </div>
                            <p className="text-gray-600 text-sm mb-2">{activity.description}</p>
                            <div className="flex items-center text-xs text-gray-500 space-x-4">
                              <span>{formatDate(activity.date)}</span>
                              {activity.location && (
                                <span className="flex items-center">
                                  <MapPinIcon className="w-3 h-3 mr-1" />
                                  {activity.location}
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                {/* Contact Information */}
                <Card variant="elevated">
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {chapter.contactEmail && (
                      <div className="flex items-center group">
                        <EnvelopeIcon className="w-5 h-5 text-gray-400 mr-3 group-hover:text-maroon-600 transition-colors" />
                        <a 
                          href={`mailto:${chapter.contactEmail}`}
                          className="text-maroon-700 hover:text-maroon-800 transition-colors break-all"
                          aria-label={`Email ${chapter.name}`}
                        >
                          {chapter.contactEmail}
                        </a>
                      </div>
                    )}
                    
                    {/* Executive Contact */}
                    {chapter.exco.length > 0 && chapter.exco[0].phone && (
                      <div className="border-t pt-4">
                        <p className="text-sm text-gray-600 mb-2">Contact President:</p>
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-900">
                            {chapter.exco[0].name}
                          </span>
                          <div className="flex gap-2">
                            {chapter.exco[0].email && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`mailto:${chapter.exco[0].email}`)}
                                className="p-2 hover:bg-maroon-50 hover:text-maroon-700"
                                aria-label={`Email ${chapter.exco[0].name}`}
                              >
                                <EnvelopeIcon className="w-4 h-4" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(`tel:${chapter.exco[0].phone}`)}
                              className="p-2 hover:bg-maroon-50 hover:text-maroon-700"
                              aria-label={`Call ${chapter.exco[0].name}`}
                            >
                              <PhoneIcon className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <Button variant="primary" size="sm" className="w-full mt-4">
                      Join This Chapter
                    </Button>
                  </CardContent>
                </Card>

                {/* Chapter Stats */}
                <Card variant="elevated">
                  <CardHeader>
                    <CardTitle>Chapter Statistics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Members</span>
                      <span className="font-semibold">{chapter.memberCount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Established</span>
                      <span className="font-semibold">{formatDate(chapter.establishedDate || '')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Last Activity</span>
                      <span className="font-semibold">{formatDate(chapter.lastActivityDate || '')}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Chapter Type</span>
                      <span className="font-semibold capitalize">{chapter.type}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'executives' && (
            <div>
              <div className="mb-8">
                <h2 className="heading-2 text-2xl mb-4">Executive Committee</h2>
                <p className="text-gray-600">
                  Meet the dedicated leaders who guide and coordinate {chapter.name} activities.
                </p>
              </div>

              {chapter.exco.length === 0 ? (
                <Card variant="elevated" className="text-center py-12">
                  <CardContent>
                    <UsersIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No Executive Information</h3>
                    <p className="text-gray-500">Executive committee information is not available at this time.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {chapter.exco.map((executive) => (
                    <Card key={executive.id} variant="elevated" hover className="text-center">
                      <CardContent className="pt-6">
                        <Avatar
                          src={executive.photo}
                          alt={executive.name}
                          size="lg"
                          className="mx-auto mb-4"
                        />
                        <h3 className="font-semibold text-lg text-gray-900 mb-1">
                          {executive.name}
                        </h3>
                        <p className="text-maroon-700 font-medium mb-2">
                          {executive.role}
                        </p>
                        <p className="text-sm text-gray-600 mb-4">
                          Class of {executive.graduationYear}
                        </p>
                        {executive.bio && (
                          <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                            {executive.bio}
                          </p>
                        )}
                        <div className="flex justify-center space-x-2">
                          {executive.email && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(`mailto:${executive.email}`)}
                              className="hover:bg-maroon-50 hover:text-maroon-700"
                              aria-label={`Email ${executive.name}`}
                            >
                              <EnvelopeIcon className="w-4 h-4" />
                            </Button>
                          )}
                          {executive.phone && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(`tel:${executive.phone}`)}
                              className="hover:bg-maroon-50 hover:text-maroon-700"
                              aria-label={`Call ${executive.name}`}
                            >
                              <PhoneIcon className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'activities' && (
            <div>
              <div className="mb-8">
                <h2 className="heading-2 text-2xl mb-4">Chapter Activities</h2>
                <p className="text-gray-600">
                  Explore the various events and activities organized by {chapter.name}.
                </p>
              </div>

              {chapter.activities.length === 0 ? (
                <Card variant="elevated" className="text-center py-12">
                  <CardContent>
                    <CalendarDaysIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">No Activities Yet</h3>
                    <p className="text-gray-500">This chapter hasn&apos;t posted any activities yet.</p>
                  </CardContent>
                </Card>
              ) : (
                <div className="relative">
                  {/* Timeline line */}
                  <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-maroon-200"></div>
                  
                  <div className="space-y-8">
                    {chapter.activities.map((activity, index) => (
                      <div key={activity.id} className="relative flex items-start">
                        {/* Timeline dot */}
                        <div className="absolute left-6 w-4 h-4 bg-maroon-600 rounded-full border-4 border-white shadow-sm z-10"></div>
                        
                        {/* Activity card */}
                        <div className="ml-16 flex-1">
                          <Card variant="elevated" hover className="transition-all duration-300">
                            <CardContent className="p-6">
                              <div className="flex items-start justify-between mb-4">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <span className={cn(
                                      'px-2 py-1 text-xs font-medium rounded-full',
                                      getActivityTypeColor(activity.type)
                                    )}>
                                      {activity.type.charAt(0).toUpperCase() + activity.type.slice(1)}
                                    </span>
                                    <span className="text-sm text-gray-500">
                                      {formatDate(activity.date)}
                                    </span>
                                  </div>
                                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                                    {activity.title}
                                  </h3>
                                  {activity.location && (
                                    <div className="flex items-center text-sm text-gray-500 mb-3">
                                      <MapPinIcon className="w-4 h-4 mr-1" />
                                      {activity.location}
                                    </div>
                                  )}
                                </div>
                              </div>
                              
                              <p className="text-gray-700 mb-4 leading-relaxed">
                                {activity.description}
                              </p>

                              {activity.photos && activity.photos.length > 0 && (
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                  {activity.photos.slice(0, 4).map((photo, photoIndex) => (
                                    <div key={photoIndex} className="relative group overflow-hidden rounded-lg">
                                      <img
                                        src={photo}
                                        alt={`${activity.title} photo ${photoIndex + 1}`}
                                        className="w-full h-24 object-cover transition-transform duration-300 group-hover:scale-110"
                                      />
                                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'gallery' && (
            <ChapterGallery chapterSlug={slug} />
          )}
        </div>
      </div>
    </>
  );
}