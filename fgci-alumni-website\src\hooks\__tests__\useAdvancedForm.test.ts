import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useAdvancedForm } from '../useAdvancedForm';
import { contactFormSchema } from '@/lib/validation';
import { ContactFormData } from '@/lib/types';

describe('useAdvancedForm', () => {
  const initialData: ContactFormData = {
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    graduationYear: undefined,
    inquiryType: '',
    chapter: '',
  };

  let mockOnSubmit: ReturnType<typeof vi.fn>;
  let mockOnFieldChange: ReturnType<typeof vi.fn>;
  let mockOnValidationChange: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockOnSubmit = vi.fn();
    mockOnFieldChange = vi.fn();
    mockOnValidationChange = vi.fn();
  });

  it('initializes with correct default values', () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    expect(result.current.data).toEqual(initialData);
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
    expect(result.current.isValidating).toBe(false);
    expect(result.current.isValid).toBe(false);
    expect(result.current.hasErrors).toBe(false);
    expect(result.current.submitAttempted).toBe(false);
  });

  it('calls onFieldChange callback when field changes', () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
        onFieldChange: mockOnFieldChange,
      })
    );

    act(() => {
      result.current.handleChange({
        target: { name: 'name', value: 'John Doe', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(mockOnFieldChange).toHaveBeenCalledWith(
      'name',
      'John Doe',
      expect.objectContaining({ name: 'John Doe' })
    );
  });

  it('calls onValidationChange callback when validation state changes', async () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
        onValidationChange: mockOnValidationChange,
        validateOnBlur: true,
      })
    );

    await act(async () => {
      result.current.handleBlur({
        target: { name: 'name', value: '' },
      } as React.FocusEvent<HTMLInputElement>);
    });

    expect(mockOnValidationChange).toHaveBeenCalledWith(
      false,
      expect.objectContaining({
        name: expect.arrayContaining(['This field is required'])
      })
    );
  });

  it('validates on change when validateOnChange is enabled', async () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
        validateOnChange: true,
        debounceMs: 0, // Disable debouncing for tests
      })
    );

    await act(async () => {
      result.current.handleChange({
        target: { name: 'email', value: 'invalid-email', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    // Wait for validation to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.errors.email).toContain('Please enter a valid email address');
  });

  it('handles file input changes correctly', () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData: { ...initialData, file: null },
        validationSchema: contactFormSchema,
      })
    );

    const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const mockFileList = {
      0: mockFile,
      length: 1,
      item: () => mockFile,
    } as FileList;

    act(() => {
      result.current.handleChange({
        target: { 
          name: 'file', 
          type: 'file',
          files: mockFileList
        },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.data.file).toBe(mockFile);
  });

  it('sets multiple field errors correctly', () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
      })
    );

    act(() => {
      result.current.setFieldErrors({
        name: 'Name is required',
        email: ['Email is required', 'Invalid email format'],
      });
    });

    expect(result.current.errors.name).toEqual(['Name is required']);
    expect(result.current.errors.email).toEqual(['Email is required', 'Invalid email format']);
  });

  it('focuses on first error field after failed submission', async () => {
    const mockFocus = vi.fn();
    const mockQuerySelector = vi.fn().mockReturnValue({ focus: mockFocus });
    
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
      })
    );

    // Mock form ref
    result.current.formRef.current = {
      querySelector: mockQuerySelector,
    } as unknown as HTMLFormElement;

    const mockEvent = {
      preventDefault: vi.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    expect(mockQuerySelector).toHaveBeenCalledWith('[name="name"]');
    expect(mockFocus).toHaveBeenCalled();
  });

  it('validates after submit attempt even without validateOnChange', async () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
        validateOnChange: false,
        debounceMs: 0,
      })
    );

    // First, change a field
    act(() => {
      result.current.handleChange({
        target: { name: 'name', value: 'J', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    // Mark field as touched
    act(() => {
      result.current.handleBlur({
        target: { name: 'name', value: 'J' },
      } as React.FocusEvent<HTMLInputElement>);
    });

    // Attempt submission (which will fail)
    const mockEvent = {
      preventDefault: vi.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    expect(result.current.submitAttempted).toBe(true);

    // Now changing the field should trigger validation
    await act(async () => {
      result.current.handleChange({
        target: { name: 'name', value: '', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    // Wait for validation
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.errors.name).toContain('This field is required');
  });

  it('provides correct field state', () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
      })
    );

    act(() => {
      result.current.setFieldError('name', 'Test error');
      result.current.handleBlur({
        target: { name: 'name', value: '' },
      } as React.FocusEvent<HTMLInputElement>);
    });

    const fieldState = result.current.getFieldState('name');
    
    expect(fieldState.error).toEqual(['Test error']);
    expect(fieldState.hasError).toBe(true);
    expect(fieldState.touched).toBe(true);
  });

  it('resets form correctly with new data', () => {
    const { result } = renderHook(() =>
      useAdvancedForm({
        initialData,
        validationSchema: contactFormSchema,
      })
    );

    // Make some changes
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldError('email', 'Some error');
    });

    expect(result.current.data.name).toBe('John Doe');
    expect(result.current.errors.email).toEqual(['Some error']);

    // Reset with new data
    act(() => {
      result.current.reset({ name: 'Jane Doe' });
    });

    expect(result.current.data.name).toBe('Jane Doe');
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
  });
});