'use client';

import { useQuery } from '@tanstack/react-query';
import { useMemo } from 'react';
import { Person, ExcoBotFilters } from '@/lib/types';
import { apiClient } from '@/lib/api-client';

interface UseAlumniSearchParams {
  query?: string;
  filters?: ExcoBotFilters;
  sortBy?: string;
  enabled?: boolean;
}



export function useAlumniSearch({
  query = '',
  filters = {},
  sortBy = 'name',
  enabled = true,
}: UseAlumniSearchParams) {
  // Create a stable query key
  const queryKey = useMemo(() => [
    'alumni-search',
    query,
    filters,
    sortBy,
  ], [query, filters, sortBy]);

  return useQuery({
    queryKey,
    queryFn: async (): Promise<Person[]> => {
      const searchParams = new URLSearchParams();
      
      if (query) {
        searchParams.append('q', query);
      }
      
      // Add filters to search params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== '' && value !== 'any') {
          searchParams.append(key, String(value));
        }
      });
      
      if (sortBy) {
        searchParams.append('sort', sortBy);
      }

      const response = await apiClient.get<Person[]>(
        `/api/excobot/search?${searchParams.toString()}`
      );
      
      return response.data;
    },
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  });
}

// Helper hook for getting search suggestions
export function useAlumniSuggestions(query: string) {
  return useQuery({
    queryKey: ['alumni-suggestions', query],
    queryFn: async (): Promise<string[]> => {
      if (query.length < 2) return [];
      
      const response = await apiClient.get<{ suggestions: string[] }>(
        `/api/excobot/suggestions?q=${encodeURIComponent(query)}`
      );
      
      return response.data.suggestions;
    },
    enabled: query.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Helper hook for getting filter options
export function useAlumniFilterOptions() {
  return useQuery({
    queryKey: ['alumni-filter-options'],
    queryFn: async () => {
      const response = await apiClient.get<{
        chapters: string[];
        graduationYears: number[];
        roles: string[];
        locations: string[];
      }>('/api/excobot/filter-options');
      
      return response;
    },
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
  });
}