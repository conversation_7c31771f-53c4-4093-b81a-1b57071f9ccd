'use client';

import {
  Hero<PERSON>arousel,
  HighlightCards,
  Announcements,
  create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>light,
  createSet<PERSON>ighlight,
  createEventHighlight,
  createGalleryHighlight,
  createSampleAnnouncements
} from '@/components/ui';
import { Button } from '@/components/ui';
import { StructuredData } from '@/components/seo';
import {
  UserGroupIcon,
  AcademicCapIcon,
  CalendarDaysIcon,
  PhotoIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import type { HeroSlide, HighlightCard } from '@/components/ui';
import { generateOrganizationStructuredData, generateEducationalOrganizationStructuredData } from '@/lib/seo';
import Link from 'next/link';

// Hero slides data
const heroSlides: HeroSlide[] = [
  {
    id: 'slide-1',
    title: 'Welcome to FGC Ikom Alumni Association',
    subtitle: 'Connecting Generations',
    description: 'Join our vibrant community of Federal Government College Ikom alumni from around the world. Stay connected, give back, and celebrate our shared heritage.',
    backgroundImage: 'https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    ctaText: 'Explore Our Community',
    overlay: 'gradient'
  },
  {
    id: 'slide-2',
    title: 'Annual Homecoming 2024',
    subtitle: 'December 14-15, 2024',
    description: 'Join us for the biggest alumni gathering of the year at FGC Ikom Campus. Reconnect with classmates, celebrate achievements, and create new memories.',
    backgroundImage: 'https://images.unsplash.com/photo-1511632765486-a01980e01a18?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    ctaText: 'Register Now',
    overlay: 'dark'
  },
  {
    id: 'slide-3',
    title: 'Building Futures Through Education',
    subtitle: 'Scholarship Program',
    description: 'Our scholarship fund has supported over 500 students. Help us continue making quality education accessible to deserving students.',
    backgroundImage: 'https://images.unsplash.com/photo-1427504494785-3a9ca7044f45?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    ctaText: 'Support Education',
    overlay: 'gradient'
  }
];

// Highlight cards data
const highlightCards: HighlightCard[] = [
  {
    ...createChapterHighlight(12),
    icon: <UserGroupIcon className="w-full h-full" />,
    ctaLink: '/chapters'
  },
  {
    ...createSetHighlight(45),
    icon: <AcademicCapIcon className="w-full h-full" />,
    ctaLink: '/sets'
  },
  {
    ...createEventHighlight(8),
    icon: <CalendarDaysIcon className="w-full h-full" />,
    ctaLink: '/events'
  },
  {
    ...createGalleryHighlight('2.5K+'),
    icon: <PhotoIcon className="w-full h-full" />,
    ctaLink: '/gallery'
  }
];

export default function HomePageClient() {
  const announcements = createSampleAnnouncements();

  // Generate structured data for homepage
  const websiteStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'FGC Ikom Alumni Association',
    url: 'https://www.fgcikomalumni.org.ng',
    description: 'Official website of the Federal Government College Ikom Alumni Association',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://www.fgcikomalumni.org.ng/excobot?search={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  };

  const organizationData = generateOrganizationStructuredData();
  const educationalOrgData = generateEducationalOrganizationStructuredData();

  return (
    <>
      <StructuredData data={websiteStructuredData} id="website" />
      <StructuredData data={organizationData} id="organization" />
      <StructuredData data={educationalOrgData} id="educational-organization" />

      <main className="min-h-screen">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-maroon-800 via-maroon-700 to-maroon-600 text-white min-h-screen flex items-center justify-center overflow-hidden"
                 style={{
                   background: 'linear-gradient(135deg, #5C0B12 0%, #7B0F17 50%, #8C1119 100%)',
                   minHeight: '100vh',
                   position: 'relative'
                 }}>
          {/* Decorative elements */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-32 h-32 border border-gold-300 rounded-full" style={{ borderColor: '#F0D898' }}></div>
            <div className="absolute bottom-32 right-32 w-24 h-24 border border-gold-400 rounded-full" style={{ borderColor: '#E8CA70' }}></div>
            <div className="absolute top-1/2 left-10 w-16 h-16 bg-gold-500 rounded-full opacity-20" style={{ backgroundColor: '#D4A017' }}></div>
          </div>

          <div className="container mx-auto px-6 text-center relative z-10 max-w-5xl">
            <div className="mb-6">
              <span className="inline-block px-4 py-2 bg-gold-500 bg-opacity-20 text-gold-200 rounded-full text-sm font-medium mb-4"
                    style={{ backgroundColor: 'rgba(212, 160, 23, 0.2)', color: '#F7E6C0' }}>
                Connecting Generations Since 1973
              </span>
            </div>

            <h1 className="heading-1 mb-8 text-white leading-tight"
                style={{
                  color: '#ffffff',
                  fontSize: 'clamp(3rem, 6vw, 5rem)',
                  fontFamily: 'var(--font-heading, serif)',
                  fontWeight: '700',
                  lineHeight: '1.1',
                  letterSpacing: '-0.02em'
                }}>
              Federal Government College<br />
              <span className="text-gold-300" style={{ color: '#F0D898' }}>Ikom Alumni Association</span>
            </h1>

            <p className="text-xl mb-12 text-gray-100 max-w-3xl mx-auto leading-relaxed"
               style={{
                 color: '#f3f4f6',
                 fontSize: 'clamp(1.125rem, 2.5vw, 1.25rem)',
                 lineHeight: '1.7'
               }}>
              Join our vibrant global community of distinguished alumni. Connect with classmates,
              celebrate achievements, and contribute to the legacy of excellence that defines FGC Ikom.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                variant="secondary"
                size="lg"
                className="text-lg px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300"
                style={{
                  backgroundColor: '#D4A017',
                  color: '#ffffff',
                  padding: '1rem 2.5rem',
                  fontSize: '1.125rem',
                  borderRadius: '0.75rem',
                  border: 'none',
                  fontWeight: '600',
                  boxShadow: '0 10px 25px rgba(0,0,0,0.2)'
                }}
              >
                Join Our Community
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="text-lg px-10 py-4 border-2 border-white text-white hover:bg-white hover:text-maroon-700 transition-all duration-300"
                style={{
                  borderColor: '#ffffff',
                  color: '#ffffff',
                  padding: '1rem 2.5rem',
                  fontSize: '1.125rem',
                  borderRadius: '0.75rem',
                  fontWeight: '600',
                  backgroundColor: 'transparent'
                }}
              >
                Explore Chapters
              </Button>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <div style={{ backgroundColor: '#F8F5EF' }}>
          {/* Highlight Cards Section */}
          <section className="py-24 relative">
            <div className="container mx-auto px-6 max-w-7xl">
              <div className="text-center mb-16">
                <h2 className="heading-2 text-maroon-800 mb-6"
                    style={{
                      color: '#7B0F17',
                      fontSize: 'clamp(2.5rem, 4vw, 3.5rem)',
                      fontFamily: 'var(--font-heading, serif)',
                      fontWeight: '600',
                      lineHeight: '1.2'
                    }}>
                  Discover Our Community
                </h2>
                <div className="h-1 w-24 bg-gold-500 rounded mx-auto mb-6" style={{ backgroundColor: '#D4A017' }}></div>
                <p className="text-lg max-w-3xl mx-auto text-gray-600 leading-relaxed"
                   style={{ color: '#475569', fontSize: '1.125rem', lineHeight: '1.7' }}>
                  Explore the various ways to connect with fellow FGC Ikom alumni through
                  our organized chapters, graduation sets, exciting events, and shared memories.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                {highlightCards.map((card, index) => (
                  <div key={index} className="group">
                    <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
                         style={{
                           backgroundColor: '#ffffff',
                           borderRadius: '1rem',
                           boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                           border: '1px solid #f1f5f9'
                         }}>
                      <div className="w-16 h-16 bg-maroon-100 rounded-xl flex items-center justify-center mb-6 group-hover:bg-maroon-200 transition-colors duration-300"
                           style={{ backgroundColor: '#F5E6E8', borderRadius: '0.75rem' }}>
                        <div className="w-8 h-8 text-maroon-600" style={{ color: '#9E1F2C' }}>
                          {card.icon}
                        </div>
                      </div>
                      <h3 className="text-xl font-semibold text-maroon-800 mb-3"
                          style={{ color: '#7B0F17', fontSize: '1.25rem', fontWeight: '600' }}>
                        {card.title}
                      </h3>
                      <p className="text-gray-600 mb-4 leading-relaxed"
                         style={{ color: '#64748b', lineHeight: '1.6' }}>
                        {card.description}
                      </p>
                      <div className="text-3xl font-bold text-gold-600 mb-4"
                           style={{ color: '#B8900F', fontSize: '2rem', fontWeight: '700' }}>
                        {card.value}
                      </div>
                      {card.ctaLink && (
                        <a href={card.ctaLink}
                           className="inline-flex items-center text-maroon-600 hover:text-maroon-800 font-medium transition-colors duration-200"
                           style={{ color: '#9E1F2C', fontWeight: '500' }}>
                          {card.ctaText || 'Learn More'}
                          <ArrowRightIcon className="w-4 h-4 ml-2" />
                        </a>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Featured Updates Section */}
          <section className="py-24 bg-white relative" aria-labelledby="updates-heading">
            <div className="container mx-auto px-6 max-w-7xl">
              <div className="flex flex-col lg:flex-row lg:items-end lg:justify-between mb-16 gap-6">
                <div className="flex-1">
                  <h2 id="updates-heading" className="heading-2 text-maroon-800 mb-4"
                      style={{
                        color: '#7B0F17',
                        fontSize: 'clamp(2.5rem, 4vw, 3.5rem)',
                        fontFamily: 'var(--font-heading, serif)',
                        fontWeight: '600',
                        lineHeight: '1.2'
                      }}>
                    Latest Updates
                  </h2>
                  <div className="h-1 w-20 bg-gold-500 rounded mb-4" style={{ backgroundColor: '#D4A017' }}></div>
                  <p className="text-lg text-gray-600 max-w-2xl leading-relaxed"
                     style={{ color: '#64748b', fontSize: '1.125rem', lineHeight: '1.7' }}>
                    Stay informed with the latest news, events, and achievements from our global alumni community
                  </p>
                </div>
                <Link href="/events">
                  <Button
                    variant="outline"
                    size="lg"
                    className="px-8 py-3 border-2 border-maroon-600 text-maroon-600 hover:bg-maroon-600 hover:text-white transition-all duration-300"
                    style={{
                      borderColor: '#9E1F2C',
                      color: '#9E1F2C',
                      padding: '0.75rem 2rem',
                      fontSize: '1rem',
                      fontWeight: '500',
                      borderRadius: '0.75rem'
                    }}
                  >
                    View All Events
                    <ArrowRightIcon className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {announcements.slice(0, 3).map((announcement, index) => (
                  <article key={index} className="group">
                    <div className="bg-gray-50 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1"
                         style={{
                           backgroundColor: '#f8fafc',
                           borderRadius: '1rem',
                           boxShadow: '0 4px 20px rgba(0,0,0,0.08)'
                         }}>
                      <div className="p-8">
                        <div className="flex items-center justify-between mb-4">
                          <span className="inline-block px-3 py-1 bg-maroon-100 text-maroon-700 rounded-full text-sm font-medium"
                                style={{ backgroundColor: '#F5E6E8', color: '#7B0F17', fontSize: '0.875rem' }}>
                            {announcement.category}
                          </span>
                          <time className="text-sm text-gray-500" style={{ color: '#64748b' }}>
                            {new Date(announcement.date).toLocaleDateString()}
                          </time>
                        </div>
                        <h3 className="text-xl font-semibold text-maroon-800 mb-3 group-hover:text-maroon-900 transition-colors duration-200"
                            style={{ color: '#7B0F17', fontSize: '1.25rem', fontWeight: '600', lineHeight: '1.3' }}>
                          {announcement.title}
                        </h3>
                        <p className="text-gray-600 leading-relaxed mb-4"
                           style={{ color: '#64748b', lineHeight: '1.6' }}>
                          {announcement.excerpt}
                        </p>
                        <a href="#" className="inline-flex items-center text-gold-600 hover:text-gold-700 font-medium transition-colors duration-200"
                           style={{ color: '#B8900F', fontWeight: '500' }}>
                          Read More
                          <ArrowRightIcon className="w-4 h-4 ml-2" />
                        </a>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </section>

          {/* Chapters & Sets Preview */}
          <section className="py-16 bg-gray-50" aria-labelledby="directories-heading">
            <div className="container-custom">
              <div className="text-center mb-12">
                <h2 id="directories-heading" className="heading-2 text-maroon-800 text-3xl sm:text-4xl mb-4">
                  Chapters & Sets
                </h2>
                <div className="h-1 w-16 bg-gold-500 rounded mx-auto mt-1.5"></div>

                <p className="body-large max-w-2xl mx-auto text-gray-600">
                  Find your local chapter and connect with your graduation set.
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="card-base p-6 border border-gray-200 rounded-lg">
                  <h3 className="heading-3 mb-2">Explore Chapters</h3>
                  <p className="body-base text-gray-600 mb-4">Browse alumni chapters by region and join your local community.</p>
                  <Link href="/chapters"><Button variant="primary" size="sm">View Chapters</Button></Link>
                </div>
                <div className="card-base p-6 border border-gray-200 rounded-lg">
                  <h3 className="heading-3 mb-2">Discover Sets</h3>
                  <p className="body-base text-gray-600 mb-4">Reconnect with classmates and year groups across decades.</p>
                  <Link href="/sets"><Button variant="primary" size="sm">View Sets</Button></Link>
                </div>
              </div>
            </div>
          </section>

          {/* Gallery Strip */}
          <section className="py-16 bg-white" aria-labelledby="gallery-strip-heading">
            <div className="container-custom">
              <div className="flex items-end justify-between mb-6">
                <h2 id="gallery-strip-heading" className="heading-2 text-maroon-800 text-3xl">From the Gallery</h2>
                  <div className="h-1 w-12 bg-gold-500 rounded mt-1.5"></div>

                <Link href="/gallery" className="link-accessible">View all photos</Link>
              </div>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
                {[1,2,3,4,5,6].map((i) => (
                  <a key={i} href="/gallery" className="group block overflow-hidden rounded-md aspect-photo bg-gray-100">
                    <img
                      src={`https://images.unsplash.com/photo-15${20+i}3050854058-8df90110c9f1?auto=format&fit=crop&w=600&q=60`}
                      alt="Alumni moment"
                      className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                      loading="lazy"
                    />
                  </a>
                ))}
              </div>
            </div>
          </section>

          {/* Impact Metrics */}
          <section className="py-16 bg-gray-50" aria-labelledby="metrics-heading">
            <div className="container-custom">
              <div className="text-center mb-10">
                <h2 id="metrics-heading" className="heading-2 text-maroon-800 text-3xl sm:text-4xl mb-4">Our Impact</h2>
                  <div className="h-1 w-16 bg-gold-500 rounded mx-auto mt-1.5"></div>

                <p className="body-base text-gray-600 max-w-2xl mx-auto">Celebrating achievements and community support across the years.</p>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {[
                  { label: 'Active Chapters', value: '12+' },
                  { label: 'Graduation Sets', value: '45+' },
                  { label: 'Scholarships Awarded', value: '500+' },
                  { label: 'Photos Shared', value: '2.5K+' },
                ].map(({label, value}) => (
                  <div key={label} className="text-center card-base p-6 border border-gray-200 rounded-lg">
                    <div className="text-3xl font-bold text-maroon-700 mb-1">{value}</div>
                    <div className="body-small text-gray-600">{label}</div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Call to Action Section */}
          <section className="py-24 bg-gradient-to-r from-maroon-800 to-maroon-700 text-white relative overflow-hidden"
                   aria-labelledby="cta-heading"
                   style={{
                     background: 'linear-gradient(135deg, #5C0B12 0%, #7B0F17 100%)'
                   }}>
            {/* Decorative elements */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 right-20 w-40 h-40 border border-gold-300 rounded-full" style={{ borderColor: '#F0D898' }}></div>
              <div className="absolute bottom-20 left-20 w-32 h-32 bg-gold-500 rounded-full opacity-20" style={{ backgroundColor: '#D4A017' }}></div>
            </div>

            <div className="container mx-auto px-6 text-center relative z-10 max-w-5xl">
              <h2 id="cta-heading" className="heading-2 mb-6 text-white"
                  style={{
                    color: '#ffffff',
                    fontSize: 'clamp(2.5rem, 4vw, 3.5rem)',
                    fontFamily: 'var(--font-heading, serif)',
                    fontWeight: '600',
                    lineHeight: '1.2'
                  }}>
                Ready to Get Involved?
              </h2>
              <p className="text-xl mb-12 text-gray-100 max-w-3xl mx-auto leading-relaxed"
                 style={{
                   color: '#f3f4f6',
                   fontSize: '1.25rem',
                   lineHeight: '1.7'
                 }}>
                Whether you're looking to reconnect with old friends, give back to your alma mater,
                or stay updated with alumni activities, there's a place for you in our community.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <Link href="/chapters" className="w-full sm:w-auto">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="px-10 py-4 w-full sm:w-auto shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1"
                    style={{
                      backgroundColor: '#D4A017',
                      color: '#ffffff',
                      padding: '1rem 2.5rem',
                      fontSize: '1.125rem',
                      borderRadius: '0.75rem',
                      border: 'none',
                      fontWeight: '600',
                      boxShadow: '0 10px 25px rgba(0,0,0,0.2)'
                    }}
                  >
                    Join Your Chapter
                  </Button>
                </Link>
                <Link href="/contact" className="w-full sm:w-auto">
                  <Button
                    variant="outline"
                    size="lg"
                    className="px-10 py-4 w-full sm:w-auto border-2 border-white text-white hover:bg-white hover:text-maroon-700 transition-all duration-300"
                    style={{
                      borderColor: '#ffffff',
                      color: '#ffffff',
                      padding: '1rem 2.5rem',
                      fontSize: '1.125rem',
                      borderRadius: '0.75rem',
                      fontWeight: '600',
                      backgroundColor: 'transparent'
                    }}
                  >
                    Contact Us
                  </Button>
                </Link>
              </div>
            </div>
          </section>
        </div>
      </main>
    </>
  );
}