import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  createFieldValidator,
  createDebouncedValidator,
  validateFormData,
  getFieldValidationState,
  formatFieldName,
  getErrorSummary,
  isFormReadyForSubmission,
  a11yHelpers,
  performanceHelpers,
} from '../formValidationUtils';
import { contactFormSchema } from '@/lib/validation';
import { ContactFormData, FormErrors } from '@/lib/types';

describe('formValidationUtils', () => {
  describe('createFieldValidator', () => {
    it('creates a validator function for a specific field', () => {
      const nameValidator = createFieldValidator('name', contactFormSchema);
      
      expect(nameValidator('')).toContain('This field is required');
      expect(nameValidator('John Do<PERSON>')).toHaveLength(0);
    });

    it('returns empty array for non-existent field', () => {
      const validator = createFieldValidator('nonExistent', contactFormSchema);
      
      expect(validator('any value')).toHaveLength(0);
    });
  });

  describe('createDebouncedValidator', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('debounces validation calls', async () => {
      const mockValidator = vi.fn().mockReturnValue(['error']);
      const debouncedValidator = createDebouncedValidator(mockValidator, 300);
      
      // Call multiple times quickly
      debouncedValidator('value1');
      debouncedValidator('value2');
      debouncedValidator('value3');
      
      // Fast-forward time
      vi.advanceTimersByTime(300);
      
      // Should only be called once with the last value
      expect(mockValidator).toHaveBeenCalledTimes(1);
      expect(mockValidator).toHaveBeenCalledWith('value3');
    });
  });

  describe('validateFormData', () => {
    const formData: ContactFormData = {
      name: '',
      email: 'invalid-email',
      phone: '',
      subject: 'Valid subject',
      message: 'Valid message that is long enough',
      graduationYear: undefined,
      inquiryType: 'general',
      chapter: '',
    };

    it('validates form data and returns results', () => {
      const result = validateFormData(formData, contactFormSchema);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.name).toContain('This field is required');
      expect(result.errors.email).toContain('Please enter a valid email address');
      expect(result.fieldCount).toBeGreaterThan(0);
    });

    it('filters errors by touched fields when requested', () => {
      const touchedFields = { name: true, email: false };
      
      const result = validateFormData(formData, contactFormSchema, {
        touchedFields,
        onlyTouchedFields: true,
      });
      
      expect(result.errors.name).toBeDefined();
      expect(result.errors.email).toBeUndefined();
    });
  });

  describe('getFieldValidationState', () => {
    const errors: FormErrors = {
      name: ['Name is required'],
      email: ['Invalid email'],
    };
    const touched = { name: true, email: false };

    it('returns correct state for touched field with error', () => {
      const state = getFieldValidationState('name', errors, touched);
      
      expect(state.hasError).toBe(true);
      expect(state.errors).toEqual(['Name is required']);
      expect(state.touched).toBe(true);
      expect(state.isValid).toBe(false);
    });

    it('returns correct state for untouched field with error', () => {
      const state = getFieldValidationState('email', errors, touched);
      
      expect(state.hasError).toBe(false);
      expect(state.errors).toEqual(['Invalid email']);
      expect(state.touched).toBe(false);
      expect(state.isValid).toBe(false);
    });

    it('returns correct state for field without error', () => {
      const state = getFieldValidationState('subject', errors, touched);
      
      expect(state.hasError).toBe(false);
      expect(state.errors).toHaveLength(0);
      expect(state.touched).toBe(false);
      expect(state.isValid).toBe(false);
    });
  });

  describe('formatFieldName', () => {
    it('formats camelCase field names correctly', () => {
      expect(formatFieldName('firstName')).toBe('First name');
      expect(formatFieldName('graduationYear')).toBe('Graduation year');
      expect(formatFieldName('inquiryType')).toBe('Inquiry type');
    });

    it('handles single word field names', () => {
      expect(formatFieldName('name')).toBe('Name');
      expect(formatFieldName('email')).toBe('Email');
    });
  });

  describe('getErrorSummary', () => {
    const errors: FormErrors = {
      name: ['Name is required'],
      email: ['Email is required', 'Invalid email format'],
      phone: ['Invalid phone number'],
    };

    it('returns correct error summary', () => {
      const summary = getErrorSummary(errors);
      
      expect(summary.totalErrors).toBe(4);
      expect(summary.fieldCount).toBe(3);
      expect(summary.summary).toHaveLength(3);
      expect(summary.summary[0]).toEqual({
        field: 'Name',
        errors: ['Name is required'],
      });
    });
  });

  describe('isFormReadyForSubmission', () => {
    const formData: ContactFormData = {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '',
      subject: 'Test subject',
      message: 'Test message that is long enough',
      graduationYear: undefined,
      inquiryType: 'general',
      chapter: '',
    };

    it('returns ready when form is valid and all required fields touched', () => {
      const touched = {
        name: true,
        email: true,
        subject: true,
        message: true,
        inquiryType: true,
      };
      const requiredFields: (keyof ContactFormData)[] = ['name', 'email', 'subject', 'message', 'inquiryType'];
      
      const result = isFormReadyForSubmission(formData, contactFormSchema, touched, requiredFields);
      
      expect(result.isReady).toBe(true);
      expect(result.missingFields).toHaveLength(0);
      expect(result.hasErrors).toBe(false);
    });

    it('returns not ready when required fields are missing', () => {
      const touched = { name: true, email: true };
      const requiredFields: (keyof ContactFormData)[] = ['name', 'email', 'subject', 'message'];
      
      const result = isFormReadyForSubmission(formData, contactFormSchema, touched, requiredFields);
      
      expect(result.isReady).toBe(false);
      expect(result.missingFields).toContain('Subject');
      expect(result.missingFields).toContain('Message');
    });
  });

  describe('a11yHelpers', () => {
    describe('getFieldAriaAttributes', () => {
      it('returns correct attributes for field with error', () => {
        const attributes = a11yHelpers.getFieldAriaAttributes('name', true);
        
        expect(attributes['aria-invalid']).toBe('true');
        expect(attributes['aria-describedby']).toBe('name-error');
      });

      it('returns correct attributes for field with help text', () => {
        const attributes = a11yHelpers.getFieldAriaAttributes('name', false, 'Help text');
        
        expect(attributes['aria-describedby']).toBe('name-help');
      });

      it('combines error and help text attributes', () => {
        const attributes = a11yHelpers.getFieldAriaAttributes('name', true, 'Help text');
        
        expect(attributes['aria-invalid']).toBe('true');
        expect(attributes['aria-describedby']).toBe('name-error name-help');
      });
    });

    describe('generateValidationAnnouncement', () => {
      it('generates announcement for valid field', () => {
        const announcement = a11yHelpers.generateValidationAnnouncement('firstName', [], true);
        
        expect(announcement).toBe('First name is now valid');
      });

      it('generates announcement for single error', () => {
        const announcement = a11yHelpers.generateValidationAnnouncement('firstName', ['Required field'], false);
        
        expect(announcement).toBe('First name: Required field');
      });

      it('generates announcement for multiple errors', () => {
        const errors = ['Required field', 'Too short'];
        const announcement = a11yHelpers.generateValidationAnnouncement('firstName', errors, false);
        
        expect(announcement).toBe('First name has 2 errors: Required field, Too short');
      });
    });
  });

  describe('performanceHelpers', () => {
    describe('throttle', () => {
      beforeEach(() => {
        vi.useFakeTimers();
      });

      afterEach(() => {
        vi.useRealTimers();
      });

      it('throttles function calls', () => {
        const mockFn = vi.fn();
        const throttledFn = performanceHelpers.throttle(mockFn, 100);
        
        // Call multiple times
        throttledFn('arg1');
        throttledFn('arg2');
        throttledFn('arg3');
        
        // Should be called immediately for first call
        expect(mockFn).toHaveBeenCalledTimes(1);
        expect(mockFn).toHaveBeenCalledWith('arg1');
        
        // Fast-forward time
        vi.advanceTimersByTime(100);
        
        // Should be called again with last argument
        expect(mockFn).toHaveBeenCalledTimes(2);
        expect(mockFn).toHaveBeenLastCalledWith('arg3');
      });
    });

    describe('memoizeValidation', () => {
      it('memoizes validation results', () => {
        const mockValidator = vi.fn().mockReturnValue(['error']);
        const memoizedValidator = performanceHelpers.memoizeValidation(mockValidator);
        
        // Call with same value multiple times
        memoizedValidator('test');
        memoizedValidator('test');
        memoizedValidator('test');
        
        // Should only call original validator once
        expect(mockValidator).toHaveBeenCalledTimes(1);
      });

      it('calls validator for different values', () => {
        const mockValidator = vi.fn().mockReturnValue(['error']);
        const memoizedValidator = performanceHelpers.memoizeValidation(mockValidator);
        
        memoizedValidator('test1');
        memoizedValidator('test2');
        
        expect(mockValidator).toHaveBeenCalledTimes(2);
      });
    });
  });
});