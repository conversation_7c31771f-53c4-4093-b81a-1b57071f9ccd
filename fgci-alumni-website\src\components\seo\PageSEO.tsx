import { Metadata } from 'next';
import { 
  generateTitle, 
  generateCanonicalUrl, 
  generateOGImageUrl,
  generateRobotsContent,
  generateKeywords,
  SITE_CONFIG,
  DEFAULT_SEO,
  type SEOConfig 
} from '@/lib/seo';

interface PageSEOProps extends Partial<SEOConfig> {
  pathname: string;
  alternates?: {
    canonical?: string;
    languages?: Record<string, string>;
  };
}

export function generatePageMetadata({
  title,
  description = DEFAULT_SEO.description,
  keywords = [],
  canonical,
  ogImage,
  ogType = 'website',
  twitterCard = 'summary_large_image',
  noindex = false,
  nofollow = false,
  pathname,
  alternates,
  structuredData
}: PageSEOProps): Metadata {
  const pageTitle = generateTitle(title);
  const canonicalUrl = canonical || generateCanonicalUrl(pathname);
  const ogImageUrl = ogImage || generateOGImageUrl(title, description);
  const keywordsString = generateKeywords(keywords);
  const robotsContent = generateRobotsContent(noindex, nofollow);

  return {
    title: pageTitle,
    description,
    keywords: keywordsString,
    authors: [{ name: SITE_CONFIG.name }],
    creator: SITE_CONFIG.name,
    publisher: SITE_CONFIG.name,
    robots: robotsContent,
    alternates: {
      canonical: canonicalUrl,
      ...alternates,
    },
    openGraph: {
      type: ogType,
      locale: 'en_US',
      url: canonicalUrl,
      siteName: SITE_CONFIG.name,
      title: pageTitle,
      description,
      images: [
        {
          url: ogImageUrl,
          width: 1200,
          height: 630,
          alt: title || SITE_CONFIG.name,
        },
      ],
    },
    twitter: {
      card: twitterCard,
      site: SITE_CONFIG.social.twitter,
      creator: SITE_CONFIG.social.twitter,
      title: pageTitle,
      description,
      images: [ogImageUrl],
    },
    other: {
      'theme-color': '#7B0F17',
      'color-scheme': 'light',
      'format-detection': 'telephone=no',
    },
  };
}

// Helper function for chapter pages
export function generateChapterMetadata(chapter: {
  name: string;
  description?: string;
  region?: string;
  slug: string;
}): Metadata {
  const title = `${chapter.name} - Alumni Chapter`;
  const description = chapter.description || 
    `Connect with ${chapter.name} alumni chapter. Find executive committee members, activities, and events in ${chapter.region || 'your area'}.`;
  
  return generatePageMetadata({
    title,
    description,
    keywords: ['chapter', chapter.name, chapter.region, 'alumni', 'executive', 'activities'].filter(Boolean),
    pathname: `/chapters/${chapter.slug}`,
    ogImage: generateOGImageUrl(title, description),
    ogType: 'article',
  });
}

// Helper function for set pages
export function generateSetMetadata(set: {
  name: string;
  year: number;
  motto?: string;
  slug: string;
}): Metadata {
  const title = `${set.name} (${set.year}) - Graduation Set`;
  const description = set.motto ? 
    `${set.name} graduated in ${set.year}. Motto: "${set.motto}". Connect with classmates and view set activities.` :
    `${set.name} graduated in ${set.year}. Connect with classmates and view set activities.`;
  
  return generatePageMetadata({
    title,
    description,
    keywords: ['graduation', 'set', set.year.toString(), 'classmates', 'reunion', set.name],
    pathname: `/sets/${set.slug}`,
    ogImage: generateOGImageUrl(title, description),
    ogType: 'article',
  });
}

// Helper function for event pages
export function generateEventMetadata(event: {
  title: string;
  description: string;
  date: string;
  location: string;
  slug: string;
}): Metadata {
  const eventDate = new Date(event.date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  
  const title = `${event.title} - Alumni Event`;
  const description = `${event.description} Join us on ${eventDate} at ${event.location}.`;
  
  return generatePageMetadata({
    title,
    description,
    keywords: ['event', 'alumni', event.location, 'reunion', 'networking'],
    pathname: `/events/${event.slug}`,
    ogImage: generateOGImageUrl(title, description),
    ogType: 'article',
  });
}

export default generatePageMetadata;