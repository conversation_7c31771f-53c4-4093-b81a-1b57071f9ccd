import '@testing-library/jest-dom';
import { expect, vi, beforeAll, afterAll } from 'vitest';
// Provide Jest API alias for tests written with Jest globals
// @ts-expect-error assigning jest global for compatibility
(globalThis as any).jest = { ...vi, fn: vi.fn, spyOn: vi.spyOn, mock: vi.mock };
import { toHaveNoViolations } from 'jest-axe';

// Extend Vitest's expect with jest-axe matchers
expect.extend(toHaveNoViolations);

// Provide a default mock for api-client for tests that require it via require()
vi.mock(
  '@/lib/api-client',
  () => {
    const post = vi.fn();
    const get = vi.fn();
    const put = vi.fn();
    const patch = vi.fn();
    const del = vi.fn();
    return {
      apiClient: { post, get, put, patch, delete: del },
    };
  },
  { eager: true, virtual: true }
);

// Shim CommonJS require for specific aliases used in some tests
const __nativeRequire = (globalThis as any).require as any;
const __apiMock = { apiClient: { post: vi.fn(), get: vi.fn(), put: vi.fn(), patch: vi.fn(), delete: vi.fn() } };
if (typeof __nativeRequire === 'function') {
  (globalThis as any).require = new Proxy(__nativeRequire, {
    apply(target, thisArg, argArray: any[]) {
      const id = argArray?.[0];
      if (id === '@/lib/api-client') return __apiMock;
      return target.apply(thisArg, argArray as any);
    },
  });
} else {
  // Define a minimal global require to satisfy tests that call require('@/lib/api-client')
  (globalThis as any).require = (id: string) => {
    if (id === '@/lib/api-client') return __apiMock;
    throw new Error(`Cannot find module '${id}'. Use ESM import or add a mock.`);
  };
}


// Start MSW server for Node test environment
import { setupMockServer } from '@/mocks/server';
setupMockServer();

// Mock Next.js router (default). Test files can override with their own jest.mock('next/navigation', ...)
import React from 'react';

vi.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: vi.fn(),
      replace: vi.fn(),
      prefetch: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
    };
  },
  usePathname: vi.fn(() => '/'),
  useSearchParams() {
    return new URLSearchParams();
  },
}));

// Mock Next.js image: simulate load/error to help tests


// Bridge custom navigation events from components to test mocks of next/navigation
if (typeof window !== 'undefined') {
  window.addEventListener('next:push', async (e: any) => {
    try {
      // Import the (possibly mocked) next/navigation module and call its router.push
      const mod: any = await import('next/navigation');
      const router = mod.useRouter?.();
      router?.push?.(e?.detail);
    } catch {
      // no-op
    }
  });
}

// Mock Next.js image: simulate load/error to help tests
vi.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    const { src, onLoad, onError, ...rest } = props || {};
    React.useEffect(() => {
      const id = setTimeout(() => {
        if (typeof src === 'string' && src.includes('invalid')) {
          onError?.(new Event('error') as any);
        } else {
          onLoad?.(new Event('load') as any);
        }
      }, 0);
      return () => clearTimeout(id);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [src]);
    return React.createElement('img', { src, onLoad, onError, ...rest });
  },
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock Web Animations API
global.Element.prototype.animate = vi.fn().mockImplementation(() => ({
  cancel: vi.fn(),
  finish: vi.fn(),
  pause: vi.fn(),
  play: vi.fn(),
  reverse: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
}));

// Mock performance API
Object.defineProperty(global, 'performance', {
  writable: true,
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
    memory: {
      usedJSHeapSize: 1024 * 1024 * 10, // 10MB
      totalJSHeapSize: 1024 * 1024 * 50, // 50MB
      jsHeapSizeLimit: 1024 * 1024 * 100, // 100MB
    },
  },
});

// Mock PerformanceObserver
global.PerformanceObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
}));

// Suppress console warnings in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});