'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { generateBreadcrumbStructuredData, type BreadcrumbItem } from '@/lib/seo';

interface BreadcrumbsProps {
  items?: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
}

// Default breadcrumb mappings
const BREADCRUMB_MAPPINGS: Record<string, string> = {
  '/': 'Home',
  '/chapters': 'Chapters',
  '/sets': 'Sets',
  '/events': 'Events',
  '/gallery': 'Gallery',
  '/excobot': 'Alumni Directory',
  '/roll-of-honour': 'Roll of Honour',
  '/chapter-excos': 'Chapter Executives',
  '/contact': 'Contact Us',
  '/accessibility-test': 'Accessibility Test',
  '/performance-test': 'Performance Test',
  '/animations-test': 'Animations Test'
};

export function Breadcrumbs({ 
  items, 
  className,
  showHome = true 
}: BreadcrumbsProps) {
  const pathname = usePathname();

  // Generate breadcrumbs from pathname if items not provided
  const breadcrumbs = items || generateBreadcrumbsFromPath(pathname);

  // Don't show breadcrumbs on home page
  if (pathname === '/' && !items) {
    return null;
  }

  // Add structured data
  const structuredData = generateBreadcrumbStructuredData(breadcrumbs);

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />

      {/* Breadcrumb Navigation */}
      <nav 
        aria-label="Breadcrumb"
        className={cn('flex items-center space-x-2 text-sm', className)}
      >
        <ol className="flex items-center space-x-2">
          {showHome && (
            <li>
              <Link
                href="/"
                className="flex items-center text-gray-500 hover:text-gray-700 transition-colors"
                aria-label="Home"
              >
                <HomeIcon className="w-4 h-4" />
              </Link>
            </li>
          )}

          {breadcrumbs.map((item, index) => {
            const isLast = index === breadcrumbs.length - 1;
            
            return (
              <li key={item.url} className="flex items-center">
                {(showHome || index > 0) && (
                  <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-2" />
                )}
                
                {isLast ? (
                  <span 
                    className="text-gray-900 font-medium"
                    aria-current="page"
                  >
                    {item.name}
                  </span>
                ) : (
                  <Link
                    href={item.url}
                    className="text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    {item.name}
                  </Link>
                )}
              </li>
            );
          })}
        </ol>
      </nav>
    </>
  );
}

// Generate breadcrumbs from pathname
function generateBreadcrumbsFromPath(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];

  let currentPath = '';
  
  segments.forEach((segment) => {
    currentPath += `/${segment}`;
    
    // Handle dynamic routes
    let name = BREADCRUMB_MAPPINGS[currentPath];
    
    if (!name) {
      // Handle dynamic segments like [slug]
      if (segment.length > 0) {
        // Convert kebab-case to title case
        name = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      } else {
        name = segment;
      }
    }

    breadcrumbs.push({
      name,
      url: currentPath
    });
  });

  return breadcrumbs;
}

export default Breadcrumbs;
