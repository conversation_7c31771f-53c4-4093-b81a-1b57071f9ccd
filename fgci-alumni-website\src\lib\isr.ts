// ISR (Incremental Static Regeneration) configuration utilities

export interface ISRConfig {
  revalidate: number | false;
  tags?: string[];
}

// ISR revalidation times (in seconds)
export const REVALIDATION_TIMES = {
  // Static content that rarely changes
  STATIC: 86400, // 24 hours
  
  // Content that changes occasionally
  WEEKLY: 604800, // 7 days
  DAILY: 86400, // 24 hours
  HOURLY: 3600, // 1 hour
  
  // Dynamic content that changes frequently
  FREQUENT: 1800, // 30 minutes
  REALTIME: 60, // 1 minute
  
  // Never revalidate (pure static)
  NEVER: false,
} as const;

// Page-specific ISR configurations
export const PAGE_ISR_CONFIG: Record<string, ISRConfig> = {
  // Static pages (SSG)
  '/': {
    revalidate: REVALIDATION_TIMES.DAILY,
    tags: ['homepage', 'announcements']
  },
  '/contact': {
    revalidate: REVALIDATION_TIMES.STATIC,
    tags: ['contact']
  },
  
  // Index pages (ISR)
  '/chapters': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['chapters', 'chapters-index']
  },
  '/sets': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['sets', 'sets-index']
  },
  '/events': {
    revalidate: REVALIDATION_TIMES.DAILY,
    tags: ['events', 'events-index']
  },
  '/gallery': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['gallery', 'gallery-index']
  },
  
  // Directory pages (ISR)
  '/excobot': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['alumni', 'directory']
  },
  '/chapter-excos': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['executives', 'chapters']
  },
  '/roll-of-honour': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['memorial', 'roll-of-honour']
  },
  
  // Dynamic detail pages (ISR)
  '/chapters/[slug]': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['chapter-detail', 'chapters']
  },
  '/sets/[slug]': {
    revalidate: REVALIDATION_TIMES.WEEKLY,
    tags: ['set-detail', 'sets']
  },
  '/events/[slug]': {
    revalidate: REVALIDATION_TIMES.DAILY,
    tags: ['event-detail', 'events']
  },
};

// Get ISR config for a specific page
export function getISRConfig(pathname: string): ISRConfig {
  // Try exact match first
  if (PAGE_ISR_CONFIG[pathname]) {
    return PAGE_ISR_CONFIG[pathname];
  }
  
  // Try pattern matching for dynamic routes
  for (const [pattern, config] of Object.entries(PAGE_ISR_CONFIG)) {
    if (pattern.includes('[') && matchesPattern(pathname, pattern)) {
      return config;
    }
  }
  
  // Default configuration
  return {
    revalidate: REVALIDATION_TIMES.DAILY,
    tags: ['default']
  };
}

// Check if pathname matches a dynamic route pattern
function matchesPattern(pathname: string, pattern: string): boolean {
  const patternParts = pattern.split('/');
  const pathnameParts = pathname.split('/');
  
  if (patternParts.length !== pathnameParts.length) {
    return false;
  }
  
  return patternParts.every((part, index) => {
    if (part.startsWith('[') && part.endsWith(']')) {
      return true; // Dynamic segment matches anything
    }
    return part === pathnameParts[index];
  });
}

// Generate static params for dynamic routes (used in generateStaticParams)
export async function generateChapterStaticParams() {
  // In production, this would fetch from your API
  // For now, return a subset of chapters for initial static generation
  return [
    { slug: 'lagos-chapter' },
    { slug: 'abuja-chapter' },
    { slug: 'port-harcourt-chapter' },
    { slug: 'kano-chapter' },
    { slug: 'ibadan-chapter' },
  ];
}

export async function generateSetStaticParams() {
  // In production, this would fetch from your API
  // Generate recent sets for initial static generation
  const currentYear = new Date().getFullYear();
  const recentYears = [];
  
  // Generate last 10 years of sets
  for (let i = 0; i < 10; i++) {
    const year = currentYear - 6 - i; // Assuming 6 years for completion
    recentYears.push({ slug: `class-of-${year}` });
  }
  
  return recentYears;
}

export async function generateEventStaticParams() {
  // In production, this would fetch from your API
  return [
    { slug: 'annual-reunion-2024' },
    { slug: 'homecoming-2024' },
    { slug: 'scholarship-fundraiser-2024' },
  ];
}

// Cache tags for revalidation
export const CACHE_TAGS = {
  // Global tags
  ALL: 'all',
  HOMEPAGE: 'homepage',
  
  // Content type tags
  CHAPTERS: 'chapters',
  SETS: 'sets',
  EVENTS: 'events',
  GALLERY: 'gallery',
  ALUMNI: 'alumni',
  EXECUTIVES: 'executives',
  MEMORIAL: 'memorial',
  
  // Specific page tags
  CHAPTERS_INDEX: 'chapters-index',
  SETS_INDEX: 'sets-index',
  EVENTS_INDEX: 'events-index',
  GALLERY_INDEX: 'gallery-index',
  
  // Detail page tags
  CHAPTER_DETAIL: 'chapter-detail',
  SET_DETAIL: 'set-detail',
  EVENT_DETAIL: 'event-detail',
} as const;

// Utility to revalidate specific tags (for use in API routes)
export function getRevalidationTags(contentType: string, action: 'create' | 'update' | 'delete'): string[] {
  const tags: string[] = [CACHE_TAGS.ALL];
  
  switch (contentType) {
    case 'chapter':
      tags.push(CACHE_TAGS.CHAPTERS, CACHE_TAGS.CHAPTERS_INDEX);
      if (action !== 'create') {
        tags.push(CACHE_TAGS.CHAPTER_DETAIL);
      }
      break;
      
    case 'set':
      tags.push(CACHE_TAGS.SETS, CACHE_TAGS.SETS_INDEX);
      if (action !== 'create') {
        tags.push(CACHE_TAGS.SET_DETAIL);
      }
      break;
      
    case 'event':
      tags.push(CACHE_TAGS.EVENTS, CACHE_TAGS.EVENTS_INDEX);
      if (action !== 'create') {
        tags.push(CACHE_TAGS.EVENT_DETAIL);
      }
      break;
      
    case 'gallery':
      tags.push(CACHE_TAGS.GALLERY, CACHE_TAGS.GALLERY_INDEX);
      break;
      
    case 'alumni':
      tags.push(CACHE_TAGS.ALUMNI, CACHE_TAGS.EXECUTIVES);
      break;
      
    default:
      break;
  }
  
  return tags;
}