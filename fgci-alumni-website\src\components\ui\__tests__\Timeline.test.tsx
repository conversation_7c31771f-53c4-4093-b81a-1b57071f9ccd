import { render, screen, fireEvent } from '@/lib/test-utils';
import { Timeline } from '../Timeline';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

const mockActivities = [
  {
    id: '1',
    date: '2024-01-15',
    title: 'Chapter Meeting',
    description: 'Monthly chapter meeting with all members.',
    type: 'meeting' as const,
    location: 'Lagos Office',
  },
  {
    id: '2',
    date: '2024-02-20',
    title: 'Fundraising Event',
    description: 'Annual fundraising gala for scholarship fund.',
    type: 'fundraiser' as const,
    location: 'Eko Hotel',
    photos: ['photo1.jpg', 'photo2.jpg'],
  },
  {
    id: '3',
    date: '2024-03-10',
    title: 'Social Gathering',
    description: 'Informal networking event for alumni.',
    type: 'social' as const,
    location: 'Victoria Island',
  },
];

describe('Timeline Component', () => {
  it('renders activities in chronological order', () => {
    render(<Timeline activities={mockActivities} />);
    
    expect(screen.getByText('Chapter Meeting')).toBeInTheDocument();
    expect(screen.getByText('Fundraising Event')).toBeInTheDocument();
    expect(screen.getByText('Social Gathering')).toBeInTheDocument();
  });

  it('renders with vertical layout by default', () => {
    render(<Timeline activities={mockActivities} />);
    
    const timeline = screen.getByTestId('timeline');
    expect(timeline).toHaveClass('timeline-vertical');
  });

  it('renders with horizontal layout when specified', () => {
    render(<Timeline activities={mockActivities} variant="horizontal" />);
    
    const timeline = screen.getByTestId('timeline');
    expect(timeline).toHaveClass('timeline-horizontal');
  });

  it('displays dates when showDates is true', () => {
    render(<Timeline activities={mockActivities} showDates />);
    
    expect(screen.getByText('Jan 15, 2024')).toBeInTheDocument();
    expect(screen.getByText('Feb 20, 2024')).toBeInTheDocument();
    expect(screen.getByText('Mar 10, 2024')).toBeInTheDocument();
  });

  it('hides dates when showDates is false', () => {
    render(<Timeline activities={mockActivities} showDates={false} />);
    
    expect(screen.queryByText('Jan 15, 2024')).not.toBeInTheDocument();
    expect(screen.queryByText('Feb 20, 2024')).not.toBeInTheDocument();
    expect(screen.queryByText('Mar 10, 2024')).not.toBeInTheDocument();
  });

  it('renders activity types with appropriate icons', () => {
    render(<Timeline activities={mockActivities} />);
    
    const meetingIcon = screen.getByTestId('activity-icon-meeting');
    const fundraiserIcon = screen.getByTestId('activity-icon-fundraiser');
    const socialIcon = screen.getByTestId('activity-icon-social');
    
    expect(meetingIcon).toBeInTheDocument();
    expect(fundraiserIcon).toBeInTheDocument();
    expect(socialIcon).toBeInTheDocument();
  });

  it('handles interactive timeline items', () => {
    const onActivityClick = jest.fn();
    render(
      <Timeline
        activities={mockActivities}
        interactive
        onActivityClick={onActivityClick}
      />
    );
    
    const firstActivity = screen.getByText('Chapter Meeting').closest('div');
    fireEvent.click(firstActivity!);
    
    expect(onActivityClick).toHaveBeenCalledWith(mockActivities[0]);
  });

  it('renders photo indicators when activities have photos', () => {
    render(<Timeline activities={mockActivities} />);
    
    const photoIndicator = screen.getByTestId('photo-indicator');
    expect(photoIndicator).toBeInTheDocument();
    expect(photoIndicator).toHaveTextContent('2 photos');
  });

  it('renders location information', () => {
    render(<Timeline activities={mockActivities} />);
    
    expect(screen.getByText('Lagos Office')).toBeInTheDocument();
    expect(screen.getByText('Eko Hotel')).toBeInTheDocument();
    expect(screen.getByText('Victoria Island')).toBeInTheDocument();
  });

  it('handles empty activities array', () => {
    render(<Timeline activities={[]} />);
    
    expect(screen.getByText('No activities to display')).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    render(<Timeline activities={mockActivities} className="custom-timeline" />);
    
    const timeline = screen.getByTestId('timeline');
    expect(timeline).toHaveClass('custom-timeline');
  });

  it('supports keyboard navigation when interactive', () => {
    const onActivityClick = jest.fn();
    render(
      <Timeline
        activities={mockActivities}
        interactive
        onActivityClick={onActivityClick}
      />
    );
    
    const firstActivity = screen.getByText('Chapter Meeting').closest('div');
    
    // Should be focusable
    expect(firstActivity).toHaveAttribute('tabIndex', '0');
    
    // Should handle Enter key
    fireEvent.keyDown(firstActivity!, { key: 'Enter' });
    expect(onActivityClick).toHaveBeenCalledWith(mockActivities[0]);
    
    // Should handle Space key
    fireEvent.keyDown(firstActivity!, { key: ' ' });
    expect(onActivityClick).toHaveBeenCalledTimes(2);
  });

  it('renders activity descriptions with proper truncation', () => {
    const longActivity = {
      ...mockActivities[0],
      description: 'This is a very long description that should be truncated when it exceeds the maximum length limit set for timeline items to maintain visual consistency.',
    };
    
    render(<Timeline activities={[longActivity]} />);
    
    const description = screen.getByText(/This is a very long description/);
    expect(description).toBeInTheDocument();
  });

  it('groups activities by date when specified', () => {
    const sameDate = '2024-01-15';
    const groupedActivities = [
      { ...mockActivities[0], date: sameDate },
      { ...mockActivities[1], date: sameDate, title: 'Second Event' },
    ];
    
    render(<Timeline activities={groupedActivities} groupByDate />);
    
    const dateHeaders = screen.getAllByText('Jan 15, 2024');
    expect(dateHeaders).toHaveLength(1); // Should only show date once
    
    expect(screen.getByText('Chapter Meeting')).toBeInTheDocument();
    expect(screen.getByText('Second Event')).toBeInTheDocument();
  });

  it('has proper semantic structure', () => {
    render(<Timeline activities={mockActivities} />);
    
    const timeline = screen.getByRole('list');
    expect(timeline).toBeInTheDocument();
    
    const timelineItems = screen.getAllByRole('listitem');
    expect(timelineItems).toHaveLength(mockActivities.length);
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<Timeline activities={mockActivities} />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('has no accessibility violations when interactive', async () => {
    const { container } = render(
      <Timeline
        activities={mockActivities}
        interactive
        onActivityClick={jest.fn()}
      />
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('renders with loading state', () => {
    render(<Timeline activities={[]} loading />);
    
    expect(screen.getByTestId('timeline-skeleton')).toBeInTheDocument();
  });

  it('renders with error state', () => {
    render(<Timeline activities={[]} error="Failed to load activities" />);
    
    expect(screen.getByText('Failed to load activities')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /retry/i })).toBeInTheDocument();
  });

  it('handles retry action in error state', () => {
    const onRetry = jest.fn();
    render(
      <Timeline
        activities={[]}
        error="Failed to load activities"
        onRetry={onRetry}
      />
    );
    
    const retryButton = screen.getByRole('button', { name: /retry/i });
    fireEvent.click(retryButton);
    
    expect(onRetry).toHaveBeenCalledTimes(1);
  });
});