'use client';

import { useState, useEffect } from 'react';
import { X, Filter } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { ExcoBotFilters } from '@/lib/types';

interface AlumniFiltersProps {
  filters: ExcoBotFilters;
  onFiltersChange: (filters: ExcoBotFilters) => void;
  onClose: () => void;
}

// Filter options based on mock data
const chapterOptions = [
  'Lagos Chapter',
  'Abuja Chapter',
  'UK Diaspora Chapter',
  'USA Diaspora Chapter',
  'Port Harcourt Chapter',
  'Enugu Chapter',
  'Kano Chapter',
  'Kaduna Chapter',
  'Ibadan Chapter',
];

const graduationYearOptions = [
  2005, 2006, 2007, 2008, 2009, 2010, 2011, 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019, 2020
];

const roleOptions = [
  'President',
  'Vice President',
  'Secretary',
  'Treasurer',
  'Set Leader',
  'Medical Director',
  'Senior Software Engineer',
  'Legal Counsel',
  'Financial Analyst',
  'Education Coordinator',
  'Agricultural Engineer',
  'Nurse Practitioner',
  'Architect',
  'Marketing Manager',
  'Mechanical Engineer',
];

const locationOptions = [
  'Lagos, Nigeria',
  'Abuja, Nigeria',
  'London, UK',
  'Manchester, UK',
  'Houston, TX, USA',
  'Atlanta, GA, USA',
  'San Francisco, CA, USA',
  'Port Harcourt, Nigeria',
  'Enugu, Nigeria',
  'Kano, Nigeria',
  'Kaduna, Nigeria',
  'Ibadan, Nigeria',
];

export function AlumniFilters({ filters, onFiltersChange, onClose }: AlumniFiltersProps) {
  const [localFilters, setLocalFilters] = useState<ExcoBotFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof ExcoBotFilters, value: string | number | undefined) => {
    const newFilters = {
      ...localFilters,
      [key]: value === '' || value === 'any' ? undefined : value,
    };
    setLocalFilters(newFilters);
  };

  const applyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const clearFilters = () => {
    const clearedFilters: ExcoBotFilters = {};
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = Object.values(localFilters).some(value => 
    value !== undefined && value !== '' && value !== 'any'
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-maroon-600" />
          <h3 className="text-lg font-semibold text-gray-900">Filter Alumni</h3>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors"
          aria-label="Close filters"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Filter Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Chapter Filter */}
        <div className="space-y-2">
          <label htmlFor="chapter-filter" className="block text-sm font-medium text-gray-700">
            Chapter
          </label>
          <select
            id="chapter-filter"
            value={localFilters.chapter || ''}
            onChange={(e) => handleFilterChange('chapter', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
          >
            <option value="">All Chapters</option>
            {chapterOptions.map((chapter) => (
              <option key={chapter} value={chapter}>
                {chapter}
              </option>
            ))}
          </select>
        </div>

        {/* Graduation Year Filter */}
        <div className="space-y-2">
          <label htmlFor="year-filter" className="block text-sm font-medium text-gray-700">
            Graduation Year
          </label>
          <select
            id="year-filter"
            value={localFilters.graduationYear || ''}
            onChange={(e) => handleFilterChange('graduationYear', e.target.value ? parseInt(e.target.value) : undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
          >
            <option value="">All Years</option>
            {graduationYearOptions.sort((a, b) => b - a).map((year) => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
        </div>

        {/* Role Filter */}
        <div className="space-y-2">
          <label htmlFor="role-filter" className="block text-sm font-medium text-gray-700">
            Current Role
          </label>
          <select
            id="role-filter"
            value={localFilters.currentRole || ''}
            onChange={(e) => handleFilterChange('currentRole', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
          >
            <option value="">All Roles</option>
            {roleOptions.map((role) => (
              <option key={role} value={role}>
                {role}
              </option>
            ))}
          </select>
        </div>

        {/* Location Filter */}
        <div className="space-y-2">
          <label htmlFor="location-filter" className="block text-sm font-medium text-gray-700">
            Current Location
          </label>
          <select
            id="location-filter"
            value={localFilters.location || ''}
            onChange={(e) => handleFilterChange('location', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
          >
            <option value="">All Locations</option>
            {locationOptions.map((location) => (
              <option key={location} value={location}>
                {location}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="border-t pt-6">
        <h4 className="text-md font-medium text-gray-900 mb-4">Advanced Filters</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Position Type Filter */}
          <div className="space-y-2">
            <label htmlFor="position-filter" className="block text-sm font-medium text-gray-700">
              Position Type
            </label>
            <select
              id="position-filter"
              value={localFilters.position || ''}
              onChange={(e) => handleFilterChange('position', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
            >
              <option value="">All Positions</option>
              <option value="executive">Executive Committee</option>
              <option value="leader">Set Leader</option>
              <option value="professional">Professional</option>
              <option value="academic">Academic</option>
            </select>
          </div>

          {/* Availability Filter */}
          <div className="space-y-2">
            <label htmlFor="availability-filter" className="block text-sm font-medium text-gray-700">
              Availability
            </label>
            <select
              id="availability-filter"
              value={localFilters.availability || 'any'}
              onChange={(e) => handleFilterChange('availability', e.target.value as 'available' | 'busy' | 'any')}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500"
            >
              <option value="any">Any</option>
              <option value="available">Available for Contact</option>
              <option value="busy">Limited Availability</option>
            </select>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t">
        <Button
          variant="primary"
          onClick={applyFilters}
          className="flex-1 sm:flex-none"
        >
          Apply Filters
        </Button>
        
        <Button
          variant="outline"
          onClick={clearFilters}
          disabled={!hasActiveFilters}
          className="flex-1 sm:flex-none"
        >
          Clear All
        </Button>
        
        <Button
          variant="ghost"
          onClick={onClose}
          className="flex-1 sm:flex-none"
        >
          Cancel
        </Button>
      </div>

      {/* Filter Summary */}
      {hasActiveFilters && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="text-sm font-medium text-gray-700 mb-2">Active Filters:</h5>
          <div className="flex flex-wrap gap-2">
            {Object.entries(localFilters).map(([key, value]) => {
              if (!value || value === 'any') return null;
              return (
                <span
                  key={key}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-white border border-gray-200 rounded text-xs text-gray-700"
                >
                  <span className="font-medium capitalize">{key}:</span>
                  <span>{value}</span>
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}