'use client';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { Modal } from '@/components/ui';
import { 
  ChevronLeftIcon, 
  ChevronRightIcon, 
  XMarkIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import type { GalleryImage } from '@/lib/types';

interface LightboxProps {
  images: GalleryImage[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
}

export function Lightbox({
  images,
  currentIndex,
  isOpen,
  onClose,
  onNext,
  onPrevious
}: LightboxProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const currentImage = images[currentIndex];

  // Enhanced keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isOpen) return;

    switch (event.key) {
      case 'Escape':
        // Let Modal handle Escape to avoid double-calling onClose in tests
        event.preventDefault();
        break;
      case 'ArrowLeft':
        event.preventDefault();
        onPrevious();
        break;
      case 'ArrowRight':
        event.preventDefault();
        onNext();
        break;
      case 'ArrowUp':
        event.preventDefault();
        onPrevious();
        break;
      case 'ArrowDown':
        event.preventDefault();
        onNext();
        break;
      case 'f':
      case 'F':
        event.preventDefault();
        setIsFullscreen(!isFullscreen);
        break;
      case 'Home':
        event.preventDefault();
        // Navigate to first image
        const firstDiff = 0 - currentIndex;
        if (firstDiff < 0) {
          for (let i = 0; i < Math.abs(firstDiff); i++) onPrevious();
        } else if (firstDiff > 0) {
          for (let i = 0; i < firstDiff; i++) onNext();
        }
        break;
      case 'End':
        event.preventDefault();
        // Navigate to last image
        const lastDiff = (images.length - 1) - currentIndex;
        if (lastDiff < 0) {
          for (let i = 0; i < Math.abs(lastDiff); i++) onPrevious();
        } else if (lastDiff > 0) {
          for (let i = 0; i < lastDiff; i++) onNext();
        }
        break;
      case ' ':
        event.preventDefault();
        // Space bar can be used for next image
        onNext();
        break;
    }
  }, [isOpen, onClose, onNext, onPrevious, isFullscreen, currentIndex, images.length]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  // Reset image loaded state when image changes
  useEffect(() => {
    setImageLoaded(false);
  }, [currentIndex]);

  // Prevent body scroll when lightbox is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!currentImage) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="fullscreen"
      closeOnOverlayClick={true}
      className="bg-black/95 backdrop-blur-sm"
    >
      <div className="relative w-full h-full flex items-center justify-center p-4">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-50 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
          aria-label="Close lightbox"
        >
          <XMarkIcon className="w-6 h-6" />
        </button>

        {/* Fullscreen toggle */}
        <button
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="absolute top-4 right-16 z-50 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
          aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? (
            <ArrowsPointingInIcon className="w-6 h-6" />
          ) : (
            <ArrowsPointingOutIcon className="w-6 h-6" />
          )}
        </button>

        {/* Previous button */}
        {images.length > 1 && (
          <button
            onClick={onPrevious}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-50 p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            aria-label="Previous image"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>
        )}

        {/* Next button */}
        {images.length > 1 && (
          <button
            onClick={onNext}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-50 p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            aria-label="Next image"
          >
            <ChevronRightIcon className="w-6 h-6" />
          </button>
        )}

        {/* Image container with smooth animations */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentImage.id}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className={cn(
              "relative flex items-center justify-center",
              isFullscreen ? "w-full h-full" : "max-w-[90vw] max-h-[80vh]"
            )}
          >
            {/* Loading spinner */}
            {!imageLoaded && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
              </div>
            )}

            {/* Main image with Next.js optimization */}
            <div className="relative w-full h-full">
              <Image
                data-testid="next-image"
                src={currentImage.url}
                alt={currentImage.alt}
                fill
                sizes="100vw"
                className={cn(
                  "object-contain transition-opacity duration-300",
                  imageLoaded ? "opacity-100" : "opacity-0"
                )}
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageLoaded(true)}
                priority={true}
                quality={95}
              />
            </div>
          </motion.div>
        </AnimatePresence>

        {/* Image info */}
        <div className="absolute bottom-4 left-4 right-4 z-50">
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-lg">
                {currentImage.caption || `Image ${currentIndex + 1}`}
              </h3>
              <span className="text-sm text-gray-300">
                {currentIndex + 1} of {images.length}
              </span>
            </div>
            
            {currentImage.photographer && (
              <p className="text-sm text-gray-300">
                Photo by {currentImage.photographer}
              </p>
            )}
            
            {currentImage.date && (
              <p className="text-sm text-gray-300">
                {new Date(currentImage.date).toLocaleDateString()}
              </p>
            )}
          </div>
        </div>

        {/* Thumbnail strip */}
        {images.length > 1 && images.length <= 20 && (
          <div className="absolute bottom-20 left-1/2 -translate-x-1/2 z-50">
            <div className="flex space-x-2 bg-black/50 backdrop-blur-sm rounded-lg p-2 max-w-md overflow-x-auto scrollbar-hide">
              {images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => {
                    // Navigate to specific image
                    const diff = index - currentIndex;
                    if (diff > 0) {
                      for (let i = 0; i < diff; i++) onNext();
                    } else if (diff < 0) {
                      for (let i = 0; i < Math.abs(diff); i++) onPrevious();
                    }
                  }}
                  className={cn(
                    "flex-shrink-0 w-12 h-12 rounded overflow-hidden border-2 transition-all relative",
                    index === currentIndex 
                      ? "border-white scale-110" 
                      : "border-transparent hover:border-gray-400"
                  )}
                >
                  <Image
                    src={image.thumbnailUrl || image.url}
                    alt={image.alt}
                    fill
                    sizes="48px"
                    className="object-cover"
                    quality={50}
                  />
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Enhanced keyboard shortcuts help */}
        <div className="absolute top-4 left-4 z-50 text-white text-sm bg-black/50 backdrop-blur-sm rounded-lg p-3">
          <div className="space-y-1">
            <div>← → Navigate</div>
            <div>Space Next</div>
            <div>Home/End First/Last</div>
            <div>ESC Close</div>
            <div>F Fullscreen</div>
          </div>
        </div>
      </div>
    </Modal>
  );
}