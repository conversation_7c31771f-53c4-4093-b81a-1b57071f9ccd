import { Set, Person, Activity, Document } from '../../lib/types';

// Mock set leaders and executive members
const set2015Exco: Person[] = [
  {
    id: 'set15-1',
    name: '<PERSON><PERSON>',
    role: 'Set Leader',
    phone: '+234-************',
    email: '<EMAIL>',
    photo: '/images/people/chioma-okafor.jpg',
    graduationYear: 2015,
    currentLocation: 'Lagos, Nigeria',
    bio: 'Marketing executive and set coordinator.',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/chioma-okafor'
    }
  },
  {
    id: 'set15-2',
    name: '<PERSON>',
    role: 'Assistant Set Leader',
    phone: '+234-************',
    email: '<EMAIL>',
    photo: '/images/people/david-nnaji.jpg',
    graduationYear: 2015,
    currentLocation: 'Abuja, Nigeria',
    bio: 'Civil engineer and project manager.',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/david-nnaji'
    }
  }
];

const set2014Exco: Person[] = [
  {
    id: 'set14-1',
    name: '<PERSON>',
    role: 'Set Leader',
    phone: '+234-************',
    email: '<EMAIL>',
    photo: '/images/people/grace-udoh.jpg',
    graduationYear: 2014,
    currentLocation: 'Port Harcourt, Nigeria',
    bio: 'Banking professional and community organizer.',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/grace-udoh'
    }
  }
];

const set2013Exco: Person[] = [
  {
    id: 'set13-1',
    name: 'Michael Obi',
    role: 'Set Leader',
    phone: '+234-************',
    email: '<EMAIL>',
    photo: '/images/people/michael-obi.jpg',
    graduationYear: 2013,
    currentLocation: 'Enugu, Nigeria',
    bio: 'Software developer and tech entrepreneur.',
    socialLinks: {
      linkedin: 'https://linkedin.com/in/michael-obi',
      twitter: 'https://twitter.com/michael_obi'
    }
  }
];

// Mock set activities
const set2015Activities: Activity[] = [
  {
    id: 'set15-act-1',
    date: '2024-02-10',
    title: 'Class of 2015 Reunion Planning',
    description: 'Planning meeting for the upcoming 10-year reunion celebration.',
    location: 'Online (Google Meet)',
    type: 'meeting'
  },
  {
    id: 'set15-act-2',
    date: '2024-01-20',
    title: 'New Year Hangout',
    description: 'Informal gathering to catch up and share updates from the past year.',
    photos: ['/images/activities/set2015-hangout-1.jpg', '/images/activities/set2015-hangout-2.jpg'],
    location: 'Lagos Island',
    type: 'social'
  }
];

const set2014Activities: Activity[] = [
  {
    id: 'set14-act-1',
    date: '2024-03-05',
    title: 'Career Development Workshop',
    description: 'Workshop on professional development and career advancement strategies.',
    location: 'Port Harcourt Business Hub',
    type: 'event'
  }
];

const set2013Activities: Activity[] = [
  {
    id: 'set13-act-1',
    date: '2024-02-28',
    title: 'Tech Meetup',
    description: 'Networking event for set members working in technology and innovation.',
    photos: ['/images/activities/set2013-tech-meetup.jpg'],
    location: 'Enugu Tech Hub',
    type: 'event'
  }
];

// Mock documents
const mockDocuments: Document[] = [
  {
    id: 'doc-1',
    title: 'Class of 2015 Directory',
    description: 'Complete contact directory of all Class of 2015 members',
    url: '/documents/class-2015-directory.pdf',
    type: 'pdf',
    size: 2048576, // 2MB
    uploadDate: '2024-01-15'
  },
  {
    id: 'doc-2',
    title: 'Reunion Planning Guide',
    description: 'Guidelines and checklist for organizing class reunions',
    url: '/documents/reunion-planning-guide.pdf',
    type: 'pdf',
    size: 1536000, // 1.5MB
    uploadDate: '2024-02-01'
  }
];

// Mock sets data
export const mockSets: Set[] = [
  {
    id: 'class-2015',
    slug: 'class-2015',
    name: 'Class of 2015',
    year: 2015,
    motto: 'Excellence in Unity',
    description: 'The Class of 2015 represents a dynamic group of alumni who have excelled in various fields including technology, business, healthcare, and public service. Known for their collaborative spirit and commitment to excellence.',
    heroImage: '/images/sets/class-2015-hero.jpg',
    leader: set2015Exco[0],
    exco: set2015Exco,
    activities: set2015Activities,
    gallery: [
      '/images/gallery/class-2015-graduation.jpg',
      '/images/gallery/class-2015-reunion-2020.jpg',
      '/images/gallery/class-2015-hangout-2024.jpg'
    ],
    documents: mockDocuments,
    memberCount: 89
  },
  {
    id: 'class-2014',
    slug: 'class-2014',
    name: 'Class of 2014',
    year: 2014,
    motto: 'Leadership Through Service',
    description: 'The Class of 2014 is distinguished by their commitment to community service and leadership. Many members have taken on significant roles in their respective communities and professional fields.',
    heroImage: '/images/sets/class-2014-hero.jpg',
    leader: set2014Exco[0],
    exco: set2014Exco,
    activities: set2014Activities,
    gallery: [
      '/images/gallery/class-2014-graduation.jpg',
      '/images/gallery/class-2014-service-project.jpg'
    ],
    documents: [
      {
        id: 'doc-3',
        title: 'Class of 2014 Yearbook',
        description: 'Digital yearbook with photos and memories from graduation year',
        url: '/documents/class-2014-yearbook.pdf',
        type: 'pdf',
        size: 5242880, // 5MB
        uploadDate: '2024-01-10'
      }
    ],
    memberCount: 76
  },
  {
    id: 'class-2013',
    slug: 'class-2013',
    name: 'Class of 2013',
    year: 2013,
    motto: 'Innovation and Progress',
    description: 'The Class of 2013 has been at the forefront of technological innovation and entrepreneurship. Many members have started successful businesses and contributed to the digital transformation of Nigeria.',
    heroImage: '/images/sets/class-2013-hero.jpg',
    leader: set2013Exco[0],
    exco: set2013Exco,
    activities: set2013Activities,
    gallery: [
      '/images/gallery/class-2013-graduation.jpg',
      '/images/gallery/class-2013-tech-conference.jpg',
      '/images/gallery/class-2013-startup-pitch.jpg'
    ],
    documents: [
      {
        id: 'doc-4',
        title: 'Entrepreneurship Guide',
        description: 'Guide for starting and scaling businesses, compiled by class members',
        url: '/documents/entrepreneurship-guide.pdf',
        type: 'pdf',
        size: 3145728, // 3MB
        uploadDate: '2024-02-15'
      }
    ],
    memberCount: 82
  },
  {
    id: 'class-2012',
    slug: 'class-2012',
    name: 'Class of 2012',
    year: 2012,
    motto: 'Strength in Diversity',
    description: 'The Class of 2012 celebrates diversity and inclusion, with members spread across different continents and industries, yet maintaining strong bonds and regular communication.',
    heroImage: '/images/sets/class-2012-hero.jpg',
    leader: {
      id: 'set12-1',
      name: 'Amina Hassan',
      role: 'Set Leader',
      phone: '+234-************',
      email: '<EMAIL>',
      photo: '/images/people/amina-hassan.jpg',
      graduationYear: 2012,
      currentLocation: 'Kano, Nigeria',
      bio: 'International development specialist.',
      socialLinks: {
        linkedin: 'https://linkedin.com/in/amina-hassan'
      }
    },
    exco: [
      {
        id: 'set12-1',
        name: 'Amina Hassan',
        role: 'Set Leader',
        phone: '+234-************',
        email: '<EMAIL>',
        photo: '/images/people/amina-hassan.jpg',
        graduationYear: 2012,
        currentLocation: 'Kano, Nigeria',
        bio: 'International development specialist.',
        socialLinks: {
          linkedin: 'https://linkedin.com/in/amina-hassan'
        }
      }
    ],
    activities: [
      {
        id: 'set12-act-1',
        date: '2024-01-30',
        title: 'Global Virtual Meetup',
        description: 'Virtual reunion connecting members from different continents.',
        location: 'Online (Zoom)',
        type: 'social'
      }
    ],
    gallery: [
      '/images/gallery/class-2012-graduation.jpg',
      '/images/gallery/class-2012-global-meetup.jpg'
    ],
    documents: [],
    memberCount: 71
  },
  {
    id: 'class-2011',
    slug: 'class-2011',
    name: 'Class of 2011',
    year: 2011,
    motto: 'Pioneers of Change',
    description: 'The Class of 2011 were pioneers in many ways, being among the first to embrace social media for alumni networking and establishing many of the traditions that continue today.',
    heroImage: '/images/sets/class-2011-hero.jpg',
    leader: {
      id: 'set11-1',
      name: 'Tunde Adebayo',
      role: 'Set Leader',
      phone: '+234-************',
      email: '<EMAIL>',
      photo: '/images/people/tunde-adebayo.jpg',
      graduationYear: 2011,
      currentLocation: 'Ibadan, Nigeria',
      bio: 'Media and communications expert.',
      socialLinks: {
        linkedin: 'https://linkedin.com/in/tunde-adebayo',
        twitter: 'https://twitter.com/tunde_adebayo'
      }
    },
    exco: [
      {
        id: 'set11-1',
        name: 'Tunde Adebayo',
        role: 'Set Leader',
        phone: '+234-************',
        email: '<EMAIL>',
        photo: '/images/people/tunde-adebayo.jpg',
        graduationYear: 2011,
        currentLocation: 'Ibadan, Nigeria',
        bio: 'Media and communications expert.',
        socialLinks: {
          linkedin: 'https://linkedin.com/in/tunde-adebayo',
          twitter: 'https://twitter.com/tunde_adebayo'
        }
      }
    ],
    activities: [
      {
        id: 'set11-act-1',
        date: '2024-03-15',
        title: 'Alumni Mentorship Program Launch',
        description: 'Launch of mentorship program connecting recent graduates with experienced alumni.',
        location: 'University of Ibadan',
        type: 'event'
      }
    ],
    gallery: [
      '/images/gallery/class-2011-graduation.jpg',
      '/images/gallery/class-2011-mentorship-launch.jpg'
    ],
    documents: [
      {
        id: 'doc-5',
        title: 'Mentorship Program Guide',
        description: 'Comprehensive guide for the alumni mentorship program',
        url: '/documents/mentorship-program-guide.pdf',
        type: 'pdf',
        size: 2097152, // 2MB
        uploadDate: '2024-03-10'
      }
    ],
    memberCount: 68
  }
];