﻿import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { generateChapterMetadata } from '@/components/seo';
import { generateChapterStaticParams, REVALIDATION_TIMES } from '@/lib/isr';
import ChapterDetailPageClient from './ChapterDetailPageClient';

// Enable ISR with weekly revalidation
export const revalidate = REVALIDATION_TIMES.WEEKLY;

// Generate static params for popular chapters
export async function generateStaticParams() {
  return generateChapterStaticParams();
}

// Mock function to get chapter data for metadata (in real app, this would fetch from API)
async function getChapterForMetadata(slug: string) {
  // In a real application, you would fetch this from your API
  // For now, we'll return a mock chapter or null if not found
  const mockChapters = [
    {
      slug: 'lagos-chapter',
      name: 'Lagos Chapter',
      description: 'The Lagos Chapter serves alumni in Lagos State and surrounding areas, organizing networking events, professional development workshops, and community service activities.',
      region: 'Lagos State'
    },
    {
      slug: 'abuja-chapter', 
      name: 'Abuja Chapter',
      description: 'The Abuja Chapter connects alumni in the Federal Capital Territory, fostering professional relationships and organizing regular meetups and social events.',
      region: 'Federal Capital Territory'
    }
  ];
  
  return mockChapters.find(chapter => chapter.slug === slug) || null;
}

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const chapter = await getChapterForMetadata(params.slug);
  
  if (!chapter) {
    return {
      title: 'Chapter Not Found | FGC Ikom Alumni Association',
      description: 'The requested chapter could not be found.',
    };
  }

  return generateChapterMetadata(chapter);
}

export default function ChapterDetailPage({ params }: { params: { slug: string } }) {
  return <ChapterDetailPageClient slug={params.slug} />;
}

