'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/outline';
import { useKeyboardNavigation } from '@/hooks/useKeyboardNavigation';
import { cn } from '@/lib/utils';

interface CarouselItem {
  id: string;
  content: React.ReactNode;
  alt?: string;
}

interface KeyboardNavigableCarouselProps {
  items: CarouselItem[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showControls?: boolean;
  showIndicators?: boolean;
  className?: string;
  onSlideChange?: (index: number) => void;
  ariaLabel?: string;
}

/**
 * Keyboard navigable carousel component with full accessibility support
 * Supports arrow keys, space bar, and screen reader announcements
 */
export function KeyboardNavigableCarousel({
  items,
  autoPlay = false,
  autoPlayInterval = 5000,
  showControls = true,
  showIndicators = true,
  className,
  onSlideChange,
  ariaLabel = 'Image carousel'
}: KeyboardNavigableCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const carouselRef = useRef<HTMLDivElement>(null);
  const announcementRef = useRef<HTMLDivElement>(null);

  const goToNext = useCallback(() => {
    const nextIndex = (currentIndex + 1) % items.length;
    setCurrentIndex(nextIndex);
    onSlideChange?.(nextIndex);
    
    // Announce slide change to screen readers
    if (announcementRef.current) {
      announcementRef.current.textContent = `Slide ${nextIndex + 1} of ${items.length}`;
    }
  }, [currentIndex, items.length, onSlideChange]);

  const goToPrevious = useCallback(() => {
    const prevIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    onSlideChange?.(prevIndex);
    
    // Announce slide change to screen readers
    if (announcementRef.current) {
      announcementRef.current.textContent = `Slide ${prevIndex + 1} of ${items.length}`;
    }
  }, [currentIndex, items.length, onSlideChange]);

  const goToSlide = useCallback((index: number) => {
    if (index >= 0 && index < items.length) {
      setCurrentIndex(index);
      onSlideChange?.(index);
      
      // Announce slide change to screen readers
      if (announcementRef.current) {
        announcementRef.current.textContent = `Slide ${index + 1} of ${items.length}`;
      }
    }
  }, [items.length, onSlideChange]);

  const togglePlayPause = useCallback(() => {
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  // Keyboard navigation
  useKeyboardNavigation({
    onArrowLeft: goToPrevious,
    onArrowRight: goToNext,
    onSpace: togglePlayPause,
    onHome: () => goToSlide(0),
    onEnd: () => goToSlide(items.length - 1),
    customKeys: {
      'p': togglePlayPause,
      'P': togglePlayPause
    },
    enabled: isFocused,
    preventDefault: ['ArrowLeft', 'ArrowRight', ' ', 'Home', 'End', 'p', 'P']
  });

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && !isHovered && !isFocused) {
      intervalRef.current = setInterval(goToNext, autoPlayInterval);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPlaying, isHovered, isFocused, goToNext, autoPlayInterval]);

  // Pause on hover or focus for accessibility
  const handleMouseEnter = () => setIsHovered(true);
  const handleMouseLeave = () => setIsHovered(false);
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  if (items.length === 0) return null;

  return (
    <div
      ref={carouselRef}
      className={cn(
        'relative overflow-hidden rounded-lg bg-gray-100 carousel-keyboard-nav',
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onFocus={handleFocus}
      onBlur={handleBlur}
      tabIndex={0}
      role="region"
      aria-label={ariaLabel}
      aria-live="polite"
      aria-atomic="true"
    >
      {/* Screen reader announcements */}
      <div
        ref={announcementRef}
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
      />

      {/* Carousel content */}
      <div className="relative aspect-video w-full">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -300 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="absolute inset-0"
          >
            {items[currentIndex].content}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation controls */}
      {showControls && items.length > 1 && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-2 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 transition-colors"
            aria-label="Previous slide"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>

          <button
            onClick={goToNext}
            className="absolute right-2 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 transition-colors"
            aria-label="Next slide"
          >
            <ChevronRightIcon className="w-6 h-6" />
          </button>

          {/* Play/Pause button */}
          {autoPlay && (
            <button
              onClick={togglePlayPause}
              className="absolute top-2 right-2 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 transition-colors"
              aria-label={isPlaying ? 'Pause slideshow' : 'Play slideshow'}
            >
              {isPlaying ? (
                <PauseIcon className="w-5 h-5" />
              ) : (
                <PlayIcon className="w-5 h-5" />
              )}
            </button>
          )}
        </>
      )}

      {/* Slide indicators */}
      {showIndicators && items.length > 1 && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-10">
          <div className="flex space-x-2" role="tablist" aria-label="Slide navigation">
            {items.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cn(
                  'w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2',
                  index === currentIndex
                    ? 'bg-white scale-125'
                    : 'bg-white/50 hover:bg-white/75'
                )}
                role="tab"
                aria-selected={index === currentIndex}
                aria-label={`Go to slide ${index + 1}`}
                tabIndex={index === currentIndex ? 0 : -1}
              />
            ))}
          </div>
        </div>
      )}

      {/* Keyboard shortcuts help */}
      <div className="absolute top-2 left-2 z-10 opacity-0 focus-within:opacity-100 hover:opacity-100 transition-opacity">
        <div className="bg-black/75 text-white text-xs rounded px-2 py-1 space-y-1">
          <div>← → Navigate</div>
          <div>Space Play/Pause</div>
          <div>Home/End First/Last</div>
        </div>
      </div>

      {/* Current slide info for screen readers */}
      <div className="sr-only">
        Slide {currentIndex + 1} of {items.length}
        {items[currentIndex].alt && `: ${items[currentIndex].alt}`}
      </div>
    </div>
  );
}