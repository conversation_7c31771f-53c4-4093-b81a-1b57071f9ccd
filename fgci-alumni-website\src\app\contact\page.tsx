﻿import { Metadata } from 'next';
import { generatePageMetadata } from '@/components/seo';
import { REVALIDATION_TIMES } from '@/lib/isr';
import ContactPageClient from './ContactPageClient';

export const metadata: Metadata = generatePageMetadata({
  title: 'Contact Us',
  description: 'Get in touch with the FGC Ikom Alumni Association. Contact us for membership, events, or general inquiries.',
  keywords: ['contact', 'support', 'membership', 'inquiries', 'help', 'alumni association'],
  pathname: '/contact',
  ogType: 'website',
});

// Static generation - contact page rarely changes
export const revalidate = REVALIDATION_TIMES.STATIC;

export default function ContactPage() {
  return <ContactPageClient />;
}
