'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  InformationCircleIcon,
  XCircleIcon 
} from '@heroicons/react/24/outline';

interface FormMessageProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  className?: string;
  onDismiss?: () => void;
}

const messageConfig = {
  success: {
    icon: CheckCircleIcon,
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    iconColor: 'text-green-600',
    titleColor: 'text-green-800',
    textColor: 'text-green-700',
  },
  error: {
    icon: XCircleIcon,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    iconColor: 'text-red-600',
    titleColor: 'text-red-800',
    textColor: 'text-red-700',
  },
  warning: {
    icon: ExclamationTriangleIcon,
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    iconColor: 'text-yellow-600',
    titleColor: 'text-yellow-800',
    textColor: 'text-yellow-700',
  },
  info: {
    icon: InformationCircleIcon,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    iconColor: 'text-blue-600',
    titleColor: 'text-blue-800',
    textColor: 'text-blue-700',
  },
};

export function FormMessage({
  type,
  title,
  message,
  className,
  onDismiss,
}: FormMessageProps) {
  const config = messageConfig[type];
  const Icon = config.icon;

  return (
    <div 
      className={cn(
        'p-4 border rounded-lg flex items-start',
        config.bgColor,
        config.borderColor,
        className
      )}
      role={type === 'error' ? 'alert' : 'status'}
      aria-live={type === 'error' ? 'assertive' : 'polite'}
    >
      <Icon className={cn('w-5 h-5 mr-3 mt-0.5 flex-shrink-0', config.iconColor)} />
      <div className="flex-1">
        {title && (
          <h3 className={cn('font-medium', config.titleColor)}>
            {title}
          </h3>
        )}
        <p className={cn(title ? 'text-sm mt-1' : 'text-sm', config.textColor)}>
          {message}
        </p>
      </div>
      {onDismiss && (
        <button
          type="button"
          onClick={onDismiss}
          className={cn(
            'ml-3 flex-shrink-0 p-1 rounded-md hover:bg-black/5 focus:outline-none focus:ring-2 focus:ring-maroon-500',
            config.iconColor
          )}
          aria-label="Dismiss message"
        >
          <XCircleIcon className="w-4 h-4" />
        </button>
      )}
    </div>
  );
}