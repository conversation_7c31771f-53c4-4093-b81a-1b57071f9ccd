import { describe, it, expect, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useForm } from '../useForm';
import { contactFormSchema } from '@/lib/validation';
import { ContactFormData } from '@/lib/types';

describe('useForm', () => {
  const initialData: ContactFormData = {
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    graduationYear: undefined,
    inquiryType: '',
    chapter: '',
  };

  let mockOnSubmit: jest.Mock;

  beforeEach(() => {
    mockOnSubmit = jest.fn();
  });

  it('initializes with correct default values', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    expect(result.current.data).toEqual(initialData);
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
    expect(result.current.isValid).toBe(false);
  });

  it('handles field changes correctly', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    act(() => {
      result.current.handleChange({
        target: { name: 'name', value: 'John Doe', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.data.name).toBe('John Doe');
  });

  it('handles number input changes correctly', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    act(() => {
      result.current.handleChange({
        target: { name: 'graduationYear', value: '1995', type: 'number' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.data.graduationYear).toBe(1995);
  });

  it('handles checkbox changes correctly', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData: { ...initialData, acceptTerms: false },
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    act(() => {
      result.current.handleChange({
        target: { name: 'acceptTerms', checked: true, type: 'checkbox' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.data.acceptTerms).toBe(true);
  });

  it('validates fields on blur', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    act(() => {
      result.current.handleBlur({
        target: { name: 'name', value: '' },
      } as React.FocusEvent<HTMLInputElement>);
    });

    expect(result.current.touched.name).toBe(true);
    expect(result.current.errors.name).toContain('This field is required');
  });

  it('clears errors when user starts typing', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    // First trigger an error
    act(() => {
      result.current.handleBlur({
        target: { name: 'name', value: '' },
      } as React.FocusEvent<HTMLInputElement>);
    });

    expect(result.current.errors.name).toContain('This field is required');

    // Then start typing to clear the error
    act(() => {
      result.current.handleChange({
        target: { name: 'name', value: 'J', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.errors.name).toBeUndefined();
  });

  it('sets field values programmatically', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    act(() => {
      result.current.setFieldValue('name', 'John Doe');
    });

    expect(result.current.data.name).toBe('John Doe');
  });

  it('sets field errors programmatically', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    act(() => {
      result.current.setFieldError('name', 'Custom error');
    });

    expect(result.current.errors.name).toEqual(['Custom error']);
  });

  it('resets form to initial state', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    // Make some changes
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldError('email', 'Some error');
    });

    expect(result.current.data.name).toBe('John Doe');
    expect(result.current.errors.email).toEqual(['Some error']);

    // Reset form
    act(() => {
      result.current.reset();
    });

    expect(result.current.data).toEqual(initialData);
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
  });

  it('provides correct field props', () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    const fieldProps = result.current.getFieldProps('name');

    expect(fieldProps).toEqual({
      name: 'name',
      value: '',
      onChange: expect.any(Function),
      onBlur: expect.any(Function),
    });
  });

  it('validates form before submission', async () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    const mockEvent = {
      preventDefault: jest.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(mockOnSubmit).not.toHaveBeenCalled();
    expect(result.current.errors.name).toContain('This field is required');
  });

  it('submits form when validation passes', async () => {
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: mockOnSubmit,
      })
    );

    // Fill in required fields
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldValue('email', '<EMAIL>');
      result.current.setFieldValue('subject', 'Test Subject');
      result.current.setFieldValue('message', 'This is a test message that is long enough');
      result.current.setFieldValue('inquiryType', 'general');
    });

    const mockEvent = {
      preventDefault: jest.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(mockOnSubmit).toHaveBeenCalledWith({
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '',
      subject: 'Test Subject',
      message: 'This is a test message that is long enough',
      graduationYear: undefined,
      inquiryType: 'general',
      chapter: '',
    });
  });

  it('handles submission errors gracefully', async () => {
    const errorOnSubmit = jest.fn().mockRejectedValue(new Error('Submission failed'));
    
    const { result } = renderHook(() =>
      useForm({
        initialData,
        validationSchema: contactFormSchema,
        onSubmit: errorOnSubmit,
      })
    );

    // Fill in required fields
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldValue('email', '<EMAIL>');
      result.current.setFieldValue('subject', 'Test Subject');
      result.current.setFieldValue('message', 'This is a test message that is long enough');
      result.current.setFieldValue('inquiryType', 'general');
    });

    const mockEvent = {
      preventDefault: jest.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    expect(result.current.isSubmitting).toBe(false);
  });
});