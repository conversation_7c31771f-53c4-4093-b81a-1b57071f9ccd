'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAnimation } from '@/components/providers/AnimationProvider';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';
import { alternativeFeedback } from '@/utils/animationUtils';

interface AnimationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AnimationSettings({ isOpen, onClose }: AnimationSettingsProps) {
  const { prefersReducedMotion, disableAnimations, setDisableAnimations } = useAnimation();
  const [testAnimations, setTestAnimations] = useState(false);

  const handleToggleAnimations = () => {
    const newValue = !disableAnimations;
    setDisableAnimations(newValue);
    
    // Provide alternative feedback
    alternativeFeedback.announce(
      newValue ? 'Animations disabled' : 'Animations enabled'
    );
    
    // Provide haptic feedback if available
    alternativeFeedback.haptic('light');
  };

  const handleTestAnimations = () => {
    setTestAnimations(true);
    
    // Provide feedback
    alternativeFeedback.announce('Testing animation preferences');
    
    setTimeout(() => {
      setTestAnimations(false);
    }, 2000);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Animation Preferences"
      size="md"
    >
      <div className="space-y-6">
        {/* Current Status */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-900 mb-2">Current Settings</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>System Preference:</span>
              <span className={prefersReducedMotion ? 'text-orange-600' : 'text-green-600'}>
                {prefersReducedMotion ? 'Reduced Motion' : 'Full Motion'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Manual Override:</span>
              <span className={disableAnimations ? 'text-orange-600' : 'text-green-600'}>
                {disableAnimations ? 'Disabled' : 'Enabled'}
              </span>
            </div>
            <div className="flex justify-between font-medium">
              <span>Effective Setting:</span>
              <span className={(prefersReducedMotion || disableAnimations) ? 'text-orange-600' : 'text-green-600'}>
                {(prefersReducedMotion || disableAnimations) ? 'Reduced Motion' : 'Full Motion'}
              </span>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="space-y-4">
          <div>
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={disableAnimations}
                onChange={handleToggleAnimations}
                className="w-4 h-4 text-maroon-600 border-gray-300 rounded focus:ring-gold-500"
              />
              <span className="text-sm font-medium text-gray-900">
                Disable all animations manually
              </span>
            </label>
            <p className="text-xs text-gray-600 mt-1 ml-7">
              Override system settings to disable animations on this site
            </p>
          </div>

          {/* Test Animation */}
          <div className="border-t pt-4">
            <Button
              onClick={handleTestAnimations}
              variant="outline"
              className="w-full"
            >
              Test Current Settings
            </Button>
            
            <AnimatePresence>
              {testAnimations && (
                <motion.div
                  className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: prefersReducedMotion || disableAnimations ? 0.1 : 0.3 }}
                >
                  <div className="flex items-center space-x-2">
                    <motion.div
                      className="w-4 h-4 bg-blue-500 rounded-full"
                      animate={
                        prefersReducedMotion || disableAnimations
                          ? {}
                          : {
                              scale: [1, 1.2, 1],
                              rotate: [0, 180, 360],
                            }
                      }
                      transition={{
                        duration: prefersReducedMotion || disableAnimations ? 0 : 1,
                        repeat: prefersReducedMotion || disableAnimations ? 0 : Infinity,
                      }}
                    />
                    <span className="text-sm text-blue-800">
                      {(prefersReducedMotion || disableAnimations)
                        ? 'Animations are disabled - you should see static content'
                        : 'Animations are enabled - you should see smooth motion'
                      }
                    </span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Information */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">About Animation Settings</h4>
          <div className="text-sm text-blue-800 space-y-2">
            <p>
              We respect your system's motion preferences automatically. You can also manually 
              disable animations if needed.
            </p>
            <p>
              When animations are disabled, we provide alternative feedback through:
            </p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Color changes and visual indicators</li>
              <li>Pattern-based feedback</li>
              <li>Enhanced focus states</li>
              <li>Screen reader announcements</li>
            </ul>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
}

// Quick toggle component for header/settings
export function AnimationToggle() {
  const { prefersReducedMotion, disableAnimations, setDisableAnimations } = useAnimation();
  const [showSettings, setShowSettings] = useState(false);

  const isAnimationsDisabled = prefersReducedMotion || disableAnimations;

  return (
    <>
      <button
        onClick={() => setShowSettings(true)}
        className="p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-gold-500 rounded"
        aria-label="Animation settings"
        title={`Animations are currently ${isAnimationsDisabled ? 'disabled' : 'enabled'}`}
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          {isAnimationsDisabled ? (
            // Pause icon for disabled animations
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          ) : (
            // Play icon for enabled animations
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"
            />
          )}
        </svg>
      </button>

      <AnimationSettings
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </>
  );
}