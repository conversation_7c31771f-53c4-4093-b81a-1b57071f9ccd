'use client';

import { useState, useMemo, useCallback } from 'react';
import { Search, Filter, X, Users, MapPin, GraduationCap, Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingStates } from '@/components/ui/LoadingStates';
import { AlumniCard } from './AlumniCard';
import { AlumniFilters } from './AlumniFilters';
import { useAlumniSearch } from '@/hooks/useAlumniSearch';
import { ExcoBotFilters } from '@/lib/types';

interface SortOption {
  value: string;
  label: string;
}

const sortOptions: SortOption[] = [
  { value: 'name', label: 'Name (A-Z)' },
  { value: 'name-desc', label: 'Name (Z-A)' },
  { value: 'year', label: 'Graduation Year (Recent)' },
  { value: 'year-desc', label: 'Graduation Year (Oldest)' },
  { value: 'chapter', label: 'Chapter (A-Z)' },
  { value: 'role', label: 'Role (A-Z)' },
];

// Helper component for filter chips
interface FilterChipProps {
  label: string;
  value: string;
  onRemove: () => void;
  variant: 'search' | 'filter';
}

function FilterChip({ label, value, onRemove, variant }: FilterChipProps) {
  const baseClasses = "inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium transition-colors";
  const variantClasses = variant === 'search'
    ? "bg-maroon-100 text-maroon-800 hover:bg-maroon-200"
    : "bg-gold-100 text-gold-800 hover:bg-gold-200";

  return (
    <span className={`${baseClasses} ${variantClasses}`}>
      <span className="font-normal">{label}:</span>
      <span>{value}</span>
      <button
        onClick={onRemove}
        className="hover:opacity-70 transition-opacity ml-1"
        aria-label={`Remove ${label} filter`}
      >
        <X className="w-3 h-3" />
      </button>
    </span>
  );
}

// Helper function to format filter labels
function formatFilterLabel(key: string): string {
  const labelMap: Record<string, string> = {
    chapter: 'Chapter',
    graduationYear: 'Year',
    currentRole: 'Role',
    location: 'Location',
    position: 'Position',
    availability: 'Availability',
  };
  return labelMap[key] || key;
}

export function ExcoBotSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ExcoBotFilters>({});
  const [sortBy, setSortBy] = useState('name');
  const [showFilters, setShowFilters] = useState(false);
  const [searchStarted, setSearchStarted] = useState(false);

  const { data: searchResults, isLoading, error } = useAlumniSearch({
    query: searchQuery,
    filters,
    sortBy,
    enabled: searchStarted || searchQuery.length > 0 || Object.keys(filters).length > 0,
  });

  // Real-time search with debouncing
  const handleSearchChange = useCallback((value: string) => {
    setSearchQuery(value);
    if (value.length > 0) {
      setSearchStarted(true);
    }
  }, []);

  const handleFilterChange = useCallback((newFilters: ExcoBotFilters) => {
    setFilters(newFilters);
    setSearchStarted(true);
  }, []);

  const clearAllFilters = useCallback(() => {
    setSearchQuery('');
    setFilters({});
    setSortBy('name');
    setSearchStarted(false);
  }, []);

  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value =>
      value !== undefined && value !== '' && value !== 'any'
    ).length;
  }, [filters]);

  const hasActiveSearch = searchQuery.length > 0 || activeFilterCount > 0;

  return (
    <div className="max-w-7xl mx-auto">
      {/* Search Header */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          <div className="flex-1 max-w-2xl">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search alumni by name, role, chapter, or location..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500 text-lg"
                aria-label="Search alumni directory"
              />
              {searchQuery && (
                <button
                  onClick={() => handleSearchChange('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  aria-label="Clear search"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>
          </div>

          <div className="flex gap-3 items-center">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
              icon={<Filter className="w-4 h-4" />}
            >
              Filters
              {activeFilterCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-gold-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {activeFilterCount}
                </span>
              )}
            </Button>

            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="appearance-none px-3 py-2 pr-8 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-gold-500 bg-white"
                aria-label="Sort results"
              >
                {sortOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveSearch && (
          <div className="mt-4 space-y-2">
            <div className="flex flex-wrap gap-2 items-center">
              <span className="text-sm text-gray-600 font-medium">Active filters:</span>
              {searchQuery && (
                <FilterChip
                  label="Search"
                  value={`"${searchQuery}"`}
                  onRemove={() => handleSearchChange('')}
                  variant="search"
                />
              )}
              {Object.entries(filters).map(([key, value]) => {
                if (!value || value === 'any') return null;
                return (
                  <FilterChip
                    key={key}
                    label={formatFilterLabel(key)}
                    value={String(value)}
                    onRemove={() => handleFilterChange({ ...filters, [key]: undefined })}
                    variant="filter"
                  />
                );
              })}
              {(searchQuery || activeFilterCount > 0) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-gray-600 hover:text-gray-800 ml-2"
                  icon={<X className="w-3 h-3" />}
                >
                  Clear all
                </Button>
              )}
            </div>

            {/* Quick Filter Suggestions */}
            {!searchQuery && activeFilterCount === 0 && (
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-xs text-gray-500">Quick filters:</span>
                <button
                  onClick={() => handleFilterChange({ ...filters, currentRole: 'President' })}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                >
                  Presidents
                </button>
                <button
                  onClick={() => handleFilterChange({ ...filters, chapter: 'Lagos Chapter' })}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                >
                  Lagos Chapter
                </button>
                <button
                  onClick={() => handleFilterChange({ ...filters, graduationYear: 2015 })}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                >
                  Class of 2015
                </button>
                <button
                  onClick={() => handleFilterChange({ ...filters, position: 'executive' })}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                >
                  Executives
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Quick Filter Suggestions (visible initially) */}
      {(!searchQuery && activeFilterCount === 0) && (
        <div className="mt-4 flex flex-wrap gap-2 items-center">
          <span className="text-xs text-gray-500">Quick filters:</span>
          <button
            onClick={() => handleFilterChange({ ...filters, currentRole: 'President' })}
            className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
          >
            Presidents
          </button>
          <button
            onClick={() => handleFilterChange({ ...filters, chapter: 'Lagos Chapter' })}
            className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
          >
            Lagos Chapter
          </button>
          <button
            onClick={() => handleFilterChange({ ...filters, graduationYear: 2015 })}
            className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
          >
            Class of 2015
          </button>
          <button
            onClick={() => handleFilterChange({ ...filters, position: 'executive' })}
            className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
          >
            Executives
          </button>
        </div>
      )}


      {/* Filters Panel */}
      {showFilters && (
        <Card variant="outlined" padding="lg" className="mb-8">
          <AlumniFilters
            filters={filters}
            onFiltersChange={handleFilterChange}
            onClose={() => setShowFilters(false)}
          />
        </Card>
      )}

      {/* Search Results */}
      <div className="space-y-6">
        {/* Results Header */}
        {searchStarted && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-gray-600">
              <Users className="w-5 h-5" />
              <span>
                {isLoading ? (
                  'Searching...'
                ) : searchResults ? (
                  `${searchResults.length} alumni found`
                ) : (
                  'No results'
                )}
              </span>
            </div>

            {searchResults && searchResults.length > 0 && (
              <div className="text-sm text-gray-500">
                Sorted by {sortOptions.find(opt => opt.value === sortBy)?.label}
              </div>
            )}
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, index) => (
              <LoadingStates.Card key={index} />
            ))}
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card variant="outlined" padding="lg" className="text-center">
            <div className="text-red-600 mb-4">
              <X className="w-12 h-12 mx-auto mb-2" />
              <h3 className="text-lg font-semibold">Search Error</h3>
              <p className="text-sm text-gray-600 mt-1">
                Unable to search alumni directory. Please try again.
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => window.location.reload()}
            >
              Retry Search
            </Button>
          </Card>
        )}

        {/* No Results */}
        {!isLoading && !error && searchStarted && (!searchResults || searchResults.length === 0) && (
          <Card variant="outlined" padding="lg" className="text-center">
            <div className="text-gray-500 mb-4">
              <Users className="w-12 h-12 mx-auto mb-2" />
              <h3 className="text-lg font-semibold">No Alumni Found</h3>
              <p className="text-sm mt-1">
                Try adjusting your search terms or filters to find more results.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 justify-center text-sm text-gray-600">
              <span>Try searching for:</span>
              <button
                onClick={() => handleSearchChange('President')}
                className="text-maroon-600 hover:text-maroon-800 underline"
              >
                "President"
              </button>
              <button
                onClick={() => handleSearchChange('Lagos')}
                className="text-maroon-600 hover:text-maroon-800 underline"
              >
                "Lagos"
              </button>
              <button
                onClick={() => handleSearchChange('2015')}
                className="text-maroon-600 hover:text-maroon-800 underline"
              >
                "2015"
              </button>
            </div>
          </Card>
        )}

        {/* Results Grid */}
        {!isLoading && searchResults && searchResults.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {searchResults.map((person) => (
              <AlumniCard
                key={person.id}
                person={person}
                searchQuery={searchQuery}
              />
            ))}
          </div>
        )}

        {/* Initial State - No Search Started */}
        {!searchStarted && !isLoading && (
          <Card variant="outlined" padding="lg" className="text-center">
            <div className="text-gray-500 mb-6">
              <Search className="w-16 h-16 mx-auto mb-4 text-gray-300" />
              <h3 className="text-xl font-semibold mb-2">Welcome to ExcoBot</h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Start searching to find and connect with FGC Ikom alumni.
                Use the search bar above or apply filters to discover alumni by chapter,
                graduation year, role, or location.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="bg-maroon-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <Users className="w-6 h-6 text-maroon-600" />
                </div>
                <h4 className="font-semibold mb-1">Search by Name</h4>
                <p className="text-sm text-gray-600">Find specific alumni by their full name</p>
              </div>

              <div className="text-center">
                <div className="bg-gold-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <MapPin className="w-6 h-6 text-gold-600" />
                </div>
                <h4 className="font-semibold mb-1">Filter by Location</h4>
                <p className="text-sm text-gray-600">Find alumni in your city or chapter</p>
              </div>

              <div className="text-center">
                <div className="bg-maroon-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <Briefcase className="w-6 h-6 text-maroon-600" />
                </div>
                <h4 className="font-semibold mb-1">Search by Role</h4>
                <p className="text-sm text-gray-600">Find alumni by their current position</p>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}