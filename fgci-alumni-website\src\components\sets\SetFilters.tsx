'use client';

import { useState, useEffect } from 'react';
import { Search, Calendar, X } from 'lucide-react';
import { Button } from '../ui/Button';

interface SetFiltersProps {
  onSearchChange: (search: string) => void;
  onYearChange: (year: number | undefined) => void;
  searchValue: string;
  yearValue: number | undefined;
  availableYears: number[];
}

export function SetFilters({
  onSearchChange,
  onYearChange,
  searchValue,
  yearValue,
  availableYears
}: SetFiltersProps) {
  const [localSearch, setLocalSearch] = useState(searchValue);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearchChange(localSearch);
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearch, onSearchChange]);

  const handleClearFilters = () => {
    setLocalSearch('');
    onSearchChange('');
    onYearChange(undefined);
  };

  const hasActiveFilters = searchValue || yearValue;

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Filter Sets</h3>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClearFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4 mr-1" />
            Clear All
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Search Input */}
        <div className="relative">
          <label htmlFor="set-search" className="block text-sm font-medium text-gray-700 mb-2">
            Search Sets
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              id="set-search"
              type="text"
              placeholder="Search by name, motto, or year..."
              value={localSearch}
              onChange={(e) => setLocalSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon focus:border-maroon transition-colors"
            />
          </div>
        </div>

        {/* Year Filter */}
        <div>
          <label htmlFor="year-filter" className="block text-sm font-medium text-gray-700 mb-2">
            Graduation Year
          </label>
          <div className="relative">
            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <select
              id="year-filter"
              value={yearValue || ''}
              onChange={(e) => onYearChange(e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon focus:border-maroon transition-colors appearance-none bg-white"
            >
              <option value="">All Years</option>
              {availableYears.map((year) => (
                <option key={year} value={year}>
                  Class of {year}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2 border-t border-gray-100">
          <span className="text-sm text-gray-600">Active filters:</span>
          {searchValue && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-maroon/10 text-maroon">
              Search: "{searchValue}"
              <button
                onClick={() => {
                  setLocalSearch('');
                  onSearchChange('');
                }}
                className="ml-1 hover:text-maroon-700"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
          {yearValue && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gold/10 text-gold-700">
              Year: {yearValue}
              <button
                onClick={() => onYearChange(undefined)}
                className="ml-1 hover:text-gold-800"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
}