'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { useReducedMotion } from '@/hooks/useReducedMotion';

interface HoverAnimationProps {
  children: ReactNode;
  className?: string;
  type?: 'lift' | 'scale' | 'glow' | 'tilt';
  intensity?: 'subtle' | 'normal' | 'strong';
  disabled?: boolean;
}

export function HoverAnimation({
  children,
  className,
  type = 'lift',
  intensity = 'normal',
  disabled = false,
}: HoverAnimationProps) {
  const prefersReducedMotion = useReducedMotion();

  if (prefersReducedMotion || disabled) {
    return <div className={className}>{children}</div>;
  }

  const intensityValues = {
    subtle: { lift: -2, scale: 1.01, glow: 0.1, tilt: 2 },
    normal: { lift: -6, scale: 1.02, glow: 0.2, tilt: 5 },
    strong: { lift: -12, scale: 1.05, glow: 0.3, tilt: 8 },
  };

  const values = intensityValues[intensity];

  const variants = {
    lift: {
      hover: {
        y: values.lift,
        boxShadow: '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 20,
        },
      },
    },
    scale: {
      hover: {
        scale: values.scale,
        transition: {
          type: 'spring',
          stiffness: 400,
          damping: 17,
        },
      },
    },
    glow: {
      hover: {
        boxShadow: `0 0 20px rgba(212, 160, 23, ${values.glow})`,
        borderColor: 'rgba(212, 160, 23, 0.5)',
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 20,
        },
      },
    },
    tilt: {
      hover: {
        rotateY: values.tilt,
        rotateX: values.tilt / 2,
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 20,
        },
      },
    },
  };

  return (
    <motion.div
      className={className}
      variants={variants[type]}
      whileHover="hover"
      style={{ transformStyle: 'preserve-3d' }}
    >
      {children}
    </motion.div>
  );
}

// Preset hover components for common patterns
export function HoverCard({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <HoverAnimation type="lift" intensity="normal" className={className}>
      {children}
    </HoverAnimation>
  );
}

export function HoverButton({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <HoverAnimation type="scale" intensity="subtle" className={className}>
      {children}
    </HoverAnimation>
  );
}

export function HoverGlow({ children, className }: { children: ReactNode; className?: string }) {
  return (
    <HoverAnimation type="glow" intensity="normal" className={className}>
      {children}
    </HoverAnimation>
  );
}