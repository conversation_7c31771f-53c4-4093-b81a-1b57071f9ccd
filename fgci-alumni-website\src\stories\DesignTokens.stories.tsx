import type { Meta, StoryObj } from '@storybook/react';

const meta: Meta = {
  title: 'Design System/Design Tokens',
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component: 'Design tokens define the visual foundation of the FGC Ikom Alumni website, including colors, typography, spacing, and other design elements.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Color palette
export const Colors: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Brand Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="w-full h-20 bg-maroon-700 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Maroon 700</div>
              <div className="text-gray-600">#8B4513</div>
              <div className="text-gray-600">Primary</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-20 bg-maroon-600 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Maroon 600</div>
              <div className="text-gray-600">#A0522D</div>
              <div className="text-gray-600">Primary Light</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-20 bg-gold-500 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Gold 500</div>
              <div className="text-gray-600">#FFD700</div>
              <div className="text-gray-600">Accent</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-20 bg-gold-600 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Gold 600</div>
              <div className="text-gray-600">#DAA520</div>
              <div className="text-gray-600">Accent Dark</div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Neutral Colors</h2>
        <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
          {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((shade) => (
            <div key={shade} className="space-y-2">
              <div className={`w-full h-16 bg-gray-${shade} rounded-lg border`}></div>
              <div className="text-sm">
                <div className="font-semibold">Gray {shade}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Semantic Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="space-y-2">
            <div className="w-full h-16 bg-green-600 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Success</div>
              <div className="text-gray-600">Green 600</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-red-600 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Error</div>
              <div className="text-gray-600">Red 600</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-yellow-500 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Warning</div>
              <div className="text-gray-600">Yellow 500</div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-16 bg-blue-600 rounded-lg"></div>
            <div className="text-sm">
              <div className="font-semibold">Info</div>
              <div className="text-gray-600">Blue 600</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
};

// Typography
export const Typography: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Font Families</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-display text-xl mb-2">Playfair Display (Headings)</h3>
            <p className="text-gray-600">Used for headings and display text</p>
            <div className="font-display text-4xl mt-2">The quick brown fox</div>
          </div>
          <div>
            <h3 className="font-body text-xl mb-2">Inter (Body)</h3>
            <p className="text-gray-600">Used for body text and UI elements</p>
            <div className="font-body text-lg mt-2">The quick brown fox jumps over the lazy dog</div>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Heading Scale</h2>
        <div className="space-y-4">
          <div className="heading-1">Heading 1 - Main page titles</div>
          <div className="heading-2">Heading 2 - Section titles</div>
          <div className="heading-3">Heading 3 - Subsection titles</div>
          <div className="heading-4">Heading 4 - Card titles</div>
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Body Text</h2>
        <div className="space-y-4">
          <div>
            <div className="body-large mb-2">Large body text</div>
            <p className="text-gray-600">Used for introductory paragraphs and important content</p>
          </div>
          <div>
            <div className="body-base mb-2">Base body text</div>
            <p className="text-gray-600">Standard body text for most content</p>
          </div>
          <div>
            <div className="body-small mb-2">Small body text</div>
            <p className="text-gray-600">Used for captions, metadata, and secondary information</p>
          </div>
        </div>
      </div>
    </div>
  ),
};

// Spacing
export const Spacing: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Spacing Scale</h2>
        <div className="space-y-4">
          {[1, 2, 3, 4, 5, 6, 8, 10, 12, 16, 20, 24, 32].map((size) => (
            <div key={size} className="flex items-center space-x-4">
              <div className="w-16 text-sm font-mono">{size * 4}px</div>
              <div className="w-16 text-sm font-mono">space-{size}</div>
              <div className={`bg-blue-500 h-4`} style={{ width: `${size * 4}px` }}></div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Container Sizes</h2>
        <div className="space-y-4">
          <div>
            <div className="font-semibold mb-2">container-custom</div>
            <div className="bg-gray-100 p-4 rounded">
              <div className="container-custom bg-blue-100 p-4 rounded">
                Max width with responsive padding
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  ),
};

// Shadows and Effects
export const ShadowsAndEffects: Story = {
  render: () => (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-bold mb-4">Shadows</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="font-semibold mb-2">Small Shadow</h3>
            <p className="text-gray-600">shadow-sm</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h3 className="font-semibold mb-2">Medium Shadow</h3>
            <p className="text-gray-600">shadow-md</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h3 className="font-semibold mb-2">Large Shadow</h3>
            <p className="text-gray-600">shadow-lg</p>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Border Radius</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-200 p-4 rounded-none">
            <div className="text-sm font-semibold">None</div>
            <div className="text-xs text-gray-600">rounded-none</div>
          </div>
          <div className="bg-gray-200 p-4 rounded-sm">
            <div className="text-sm font-semibold">Small</div>
            <div className="text-xs text-gray-600">rounded-sm</div>
          </div>
          <div className="bg-gray-200 p-4 rounded-md">
            <div className="text-sm font-semibold">Medium</div>
            <div className="text-xs text-gray-600">rounded-md</div>
          </div>
          <div className="bg-gray-200 p-4 rounded-lg">
            <div className="text-sm font-semibold">Large</div>
            <div className="text-xs text-gray-600">rounded-lg</div>
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">Transitions</h2>
        <div className="space-y-4">
          <div className="bg-white p-4 rounded-lg border hover:shadow-md transition-shadow duration-200">
            <div className="font-semibold">Hover for shadow transition</div>
            <div className="text-sm text-gray-600">transition-shadow duration-200</div>
          </div>
          <div className="bg-maroon-700 text-white p-4 rounded-lg hover:bg-maroon-600 transition-colors duration-200">
            <div className="font-semibold">Hover for color transition</div>
            <div className="text-sm opacity-90">transition-colors duration-200</div>
          </div>
        </div>
      </div>
    </div>
  ),
};
