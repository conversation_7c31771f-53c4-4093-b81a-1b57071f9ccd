import { ValidationSchema, FormErrors } from '@/lib/types';
import { validateField, validateForm } from '@/lib/validation';

/**
 * Utility functions for form validation
 */

/**
 * Create a validation function for a specific field
 */
export function createFieldValidator(
  fieldName: string,
  validationSchema: ValidationSchema
) {
  return (value: unknown): string[] => {
    const fieldRules = validationSchema[fieldName];
    if (!fieldRules) return [];
    return validateField(value, fieldRules);
  };
}

/**
 * Create a debounced validation function
 */
export function createDebouncedValidator(
  validator: (value: unknown) => string[],
  delay: number = 300
) {
  let timeoutId: NodeJS.Timeout;
  
  return (value: unknown): Promise<string[]> => {
    return new Promise((resolve) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        resolve(validator(value));
      }, delay);
    });
  };
}

/**
 * Validate form data and return formatted errors
 */
export function validateFormData<T extends Record<string, unknown>>(
  data: T,
  schema: ValidationSchema,
  options: {
    touchedFields?: Record<keyof T, boolean>;
    onlyTouchedFields?: boolean;
  } = {}
): { isValid: boolean; errors: FormErrors; fieldCount: number } {
  const { touchedFields = {}, onlyTouchedFields = false } = options;
  
  const { isValid, errors } = validateForm(data, schema);
  
  // Filter errors to only include touched fields if requested
  const filteredErrors: FormErrors = {};
  let fieldCount = 0;
  
  Object.entries(errors).forEach(([field, fieldErrors]) => {
    if (!onlyTouchedFields || touchedFields[field as keyof T]) {
      filteredErrors[field] = fieldErrors;
      fieldCount += fieldErrors.length;
    }
  });
  
  const finalIsValid = Object.keys(filteredErrors).length === 0;
  
  return {
    isValid: finalIsValid,
    errors: filteredErrors,
    fieldCount,
  };
}

/**
 * Get validation state for a specific field
 */
export function getFieldValidationState(
  fieldName: string,
  errors: FormErrors,
  touched: Record<string, boolean>
): {
  hasError: boolean;
  errors: string[];
  touched: boolean;
  isValid: boolean;
} {
  const fieldErrors = errors[fieldName] || [];
  const fieldTouched = touched[fieldName] || false;
  
  return {
    hasError: fieldTouched && fieldErrors.length > 0,
    errors: fieldErrors,
    touched: fieldTouched,
    isValid: fieldTouched && fieldErrors.length === 0,
  };
}

/**
 * Format field name for display (camelCase to readable text)
 */
export function formatFieldName(fieldName: string): string {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .toLowerCase()
    .replace(/^./, str => str.toUpperCase());
}

/**
 * Get error summary for display
 */
export function getErrorSummary(errors: FormErrors): {
  totalErrors: number;
  fieldCount: number;
  summary: Array<{ field: string; errors: string[] }>;
} {
  const fieldCount = Object.keys(errors).length;
  const totalErrors = Object.values(errors).reduce(
    (sum, fieldErrors) => sum + fieldErrors.length,
    0
  );
  
  const summary = Object.entries(errors).map(([field, fieldErrors]) => ({
    field: formatFieldName(field),
    errors: fieldErrors,
  }));
  
  return {
    totalErrors,
    fieldCount,
    summary,
  };
}

/**
 * Check if form is ready for submission
 */
export function isFormReadyForSubmission<T extends Record<string, unknown>>(
  data: T,
  schema: ValidationSchema,
  touched: Record<keyof T, boolean>,
  requiredFields: (keyof T)[]
): {
  isReady: boolean;
  missingFields: string[];
  hasErrors: boolean;
} {
  const { isValid, errors } = validateForm(data, schema);
  
  // Check if all required fields are touched
  const missingFields = requiredFields.filter(field => !touched[field]);
  
  return {
    isReady: isValid && missingFields.length === 0,
    missingFields: missingFields.map(field => formatFieldName(field as string)),
    hasErrors: !isValid,
  };
}

/**
 * Create validation rules for common patterns
 */
export const commonValidationRules = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  email: {
    required: true,
    email: true,
    maxLength: 255,
  },
  phone: {
    phone: true,
  },
  password: {
    required: true,
    minLength: 8,
    strongPassword: true,
  },
  url: {
    url: true,
  },
  graduationYear: {
    min: 1970,
    max: new Date().getFullYear(),
  },
};

/**
 * Accessibility helpers for form validation
 */
export const a11yHelpers = {
  /**
   * Generate ARIA attributes for form fields
   */
  getFieldAriaAttributes: (
    fieldName: string,
    hasError: boolean,
    helpText?: string
  ) => {
    const attributes: Record<string, string> = {};
    
    if (hasError) {
      attributes['aria-invalid'] = 'true';
      attributes['aria-describedby'] = `${fieldName}-error`;
    }
    
    if (helpText) {
      const describedBy = attributes['aria-describedby'] || '';
      attributes['aria-describedby'] = describedBy 
        ? `${describedBy} ${fieldName}-help`
        : `${fieldName}-help`;
    }
    
    return attributes;
  },
  
  /**
   * Generate live region announcements for validation changes
   */
  generateValidationAnnouncement: (
    fieldName: string,
    errors: string[],
    isValid: boolean
  ): string => {
    const formattedFieldName = formatFieldName(fieldName);
    
    if (isValid) {
      return `${formattedFieldName} is now valid`;
    }
    
    if (errors.length === 1) {
      return `${formattedFieldName}: ${errors[0]}`;
    }
    
    return `${formattedFieldName} has ${errors.length} errors: ${errors.join(', ')}`;
  },
};

/**
 * Performance helpers for form validation
 */
export const performanceHelpers = {
  /**
   * Throttle validation function calls
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): T => {
    let timeoutId: NodeJS.Timeout | null = null;
    let lastExecTime = 0;
    
    return ((...args: Parameters<T>) => {
      const currentTime = Date.now();
      
      if (currentTime - lastExecTime > delay) {
        func(...args);
        lastExecTime = currentTime;
      } else {
        if (timeoutId) clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func(...args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    }) as T;
  },
  
  /**
   * Memoize validation results
   */
  memoizeValidation: <T>(
    validator: (value: T) => string[]
  ) => {
    const cache = new Map<T, string[]>();
    
    return (value: T): string[] => {
      if (cache.has(value)) {
        return cache.get(value)!;
      }
      
      const result = validator(value);
      cache.set(value, result);
      
      // Limit cache size to prevent memory leaks
      if (cache.size > 100) {
        const firstKey = cache.keys().next().value;
        cache.delete(firstKey);
      }
      
      return result;
    };
  },
};