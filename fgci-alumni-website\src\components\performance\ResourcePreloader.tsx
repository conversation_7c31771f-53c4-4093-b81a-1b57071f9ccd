'use client';

import { useEffect } from 'react';
import { ResourcePreloader } from '@/lib/webVitals';

interface ResourcePreloaderProps {
  criticalImages?: string[];
  criticalFonts?: string[];
  criticalCSS?: string[];
  criticalJS?: string[];
}

export function ResourcePreloaderComponent({
  criticalImages = [],
  criticalFonts = [],
  criticalCSS = [],
  criticalJS = []
}: ResourcePreloaderProps) {
  useEffect(() => {
    // Preload critical fonts
    criticalFonts.forEach(font => {
      ResourcePreloader.preloadFont(font);
    });

    // Preload critical CSS
    criticalCSS.forEach(css => {
      ResourcePreloader.preloadCSS(css);
    });

    // Preload critical JavaScript
    criticalJS.forEach(js => {
      ResourcePreloader.preloadJS(js);
    });

    // Preload critical images
    criticalImages.forEach(image => {
      ResourcePreloader.preloadImage(image, 'high');
    });
  }, [criticalImages, criticalFonts, criticalCSS, criticalJS]);

  return null; // This component doesn't render anything
}

/**
 * Default critical resources for the application
 */
export function DefaultResourcePreloader() {
  return (
    <ResourcePreloaderComponent
      criticalFonts={[
        '/fonts/inter-var.woff2',
        '/fonts/playfair-display-var.woff2'
      ]}
      criticalImages={[
        '/images/hero-bg.jpg',
        '/images/logo.svg'
      ]}
    />
  );
}