'use client';

import { useState } from 'react';
import { useChapterExcos } from '@/hooks/useChapterExcos';
import { ChapterExcosHero } from './ChapterExcosHero';
import { ChapterExcosSearch } from './ChapterExcosSearch';
import { ChapterExcosGrid } from './ChapterExcosGrid';
import { ChapterExcosFilters } from './ChapterExcosFilters';
import { LoadingSpinner, ErrorState } from '@/components/ui/LoadingStates';
import { SearchFilters } from '@/lib/types';

export function ChapterExcosDirectory() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const { 
    data: chapterExcos, 
    loading, 
    error, 
    refetch 
  } = useChapterExcos({ 
    query: searchQuery, 
    ...filters 
  });

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFilterChange = (newFilters: SearchFilters) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setFilters({});
  };

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <ErrorState 
          message="Failed to load chapter executives directory" 
          onRetry={refetch}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <ChapterExcosHero />
      
      <div className="container mx-auto px-4 py-8">
        {/* Search and Controls */}
        <div className="mb-8 space-y-6">
          <ChapterExcosSearch
            query={searchQuery}
            onSearch={handleSearch}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
            resultsCount={chapterExcos?.length || 0}
          />
          
          <ChapterExcosFilters
            filters={filters}
            onFilterChange={handleFilterChange}
            onClearFilters={handleClearFilters}
          />
        </div>

        {/* Results */}
        {loading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <ChapterExcosGrid
            chapterExcos={chapterExcos || []}
            viewMode={viewMode}
            searchQuery={searchQuery}
          />
        )}
      </div>
    </div>
  );
}