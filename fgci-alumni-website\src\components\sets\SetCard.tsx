'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Set } from '../../lib/types';
import { Card } from '../ui/Card';
import { Avatar } from '../ui/Avatar';
import { Users, Calendar, User } from 'lucide-react';

interface SetCardProps {
  set: Set;
  showMemberCount?: boolean;
  compact?: boolean;
}

export function SetCard({ set, showMemberCount = true, compact = false }: SetCardProps) {
  return (
    <Link href={`/sets/${set.slug}`} className="block group">
      <Card
        variant="elevated"
        padding={compact ? 'sm' : 'md'}
        hover
        className="h-full transition-all duration-300 group-hover:shadow-lg group-hover:-translate-y-1"
      >
        <div className="space-y-4">
          {/* Hero Image */}
          {set.heroImage && (
            <div className="relative aspect-video rounded-lg overflow-hidden bg-gray-100">
              <Image
                src={set.heroImage}
                alt={`${set.name} hero image`}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              
              {/* Year Badge */}
              <div className="absolute top-3 right-3">
                <div className="bg-maroon text-white px-3 py-1 rounded-full text-sm font-semibold">
                  {set.year}
                </div>
              </div>
            </div>
          )}

          {/* Content */}
          <div className="space-y-3">
            {/* Header */}
            <div>
              <h3 className="text-xl font-bold text-gray-900 group-hover:text-maroon transition-colors">
                {set.name}
              </h3>
              {set.motto && (
                <p className="text-sm text-gold font-medium italic mt-1">
                  "{set.motto}"
                </p>
              )}
            </div>

            {/* Description */}
            {set.description && !compact && (
              <p className="text-gray-600 text-sm line-clamp-2">
                {set.description}
              </p>
            )}

            {/* Stats Row */}
            <div className="flex items-center justify-between text-sm text-gray-500">
              <div className="flex items-center space-x-4">
                {/* Member Count */}
                {showMemberCount && set.memberCount && (
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4" />
                    <span>{set.memberCount} members</span>
                  </div>
                )}

                {/* Graduation Year */}
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4" />
                  <span>Class of {set.year}</span>
                </div>
              </div>
            </div>

            {/* Set Leader */}
            {set.leader && (
              <div className="flex items-center space-x-3 pt-2 border-t border-gray-100">
                <Avatar
                  src={set.leader.photo}
                  alt={set.leader.name}
                  size="sm"
                  fallback={set.leader.name}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-1">
                    <User className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-500">Set Leader</span>
                  </div>
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {set.leader.name}
                  </p>
                </div>
              </div>
            )}

            {/* Activities Count */}
            {set.activities.length > 0 && (
              <div className="text-xs text-gray-500">
                {set.activities.length} recent {set.activities.length === 1 ? 'activity' : 'activities'}
              </div>
            )}
          </div>
        </div>
      </Card>
    </Link>
  );
}