'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/Button';

interface MemorialFiltersProps {
  onFiltersChange?: (filters: MemorialFilters) => void;
}

interface MemorialFilters {
  graduationYear?: string;
  chapter?: string;
  searchQuery?: string;
}

export function MemorialFilters({ onFiltersChange }: MemorialFiltersProps) {
  const [filters, setFilters] = useState<MemorialFilters>({});
  const [isExpanded, setIsExpanded] = useState(false);

  const graduationYears = [
    '1970s', '1980s', '1990s', '2000s', '2010s'
  ];

  const chapters = [
    'Lagos Chapter',
    'Abuja Chapter', 
    'Port Harcourt Chapter',
    'Enugu Chapter',
    'Calabar Chapter',
    'Nsukka Chapter',
    'Kano Chapter',
    'Ibadan Chapter'
  ];

  const handleFilterChange = (key: keyof MemorialFilters, value: string) => {
    const newFilters = {
      ...filters,
      [key]: filters[key] === value ? undefined : value
    };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const clearFilters = () => {
    setFilters({});
    onFiltersChange?.({});
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== undefined);

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Filter Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-medium text-gray-900">Search & Filter</h3>
            {hasActiveFilters && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-maroon-100 text-maroon-800">
                {Object.values(filters).filter(Boolean).length} active
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-gray-500 hover:text-gray-700"
              >
                Clear All
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-500 hover:text-gray-700"
            >
              {isExpanded ? 'Hide Filters' : 'Show Filters'}
              <svg 
                className={`ml-2 w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </Button>
          </div>
        </div>
      </div>

      {/* Search Bar - Always Visible */}
      <div className="p-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <input
            type="text"
            placeholder="Search by name, role, or chapter..."
            value={filters.searchQuery || ''}
            onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-maroon-500 focus:border-maroon-500"
          />
        </div>
      </div>

      {/* Expandable Filters */}
      <motion.div
        initial={false}
        animate={{ height: isExpanded ? 'auto' : 0 }}
        className="overflow-hidden"
      >
        <div className="px-4 pb-4 space-y-4">
          {/* Graduation Year Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Graduation Era
            </label>
            <div className="flex flex-wrap gap-2">
              {graduationYears.map((year) => (
                <button
                  key={year}
                  onClick={() => handleFilterChange('graduationYear', year)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    filters.graduationYear === year
                      ? 'bg-maroon-100 text-maroon-800 border border-maroon-300'
                      : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {year}
                </button>
              ))}
            </div>
          </div>

          {/* Chapter Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Chapter
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {chapters.map((chapter) => (
                <button
                  key={chapter}
                  onClick={() => handleFilterChange('chapter', chapter)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors text-left ${
                    filters.chapter === chapter
                      ? 'bg-maroon-100 text-maroon-800 border border-maroon-300'
                      : 'bg-gray-100 text-gray-700 border border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {chapter}
                </button>
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}