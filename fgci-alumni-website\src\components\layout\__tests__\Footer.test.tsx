import { render, screen, fireEvent } from '@/lib/test-utils';
import { Footer } from '../Footer';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Footer Component', () => {
  it('renders footer with proper semantic structure', () => {
    render(<Footer />);
    
    const footer = screen.getByRole('contentinfo');
    expect(footer).toBeInTheDocument();
  });

  it('renders contact information section', () => {
    render(<Footer />);
    
    expect(screen.getByText('Contact Information')).toBeInTheDocument();
    expect(screen.getByText(/email/i)).toBeInTheDocument();
    expect(screen.getByText(/phone/i)).toBeInTheDocument();
    expect(screen.getByText(/address/i)).toBeInTheDocument();
  });

  it('renders quick links section', () => {
    render(<Footer />);
    
    expect(screen.getByText('Quick Links')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /about/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /chapters/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /sets/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /events/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /gallery/i })).toBeInTheDocument();
  });

  it('renders social media links', () => {
    render(<Footer />);
    
    expect(screen.getByText('Follow Us')).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /facebook/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /twitter/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /linkedin/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /instagram/i })).toBeInTheDocument();
  });

  it('renders newsletter signup section', () => {
    render(<Footer />);
    
    expect(screen.getByText('Stay Connected')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/email address/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /subscribe/i })).toBeInTheDocument();
  });

  it('handles newsletter signup submission', () => {
    const mockSubmit = jest.fn();
    render(<Footer onNewsletterSubmit={mockSubmit} />);
    
    const emailInput = screen.getByPlaceholderText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /subscribe/i });
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);
    
    expect(mockSubmit).toHaveBeenCalledWith('<EMAIL>');
  });

  it('validates email input in newsletter signup', () => {
    render(<Footer />);
    
    const emailInput = screen.getByPlaceholderText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /subscribe/i });
    
    // Try to submit with invalid email
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
    fireEvent.click(submitButton);
    
    expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
  });

  it('renders copyright information', () => {
    render(<Footer />);
    
    const currentYear = new Date().getFullYear();
    expect(screen.getByText(new RegExp(`© ${currentYear}`))).toBeInTheDocument();
    expect(screen.getByText(/FGC Ikom Alumni Association/i)).toBeInTheDocument();
  });

  it('renders legal links', () => {
    render(<Footer />);
    
    expect(screen.getByRole('link', { name: /privacy policy/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /terms of service/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /cookie policy/i })).toBeInTheDocument();
  });

  it('renders back to top button', () => {
    render(<Footer />);
    
    const backToTopButton = screen.getByRole('button', { name: /back to top/i });
    expect(backToTopButton).toBeInTheDocument();
  });

  it('handles back to top functionality', () => {
    const mockScrollTo = jest.fn();
    Object.defineProperty(window, 'scrollTo', {
      writable: true,
      value: mockScrollTo,
    });

    render(<Footer />);
    
    const backToTopButton = screen.getByRole('button', { name: /back to top/i });
    fireEvent.click(backToTopButton);
    
    expect(mockScrollTo).toHaveBeenCalledWith({
      top: 0,
      behavior: 'smooth',
    });
  });

  it('renders contact links with proper attributes', () => {
    render(<Footer />);
    
    const emailLink = screen.getByRole('link', { name: /<EMAIL>/i });
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
    
    const phoneLink = screen.getByRole('link', { name: /\+234/i });
    expect(phoneLink).toHaveAttribute('href', expect.stringMatching(/^tel:/));
  });

  it('renders social media links with proper attributes', () => {
    render(<Footer />);
    
    const facebookLink = screen.getByRole('link', { name: /facebook/i });
    expect(facebookLink).toHaveAttribute('target', '_blank');
    expect(facebookLink).toHaveAttribute('rel', 'noopener noreferrer');
    
    const twitterLink = screen.getByRole('link', { name: /twitter/i });
    expect(twitterLink).toHaveAttribute('target', '_blank');
    expect(twitterLink).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('has proper heading hierarchy', () => {
    render(<Footer />);
    
    const headings = screen.getAllByRole('heading');
    
    // Should have h2 headings for main sections
    headings.forEach(heading => {
      expect(['H2', 'H3'].includes(heading.tagName)).toBe(true);
    });
  });

  it('has proper landmark structure', () => {
    render(<Footer />);
    
    const footer = screen.getByRole('contentinfo');
    const navigation = screen.getByRole('navigation', { name: /footer navigation/i });
    
    expect(footer).toContainElement(navigation);
  });

  it('supports keyboard navigation', () => {
    render(<Footer />);
    
    const links = screen.getAllByRole('link');
    const buttons = screen.getAllByRole('button');
    
    // All interactive elements should be focusable
    [...links, ...buttons].forEach(element => {
      element.focus();
      expect(document.activeElement).toBe(element);
    });
  });

  it('has proper ARIA attributes', () => {
    render(<Footer />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveAttribute('aria-label', 'Footer navigation');
    
    const newsletterForm = screen.getByRole('form');
    expect(newsletterForm).toHaveAttribute('aria-label', 'Newsletter signup');
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<Footer />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('renders responsive layout', () => {
    render(<Footer />);
    
    const footer = screen.getByRole('contentinfo');
    expect(footer).toHaveClass('footer-responsive');
  });

  it('handles newsletter loading state', () => {
    render(<Footer newsletterLoading />);
    
    const submitButton = screen.getByRole('button', { name: /subscribe/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText(/subscribing/i)).toBeInTheDocument();
  });

  it('shows newsletter success message', () => {
    render(<Footer newsletterSuccess />);
    
    expect(screen.getByText(/successfully subscribed/i)).toBeInTheDocument();
  });

  it('shows newsletter error message', () => {
    render(<Footer newsletterError="Subscription failed" />);
    
    expect(screen.getByText(/subscription failed/i)).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    render(<Footer className="custom-footer" />);
    
    const footer = screen.getByRole('contentinfo');
    expect(footer).toHaveClass('custom-footer');
  });

  it('renders organization information', () => {
    render(<Footer />);
    
    expect(screen.getByText(/Federal Government College Ikom/i)).toBeInTheDocument();
    expect(screen.getByText(/Alumni Association/i)).toBeInTheDocument();
  });

  it('renders mission statement', () => {
    render(<Footer />);
    
    expect(screen.getByText(/connecting alumni/i)).toBeInTheDocument();
    expect(screen.getByText(/fostering community/i)).toBeInTheDocument();
  });

  it('handles form validation properly', () => {
    render(<Footer />);
    
    const emailInput = screen.getByPlaceholderText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /subscribe/i });
    
    // Submit empty form
    fireEvent.click(submitButton);
    expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    
    // Submit with invalid email
    fireEvent.change(emailInput, { target: { value: 'invalid' } });
    fireEvent.click(submitButton);
    expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
    
    // Submit with valid email
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(submitButton);
    expect(screen.queryByText(/please enter a valid email/i)).not.toBeInTheDocument();
  });
});