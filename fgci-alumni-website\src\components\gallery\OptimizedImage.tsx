'use client';

import { useState, useRef, useEffect, forwardRef } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { 
  generateBlurDataURL, 
  calculateOptimalQuality, 
  RESPONSIVE_SIZES,
  createLazyLoadObserver,
  ImagePerformanceObserver
} from '@/lib/imageOptimization';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  usage?: 'hero' | 'gallery' | 'card' | 'avatar' | 'thumbnail';
  aspectRatio?: string;
  onLoad?: () => void;
  onError?: () => void;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  enablePerformanceMonitoring?: boolean;
}

// Global performance observer instance
let performanceObserver: ImagePerformanceObserver | null = null;

export const OptimizedImage = forwardRef<HTMLDivElement, OptimizedImageProps>(
  ({
    src,
    alt,
    width,
    height,
    className,
    priority = false,
    quality,
    sizes,
    usage = 'gallery',
    aspectRatio,
    onLoad,
    onError,
    placeholder = 'blur',
    blurDataURL,
    enablePerformanceMonitoring = false
  }, ref) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [hasError, setHasError] = useState(false);
    const [isInView, setIsInView] = useState(priority);
    const [loadStartTime, setLoadStartTime] = useState<number>(0);
    const imageRef = useRef<HTMLDivElement>(null);

    // Calculate optimal quality and sizes if not provided
    const optimizedQuality = quality || calculateOptimalQuality(usage, window?.devicePixelRatio > 1);
    const optimizedSizes = sizes || RESPONSIVE_SIZES[usage] || RESPONSIVE_SIZES.gallery;
    const optimizedBlurDataURL = blurDataURL || generateBlurDataURL(10, 10, '#f3f4f6');

    // Initialize performance monitoring
    useEffect(() => {
      if (enablePerformanceMonitoring && !performanceObserver) {
        performanceObserver = new ImagePerformanceObserver();
      }
      
      return () => {
        if (enablePerformanceMonitoring && performanceObserver) {
          performanceObserver.disconnect();
          performanceObserver = null;
        }
      };
    }, [enablePerformanceMonitoring]);

    // Enhanced intersection observer with better performance
    useEffect(() => {
      if (priority) return;

      const observer = createLazyLoadObserver(
        (entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            setLoadStartTime(performance.now());
            observer?.unobserve(entry.target);
          }
        },
        {
          rootMargin: '200px 0px',
          threshold: 0.1
        }
      );

      if (observer && imageRef.current) {
        observer.observe(imageRef.current);
      }

      return () => {
        if (observer) {
          observer.disconnect();
        }
      };
    }, [priority]);

    const handleLoad = () => {
      setIsLoaded(true);
      
      // Performance monitoring
      if (enablePerformanceMonitoring && loadStartTime > 0) {
        const loadTime = performance.now() - loadStartTime;
        console.debug(`Image loaded in ${loadTime.toFixed(2)}ms:`, src);
      }
      
      onLoad?.();
    };

    const handleError = () => {
      setHasError(true);
      setIsLoaded(true);
      
      if (enablePerformanceMonitoring) {
        console.warn('Image failed to load:', src);
      }
      
      onError?.();
    };

    // Calculate container styles for aspect ratio
    const containerStyles = aspectRatio ? {
      aspectRatio: aspectRatio,
    } : {};

    return (
      <div
        ref={ref || imageRef}
        data-testid="optimized-image"
        className={cn(
          'relative overflow-hidden bg-gray-200',
          'gallery-image gpu-accelerated',
          // Optimize for different usage types
          usage === 'hero' && 'will-change-transform',
          usage === 'gallery' && 'content-visibility-auto',
          className
        )}
        style={containerStyles}
      >
        {isInView && !hasError ? (
          <>
            {/* Enhanced loading placeholder with shimmer effect */}
            {!isLoaded && (
              <div className="absolute inset-0">
                <div className="image-loading" />
                {/* Skeleton content for better UX */}
                <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-pulse" />
              </div>
            )}

            {/* Optimized image with enhanced attributes */}
            <Image
              src={src}
              alt={alt}
              width={width}
              height={height}
              fill={!width || !height}
              sizes={optimizedSizes}
              className={cn(
                'object-cover transition-opacity duration-300',
                isLoaded ? 'opacity-100' : 'opacity-0',
                // Performance optimizations
                'will-change-opacity',
                usage === 'hero' && 'object-center',
                usage === 'avatar' && 'object-center object-cover'
              )}
              onLoad={handleLoad}
              onError={handleError}
              priority={priority}
              quality={optimizedQuality}
              placeholder={placeholder}
              blurDataURL={optimizedBlurDataURL}
              loading={priority ? 'eager' : 'lazy'}
              decoding="async"
              // Enhanced attributes for better performance
              fetchPriority={priority ? 'high' : 'auto'}
              // Prevent layout shift
              style={{
                maxWidth: '100%',
                height: 'auto',
              }}
            />
          </>
        ) : hasError ? (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
            <div className="text-center text-gray-500">
              <svg 
                className="w-8 h-8 mx-auto mb-2" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
                />
              </svg>
              <p className="text-xs">Failed to load</p>
            </div>
          </div>
        ) : (
          <div className="absolute inset-0 bg-gray-200">
            <div className="animate-pulse bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 h-full" />
          </div>
        )}
      </div>
    );
  }
);

OptimizedImage.displayName = 'OptimizedImage';