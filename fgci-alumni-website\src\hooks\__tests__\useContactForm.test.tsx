import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useContactForm } from '../useContactForm';
import { apiClient } from '@/lib/api-client';

// Mock the API client
vi.mock('@/lib/api-client', () => ({
  apiClient: {
    post: vi.fn(),
  },
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={createTestQueryClient()}>
    {children}
  </QueryClientProvider>
);

describe('useContactForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('initializes with correct default values', () => {
    const { result } = renderHook(() => useContactForm(), { wrapper });

    expect(result.current.formData).toEqual({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      graduationYear: undefined,
      inquiryType: '',
      chapter: '',
    });
    expect(result.current.errors).toEqual({});
    expect(result.current.touched).toEqual({});
    expect(result.current.isSubmitting).toBe(false);
    expect(result.current.submitStatus).toBe('idle');
    expect(result.current.submitMessage).toBe('');
  });

  it('handles successful form submission', async () => {
    const mockResponse = {
      success: true,
      data: {
        id: 'contact-123',
        message: 'Thank you for your message!',
        submittedAt: new Date().toISOString(),
      },
    };

    (apiClient.post as any).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useContactForm(), { wrapper });

    // Fill in required fields
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldValue('email', '<EMAIL>');
      result.current.setFieldValue('subject', 'Test Subject');
      result.current.setFieldValue('message', 'This is a test message that is long enough');
      result.current.setFieldValue('inquiryType', 'general');
    });

    const mockEvent = {
      preventDefault: vi.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    await waitFor(() => {
      expect(result.current.submitStatus).toBe('success');
      expect(result.current.submitMessage).toBe('Thank you for your message!');
    });

    expect(apiClient.post).toHaveBeenCalledWith('/contact', {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '',
      subject: 'Test Subject',
      message: 'This is a test message that is long enough',
      graduationYear: undefined,
      inquiryType: 'general',
      chapter: '',
    });
  });

  it('handles form submission errors', async () => {
    const mockError = {
      message: 'Server error occurred',
    };

    (apiClient.post as any).mockRejectedValue(mockError);

    const { result } = renderHook(() => useContactForm(), { wrapper });

    // Fill in required fields
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldValue('email', '<EMAIL>');
      result.current.setFieldValue('subject', 'Test Subject');
      result.current.setFieldValue('message', 'This is a test message that is long enough');
      result.current.setFieldValue('inquiryType', 'general');
    });

    const mockEvent = {
      preventDefault: vi.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    await waitFor(() => {
      expect(result.current.submitStatus).toBe('error');
      expect(result.current.submitMessage).toBe('Server error occurred');
    });
  });

  it('handles validation errors from server', async () => {
    const mockError = {
      fieldErrors: {
        email: ['Invalid email format'],
        name: ['Name is too short'],
      },
    };

    (apiClient.post as any).mockRejectedValue(mockError);

    const { result } = renderHook(() => useContactForm(), { wrapper });

    // Fill in required fields
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldValue('email', '<EMAIL>');
      result.current.setFieldValue('subject', 'Test Subject');
      result.current.setFieldValue('message', 'This is a test message that is long enough');
      result.current.setFieldValue('inquiryType', 'general');
    });

    const mockEvent = {
      preventDefault: vi.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    await act(async () => {
      await result.current.handleSubmit()(mockEvent);
    });

    await waitFor(() => {
      expect(result.current.submitStatus).toBe('error');
      expect(result.current.submitMessage).toBe('Please correct the errors below and try again.');
      expect(result.current.errors.email).toEqual(['Invalid email format']);
      expect(result.current.errors.name).toEqual(['Name is too short']);
    });
  });

  it('clears submit status when user starts typing', () => {
    const { result } = renderHook(() => useContactForm(), { wrapper });

    // Set initial submit status
    act(() => {
      result.current.setFieldError('name', 'Some error');
    });

    // Simulate typing
    act(() => {
      result.current.handleChange({
        target: { name: 'name', value: 'J', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.submitStatus).toBe('idle');
    expect(result.current.submitMessage).toBe('');
  });

  it('resets form and status', () => {
    const { result } = renderHook(() => useContactForm(), { wrapper });

    // Make some changes
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldError('email', 'Some error');
    });

    // Reset form
    act(() => {
      result.current.reset();
    });

    expect(result.current.formData).toEqual({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      graduationYear: undefined,
      inquiryType: '',
      chapter: '',
    });
    expect(result.current.errors).toEqual({});
    expect(result.current.submitStatus).toBe('idle');
    expect(result.current.submitMessage).toBe('');
  });

  it('provides correct field props with enhanced onChange', () => {
    const { result } = renderHook(() => useContactForm(), { wrapper });

    const fieldProps = result.current.getFieldProps('name');

    expect(fieldProps).toEqual({
      name: 'name',
      value: '',
      onChange: expect.any(Function),
      onBlur: expect.any(Function),
    });

    // Test that the enhanced onChange clears submit status
    act(() => {
      result.current.setFieldError('name', 'Some error');
    });

    act(() => {
      fieldProps.onChange({
        target: { name: 'name', value: 'J', type: 'text' },
      } as React.ChangeEvent<HTMLInputElement>);
    });

    expect(result.current.submitStatus).toBe('idle');
  });

  it('shows loading state during submission', async () => {
    let resolvePromise: (value: any) => void;
    const mockPromise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    (apiClient.post as any).mockReturnValue(mockPromise);

    const { result } = renderHook(() => useContactForm(), { wrapper });

    // Fill in required fields
    act(() => {
      result.current.setFieldValue('name', 'John Doe');
      result.current.setFieldValue('email', '<EMAIL>');
      result.current.setFieldValue('subject', 'Test Subject');
      result.current.setFieldValue('message', 'This is a test message that is long enough');
      result.current.setFieldValue('inquiryType', 'general');
    });

    const mockEvent = {
      preventDefault: vi.fn(),
    } as unknown as React.FormEvent<HTMLFormElement>;

    // Start submission
    act(() => {
      result.current.handleSubmit()(mockEvent);
    });

    // Should be in loading state
    expect(result.current.isSubmitting).toBe(true);

    // Resolve the promise
    act(() => {
      resolvePromise!({
        success: true,
        data: { message: 'Success!' },
      });
    });

    await waitFor(() => {
      expect(result.current.isSubmitting).toBe(false);
    });
  });
});
