import { render, screen } from '@/lib/test-utils';
import { Avatar } from '../Avatar';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Avatar Component', () => {
  it('renders with image when src is provided', () => {
    render(
      <Avatar
        src="https://example.com/avatar.jpg"
        alt="John Do<PERSON>"
        name="<PERSON>"
      />
    );
    
    const image = screen.getByRole('img', { name: /john doe/i });
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', 'https://example.com/avatar.jpg');
    expect(image).toHaveAttribute('alt', '<PERSON>');
  });

  it('renders initials fallback when no image src', () => {
    render(<Avatar name="<PERSON>" />);
    
    expect(screen.getByText('JD')).toBeInTheDocument();
    expect(screen.queryByRole('img')).not.toBeInTheDocument();
  });

  it('renders single initial for single name', () => {
    render(<Avatar name="John" />);
    
    expect(screen.getByText('J')).toBeInTheDocument();
  });

  it('renders with different sizes', () => {
    const { rerender } = render(<Avatar name="John Doe" size="sm" />);
    expect(screen.getByText('JD').closest('div')).toHaveClass('avatar-sm');

    rerender(<Avatar name="John Doe" size="lg" />);
    expect(screen.getByText('JD').closest('div')).toHaveClass('avatar-lg');

    rerender(<Avatar name="John Doe" size="xl" />);
    expect(screen.getByText('JD').closest('div')).toHaveClass('avatar-xl');
  });

  it('handles image load error gracefully', () => {
    render(
      <Avatar
        src="https://invalid-url.com/avatar.jpg"
        alt="John Doe"
        name="John Doe"
      />
    );
    
    const image = screen.getByRole('img');
    
    // Simulate image load error
    image.dispatchEvent(new Event('error'));
    
    // Should fall back to initials
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    render(<Avatar name="John Doe" className="custom-avatar" />);
    
    const avatar = screen.getByText('JD').closest('div');
    expect(avatar).toHaveClass('custom-avatar');
  });

  it('renders with status indicator', () => {
    render(<Avatar name="John Doe" status="online" />);
    
    const statusIndicator = screen.getByTestId('avatar-status');
    expect(statusIndicator).toBeInTheDocument();
    expect(statusIndicator).toHaveClass('status-online');
  });

  it('renders with badge', () => {
    render(<Avatar name="John Doe" badge="5" />);
    
    const badge = screen.getByText('5');
    expect(badge).toBeInTheDocument();
    expect(badge.closest('span')).toHaveClass('avatar-badge');
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Avatar name="John Doe" onClick={handleClick} />);
    
    const avatar = screen.getByText('JD').closest('div');
    avatar?.click();
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('is keyboard accessible when clickable', () => {
    const handleClick = jest.fn();
    render(<Avatar name="John Doe" onClick={handleClick} />);
    
    const avatar = screen.getByText('JD').closest('div');
    expect(avatar).toHaveAttribute('tabIndex', '0');
    expect(avatar).toHaveAttribute('role', 'button');
  });

  it('has no accessibility violations', async () => {
    const { container } = render(
      <Avatar
        src="https://example.com/avatar.jpg"
        alt="John Doe"
        name="John Doe"
      />
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('has no accessibility violations with fallback', async () => {
    const { container } = render(<Avatar name="John Doe" />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('generates consistent initials', () => {
    const { rerender } = render(<Avatar name="John Doe" />);
    expect(screen.getByText('JD')).toBeInTheDocument();

    rerender(<Avatar name="jane smith" />);
    expect(screen.getByText('JS')).toBeInTheDocument();

    rerender(<Avatar name="MARY JOHNSON" />);
    expect(screen.getByText('MJ')).toBeInTheDocument();
  });

  it('handles empty name gracefully', () => {
    render(<Avatar name="" />);
    
    expect(screen.getByText('?')).toBeInTheDocument();
  });

  it('handles special characters in name', () => {
    render(<Avatar name="José María" />);
    
    expect(screen.getByText('JM')).toBeInTheDocument();
  });
});