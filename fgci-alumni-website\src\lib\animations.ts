// Animation utilities and configurations

export interface AnimationConfig {
  duration: number;
  delay?: number;
  easing: string;
  fillMode?: 'forwards' | 'backwards' | 'both' | 'none';
}

// Animation presets
export const ANIMATION_PRESETS = {
  // Fade animations
  fadeIn: {
    duration: 300,
    easing: 'ease-out',
    fillMode: 'forwards' as const
  },
  fadeOut: {
    duration: 200,
    easing: 'ease-in',
    fillMode: 'forwards' as const
  },
  
  // Slide animations
  slideInUp: {
    duration: 400,
    easing: 'cubic-bezier(0.16, 1, 0.3, 1)',
    fillMode: 'forwards' as const
  },
  slideInDown: {
    duration: 400,
    easing: 'cubic-bezier(0.16, 1, 0.3, 1)',
    fillMode: 'forwards' as const
  },
  slideInLeft: {
    duration: 400,
    easing: 'cubic-bezier(0.16, 1, 0.3, 1)',
    fillMode: 'forwards' as const
  },
  slideInRight: {
    duration: 400,
    easing: 'cubic-bezier(0.16, 1, 0.3, 1)',
    fillMode: 'forwards' as const
  },
  
  // Scale animations
  scaleIn: {
    duration: 300,
    easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
    fillMode: 'forwards' as const
  },
  scaleOut: {
    duration: 200,
    easing: 'ease-in',
    fillMode: 'forwards' as const
  },
  
  // Bounce animation
  bounce: {
    duration: 600,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    fillMode: 'forwards' as const
  },
  
  // Page transitions
  pageTransition: {
    duration: 500,
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    fillMode: 'forwards' as const
  }
} as const;

// Keyframe definitions
export const KEYFRAMES = {
  fadeIn: [
    { opacity: 0 },
    { opacity: 1 }
  ],
  fadeOut: [
    { opacity: 1 },
    { opacity: 0 }
  ],
  slideInUp: [
    { transform: 'translateY(30px)', opacity: 0 },
    { transform: 'translateY(0)', opacity: 1 }
  ],
  slideInDown: [
    { transform: 'translateY(-30px)', opacity: 0 },
    { transform: 'translateY(0)', opacity: 1 }
  ],
  slideInLeft: [
    { transform: 'translateX(-30px)', opacity: 0 },
    { transform: 'translateX(0)', opacity: 1 }
  ],
  slideInRight: [
    { transform: 'translateX(30px)', opacity: 0 },
    { transform: 'translateX(0)', opacity: 1 }
  ],
  scaleIn: [
    { transform: 'scale(0.8)', opacity: 0 },
    { transform: 'scale(1)', opacity: 1 }
  ],
  scaleOut: [
    { transform: 'scale(1)', opacity: 1 },
    { transform: 'scale(0.8)', opacity: 0 }
  ],
  bounce: [
    { transform: 'scale(0.3)', opacity: 0 },
    { transform: 'scale(1.05)', opacity: 0.8 },
    { transform: 'scale(0.9)', opacity: 0.9 },
    { transform: 'scale(1)', opacity: 1 }
  ],
  pulse: [
    { transform: 'scale(1)' },
    { transform: 'scale(1.05)' },
    { transform: 'scale(1)' }
  ],
  shake: [
    { transform: 'translateX(0)' },
    { transform: 'translateX(-10px)' },
    { transform: 'translateX(10px)' },
    { transform: 'translateX(-10px)' },
    { transform: 'translateX(10px)' },
    { transform: 'translateX(-5px)' },
    { transform: 'translateX(5px)' },
    { transform: 'translateX(0)' }
  ]
} as const;

// Animation utility functions
export function animate(
  element: HTMLElement,
  keyframes: Keyframe[],
  config: AnimationConfig
): Animation {
  return element.animate(keyframes, {
    duration: config.duration,
    delay: config.delay || 0,
    easing: config.easing,
    fill: config.fillMode || 'forwards'
  });
}

export function animateWithPreset(
  element: HTMLElement,
  preset: keyof typeof ANIMATION_PRESETS,
  delay?: number
): Animation {
  const config = { ...ANIMATION_PRESETS[preset], delay };
  const keyframes = KEYFRAMES[preset as keyof typeof KEYFRAMES];
  
  return animate(element, keyframes, config);
}

// Intersection Observer for scroll animations
export function createScrollAnimationObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '-10% 0px -10% 0px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver(callback, defaultOptions);
}

// Stagger animation utility
export function staggerAnimation(
  elements: HTMLElement[],
  preset: keyof typeof ANIMATION_PRESETS,
  staggerDelay: number = 100
): Animation[] {
  return elements.map((element, index) => 
    animateWithPreset(element, preset, index * staggerDelay)
  );
}

// Reduced motion check
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

// Safe animation wrapper that respects reduced motion
export function safeAnimate(
  element: HTMLElement,
  keyframes: Keyframe[],
  config: AnimationConfig
): Animation | null {
  if (prefersReducedMotion()) {
    // Apply final state immediately for reduced motion
    const finalFrame = keyframes[keyframes.length - 1];
    Object.assign(element.style, finalFrame);
    return null;
  }
  
  return animate(element, keyframes, config);
}

// Page transition utilities
export interface PageTransitionConfig {
  enter: keyof typeof ANIMATION_PRESETS;
  exit: keyof typeof ANIMATION_PRESETS;
  duration?: number;
}

export const PAGE_TRANSITIONS: Record<string, PageTransitionConfig> = {
  fade: {
    enter: 'fadeIn',
    exit: 'fadeOut'
  },
  slide: {
    enter: 'slideInRight',
    exit: 'slideInLeft'
  },
  scale: {
    enter: 'scaleIn',
    exit: 'scaleOut'
  }
};

// Hover effect utilities
export function addHoverEffect(
  element: HTMLElement,
  hoverKeyframes: Keyframe[],
  config: Partial<AnimationConfig> = {}
): void {
  const defaultConfig: AnimationConfig = {
    duration: 200,
    easing: 'ease-out',
    fillMode: 'forwards'
  };
  
  const animationConfig = { ...defaultConfig, ...config };
  let hoverAnimation: Animation | null = null;
  
  element.addEventListener('mouseenter', () => {
    if (prefersReducedMotion()) return;
    
    hoverAnimation?.cancel();
    hoverAnimation = animate(element, hoverKeyframes, animationConfig);
  });
  
  element.addEventListener('mouseleave', () => {
    if (prefersReducedMotion()) return;
    
    hoverAnimation?.cancel();
    // Reverse the animation
    const reverseKeyframes = [...hoverKeyframes].reverse();
    hoverAnimation = animate(element, reverseKeyframes, animationConfig);
  });
}

// Loading animation utilities
export function createLoadingAnimation(element: HTMLElement): Animation | null {
  if (prefersReducedMotion()) return null;
  
  return animate(element, KEYFRAMES.pulse, {
    duration: 1000,
    easing: 'ease-in-out',
    fillMode: 'forwards'
  });
}

// Success/Error animation utilities
export function playSuccessAnimation(element: HTMLElement): Animation | null {
  if (prefersReducedMotion()) return null;
  
  return animateWithPreset(element, 'bounce');
}

export function playErrorAnimation(element: HTMLElement): Animation | null {
  if (prefersReducedMotion()) return null;
  
  return animate(element, KEYFRAMES.shake, {
    duration: 500,
    easing: 'ease-in-out',
    fillMode: 'forwards'
  });
}
