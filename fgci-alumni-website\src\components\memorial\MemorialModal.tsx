'use client';

import { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { Memorial } from '@/lib/types';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { CondolenceForm } from './CondolenceForm';

interface MemorialModalProps {
  memorial: Memorial;
  isOpen: boolean;
  onClose: () => void;
}

export function MemorialModal({ memorial, isOpen, onClose }: MemorialModalProps) {
  const [showCondolenceForm, setShowCondolenceForm] = useState(false);
  const { person, dateOfPassing, tribute, photos, condolences } = memorial;

  const passingDate = new Date(dateOfPassing).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="xl"
      title={`In Memory of ${person.name}`}
      className="max-h-[90vh] overflow-y-auto"
    >
      <div className="space-y-8">
        {/* Header Section */}
        <div className="text-center border-b border-gray-200 pb-6">
          <div className="mb-4">
            {person.photo ? (
              <div className="w-32 h-32 mx-auto rounded-full overflow-hidden border-4 border-gray-300 shadow-lg">
                <Image
                  src={person.photo}
                  alt={`${person.name} memorial photo`}
                  width={128}
                  height={128}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-32 h-32 mx-auto rounded-full bg-gray-300 border-4 border-gray-400 flex items-center justify-center">
                <svg className="w-16 h-16 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
            )}
          </div>
          
          <h2 className="text-2xl font-serif font-bold text-gray-900 mb-2">
            {person.name}
          </h2>
          
          <div className="space-y-1 text-gray-600">
            <p className="text-lg">Class of {person.graduationYear}</p>
            {person.role && <p className="italic">{person.role}</p>}
            {person.chapter && <p>{person.chapter}</p>}
          </div>
          
          <div className="mt-4 pt-4 border-t border-gray-100">
            <p className="text-gray-500">
              <span className="font-medium">Passed away:</span> {passingDate}
            </p>
          </div>
        </div>

        {/* Biographical Information */}
        {person.bio && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">About {person.name}</h3>
            <p className="text-gray-700 leading-relaxed">{person.bio}</p>
          </div>
        )}

        {/* Tribute Section */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Tribute</h3>
          <div className="bg-gray-50 p-6 rounded-lg border-l-4 border-gray-400">
            <p className="text-gray-700 leading-relaxed italic">{tribute}</p>
          </div>
        </div>

        {/* Photo Gallery */}
        {photos && photos.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Memorial Photos</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {photos.map((photo, index) => (
                <div key={index} className="aspect-square rounded-lg overflow-hidden bg-gray-200">
                  <Image
                    src={photo}
                    alt={`Memorial photo ${index + 1} of ${person.name}`}
                    width={200}
                    height={200}
                    className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Condolences Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">
              Condolences ({condolences?.filter(c => c.approved).length || 0})
            </h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCondolenceForm(!showCondolenceForm)}
              className="text-gray-600 border-gray-300 hover:border-gray-400"
            >
              {showCondolenceForm ? 'Cancel' : 'Leave Condolence'}
            </Button>
          </div>

          {/* Condolence Form */}
          <AnimatePresence>
            {showCondolenceForm && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mb-6"
              >
                <CondolenceForm
                  memorialId={memorial.id}
                  onSubmit={() => setShowCondolenceForm(false)}
                  onCancel={() => setShowCondolenceForm(false)}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Existing Condolences */}
          <div className="space-y-4">
            {condolences?.filter(c => c.approved).map((condolence) => (
              <div key={condolence.id} className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{condolence.name}</h4>
                  <span className="text-sm text-gray-500">
                    {new Date(condolence.date).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-gray-700 leading-relaxed">{condolence.message}</p>
              </div>
            ))}
            
            {(!condolences || condolences.filter(c => c.approved).length === 0) && (
              <div className="text-center py-8 text-gray-500">
                <p>No condolences yet. Be the first to share your memories.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
}