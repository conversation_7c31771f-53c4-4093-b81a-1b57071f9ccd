import { <PERSON>ada<PERSON>Route } from 'next';
import { SITE_CONFIG } from '@/lib/seo';

// Mock data fetching functions (in real app, these would fetch from your API)
async function getChapters() {
  // In production, this would fetch from your API
  return [
    { slug: 'lagos-chapter', lastModified: '2024-01-15' },
    { slug: 'abuja-chapter', lastModified: '2024-01-10' },
    { slug: 'port-harcourt-chapter', lastModified: '2024-01-08' },
    { slug: 'kano-chapter', lastModified: '2024-01-05' },
    { slug: 'ibadan-chapter', lastModified: '2024-01-03' },
    { slug: 'enugu-chapter', lastModified: '2024-01-01' },
    { slug: 'kaduna-chapter', lastModified: '2023-12-28' },
    { slug: 'jos-chapter', lastModified: '2023-12-25' },
    { slug: 'calabar-chapter', lastModified: '2023-12-20' },
    { slug: 'warri-chapter', lastModified: '2023-12-15' },
    { slug: 'london-chapter', lastModified: '2023-12-10' },
    { slug: 'new-york-chapter', lastModified: '2023-12-05' },
    { slug: 'toronto-chapter', lastModified: '2023-12-01' },
    { slug: 'dubai-chapter', lastModified: '2023-11-28' },
    { slug: 'johannesburg-chapter', lastModified: '2023-11-25' }
  ];
}

async function getSets() {
  // In production, this would fetch from your API
  const currentYear = new Date().getFullYear();
  const sets = [];
  
  // Generate sets from 1980 to current year - 6 (assuming 6 years for secondary school completion)
  for (let year = 1980; year <= currentYear - 6; year++) {
    sets.push({
      slug: `class-of-${year}`,
      lastModified: '2024-01-01' // In real app, this would be the actual last modified date
    });
  }
  
  return sets;
}

async function getEvents() {
  // In production, this would fetch from your API
  return [
    { slug: 'annual-reunion-2024', lastModified: '2024-01-20' },
    { slug: 'lagos-chapter-networking-2024', lastModified: '2024-01-18' },
    { slug: 'scholarship-fundraiser-2024', lastModified: '2024-01-15' },
    { slug: 'homecoming-2024', lastModified: '2024-01-12' },
    { slug: 'career-mentorship-program-2024', lastModified: '2024-01-10' }
  ];
}

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = SITE_CONFIG.url;
  const currentDate = new Date().toISOString();

  // Static pages with SSG
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/chapters`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/sets`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/events`,
      lastModified: currentDate,
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/gallery`,
      lastModified: currentDate,
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/excobot`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/roll-of-honour`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/chapter-excos`,
      lastModified: currentDate,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: currentDate,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
  ];

  try {
    // Fetch dynamic content
    const [chapters, sets, events] = await Promise.all([
      getChapters(),
      getSets(),
      getEvents()
    ]);

    // Chapter pages (ISR)
    const chapterPages: MetadataRoute.Sitemap = chapters.map((chapter) => ({
      url: `${baseUrl}/chapters/${chapter.slug}`,
      lastModified: new Date(chapter.lastModified).toISOString(),
      changeFrequency: 'monthly',
      priority: 0.6,
    }));

    // Set pages (ISR)
    const setPages: MetadataRoute.Sitemap = sets.map((set) => ({
      url: `${baseUrl}/sets/${set.slug}`,
      lastModified: new Date(set.lastModified).toISOString(),
      changeFrequency: 'monthly',
      priority: 0.6,
    }));

    // Event pages (ISR)
    const eventPages: MetadataRoute.Sitemap = events.map((event) => ({
      url: `${baseUrl}/events/${event.slug}`,
      lastModified: new Date(event.lastModified).toISOString(),
      changeFrequency: 'weekly',
      priority: 0.7,
    }));

    return [...staticPages, ...chapterPages, ...setPages, ...eventPages];
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return static pages only if dynamic content fails
    return staticPages;
  }
}
