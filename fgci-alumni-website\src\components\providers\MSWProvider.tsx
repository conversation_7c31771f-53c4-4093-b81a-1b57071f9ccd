'use client';

import { useEffect, useState } from 'react';

interface MSWProviderProps {
  children: React.ReactNode;
}

export function MSWProvider({ children }: MSWProviderProps) {
  const [mswReady, setMswReady] = useState(false);

  useEffect(() => {
    const initMSW = async () => {
      if (
        process.env.NODE_ENV === 'development' && 
        process.env.NEXT_PUBLIC_USE_MOCKS === 'true'
      ) {
        try {
          const { enableMocking } = await import('../../mocks/browser');
          await enableMocking();
        } catch (error) {
          console.error('Failed to initialize MSW:', error);
        }
      }
      setMswReady(true);
    };

    initMSW();
  }, []);

  // Show loading state while MSW is initializing in mock mode
  if (
    process.env.NODE_ENV === 'development' && 
    process.env.NEXT_PUBLIC_USE_MOCKS === 'true' && 
    !mswReady
  ) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-maroon-200 border-t-maroon-600 mx-auto"></div>
          <p className="text-gray-600">Initializing mock API...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}