import { ImageResponse } from 'next/og';
import { NextRequest } from 'next/server';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Get parameters from URL
    const title = searchParams.get('title') || 'FGC Ikom Alumni Association';
    const description = searchParams.get('description') || 'Connect with fellow alumni, explore chapters, sets, events, and galleries.';
    const type = searchParams.get('type') || 'default'; // chapter, set, event, etc.
    
    // Load fonts - using fallbacks if fonts are not available
    let interSemiBoldData = null;
    let playfairBoldData = null;
    
    try {
      // Try to load Inter font
      const interResponse = await fetch(
        new URL('../../../assets/fonts/Inter-SemiBold.ttf', import.meta.url)
      );
      if (interResponse.ok) {
        interSemiBoldData = await interResponse.arrayBuffer();
      }
    } catch {
      // Silently fail if font not found
    }
    
    try {
      // Try to load Playfair Display font
      const playfairResponse = await fetch(
        new URL('../../../assets/fonts/PlayfairDisplay-Bold.ttf', import.meta.url)
      );
      if (playfairResponse.ok) {
        playfairBoldData = await playfairResponse.arrayBuffer();
      }
    } catch {
      // Silently fail if font not found
    }

    return new ImageResponse(
      (
        <div
          style={{
            height: '100%',
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#7B0F17', // Maroon background
            backgroundImage: 'linear-gradient(135deg, #7B0F17 0%, #5C0B12 100%)',
            position: 'relative',
          }}
        >
          {/* Background Pattern */}
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23D4A017' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              opacity: 0.3,
            }}
          />

          {/* Content Container */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '80px',
              textAlign: 'center',
              zIndex: 1,
            }}
          >
            {/* Logo/Icon */}
            <div
              style={{
                width: '120px',
                height: '120px',
                backgroundColor: '#D4A017',
                borderRadius: '60px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '40px',
                fontSize: '48px',
                fontWeight: 'bold',
                color: '#7B0F17',
              }}
            >
              FGC
            </div>

            {/* Title */}
            <h1
              style={{
                fontSize: type === 'chapter' || type === 'set' ? '56px' : '64px',
                fontWeight: 'bold',
                color: 'white',
                marginBottom: '24px',
                lineHeight: 1.1,
                fontFamily: playfairBoldData ? 'Playfair Display' : 'serif',
                maxWidth: '900px',
              }}
            >
              {title}
            </h1>

            {/* Description */}
            {description && (
              <p
                style={{
                  fontSize: '28px',
                  color: '#F7E6C0',
                  marginBottom: '32px',
                  lineHeight: 1.4,
                  fontFamily: interSemiBoldData ? 'Inter' : 'sans-serif',
                  maxWidth: '800px',
                }}
              >
                {description}
              </p>
            )}

            {/* Type Badge */}
            {type !== 'default' && (
              <div
                style={{
                  backgroundColor: '#D4A017',
                  color: '#7B0F17',
                  padding: '12px 24px',
                  borderRadius: '24px',
                  fontSize: '20px',
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  letterSpacing: '1px',
                  fontFamily: interSemiBoldData ? 'Inter' : 'sans-serif',
                }}
              >
                {type}
              </div>
            )}
          </div>

          {/* Bottom Brand */}
          <div
            style={{
              position: 'absolute',
              bottom: '40px',
              left: '80px',
              right: '80px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                color: '#F7E6C0',
                fontSize: '20px',
                fontFamily: interSemiBoldData ? 'Inter' : 'sans-serif',
              }}
            >
              FGC Ikom Alumni Association
            </div>
            <div
              style={{
                color: '#D4A017',
                fontSize: '18px',
                fontFamily: interSemiBoldData ? 'Inter' : 'sans-serif',
              }}
            >
              www.fgcikomalumni.org.ng
            </div>
          </div>
        </div>
      ),
      {
        width: 1200,
        height: 630,
        fonts: [
          ...(interSemiBoldData
            ? [
                {
                  name: 'Inter',
                  data: interSemiBoldData,
                  style: 'normal',
                  weight: 600,
                },
              ]
            : []),
          ...(playfairBoldData
            ? [
                {
                  name: 'Playfair Display',
                  data: playfairBoldData,
                  style: 'normal',
                  weight: 700,
                },
              ]
            : []),
        ],
      }
    );
  } catch (e: any) {
    console.log(`Failed to generate OG image: ${e.message}`);
    return new Response(`Failed to generate the image`, {
      status: 500,
    });
  }
}