'use client';

import { useEffect, useState } from 'react';
import { 
  measureWebVitals, 
  checkPerformanceBudget, 
  getMemoryUsage,
  type PerformanceMetrics 
} from '@/lib/performance';

interface PerformanceMonitorProps {
  enabled?: boolean;
  showInDevelopment?: boolean;
}

export function PerformanceMonitor({ 
  enabled = true, 
  showInDevelopment = true 
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [budgetStatus, setBudgetStatus] = useState<{ passed: boolean; violations: string[] } | null>(null);
  const [memoryInfo, setMemoryInfo] = useState<MemoryInfo | null>(null);

  useEffect(() => {
    if (!enabled) return;
    
    // Only show in development if explicitly enabled
    if (process.env.NODE_ENV === 'production' || showInDevelopment) {
      // Measure Web Vitals
      measureWebVitals().then((webVitals) => {
        setMetrics(webVitals);
        
        // Check performance budget
        const budget = checkPerformanceBudget(webVitals);
        setBudgetStatus(budget);
        
        // Log performance metrics
        console.group('🚀 Performance Metrics');
        console.log('First Contentful Paint (FCP):', `${webVitals.fcp.toFixed(2)}ms`);
        console.log('Largest Contentful Paint (LCP):', `${webVitals.lcp.toFixed(2)}ms`);
        console.log('First Input Delay (FID):', `${webVitals.fid.toFixed(2)}ms`);
        console.log('Cumulative Layout Shift (CLS):', webVitals.cls.toFixed(4));
        console.log('Time to First Byte (TTFB):', `${webVitals.ttfb.toFixed(2)}ms`);
        
        if (!budget.passed) {
          console.warn('⚠️ Performance Budget Violations:', budget.violations);
        } else {
          console.log('✅ All performance budgets passed!');
        }
        console.groupEnd();
      });

      // Monitor memory usage
      const memory = getMemoryUsage();
      if (memory) {
        setMemoryInfo(memory);
        console.log('💾 Memory Usage:', {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`
        });
      }
    }
  }, [enabled, showInDevelopment]);

  // Don't render anything in production unless explicitly enabled
  if (process.env.NODE_ENV === 'production' && !enabled) {
    return null;
  }

  // Don't render in development unless explicitly enabled
  if (process.env.NODE_ENV === 'development' && !showInDevelopment) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      {metrics && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 text-xs">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-900">Performance</h3>
            <div className={`w-2 h-2 rounded-full ${budgetStatus?.passed ? 'bg-green-500' : 'bg-red-500'}`} />
          </div>
          
          <div className="space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-600">FCP:</span>
              <span className={metrics.fcp > 1800 ? 'text-red-600' : 'text-green-600'}>
                {metrics.fcp.toFixed(0)}ms
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">LCP:</span>
              <span className={metrics.lcp > 2500 ? 'text-red-600' : 'text-green-600'}>
                {metrics.lcp.toFixed(0)}ms
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">FID:</span>
              <span className={metrics.fid > 100 ? 'text-red-600' : 'text-green-600'}>
                {metrics.fid.toFixed(0)}ms
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">CLS:</span>
              <span className={metrics.cls > 0.1 ? 'text-red-600' : 'text-green-600'}>
                {metrics.cls.toFixed(3)}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">TTFB:</span>
              <span className={metrics.ttfb > 600 ? 'text-red-600' : 'text-green-600'}>
                {metrics.ttfb.toFixed(0)}ms
              </span>
            </div>
          </div>

          {memoryInfo && (
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="flex justify-between">
                <span className="text-gray-600">Memory:</span>
                <span className="text-gray-900">
                  {(memoryInfo.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB
                </span>
              </div>
            </div>
          )}

          {budgetStatus && !budgetStatus.passed && (
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="text-red-600 text-xs">
                {budgetStatus.violations.length} violation(s)
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default PerformanceMonitor;
