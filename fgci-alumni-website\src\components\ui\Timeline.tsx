'use client';

import { Activity } from '../../lib/types';
import { Calendar, MapPin, Camera } from 'lucide-react';
import { format } from 'date-fns';
import Image from 'next/image';

export interface TimelineProps {
  activities: Activity[];
  variant?: 'vertical' | 'horizontal';
  showDates?: boolean;
  interactive?: boolean;
  className?: string;
  loading?: boolean;
  error?: string;
  onRetry?: () => void;
  onActivityClick?: (activity: Activity) => void;
  groupByDate?: boolean;
}

export function Timeline({
  activities,
  variant = 'vertical',
  showDates = true,
  interactive = false,
  className = '',
  loading = false,
  error,
  onRetry,
  onActivityClick,
  groupByDate = false,
}: TimelineProps) {
  if (loading) {
    return (
      <div data-testid="timeline-skeleton" className="space-y-4">
        <div className="h-6 bg-gray-200 rounded" />
        <div className="h-6 bg-gray-200 rounded" />
        <div className="h-6 bg-gray-200 rounded" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-gray-500">{error}</p>
        <button onClick={onRetry} disabled={!onRetry} className="mt-3 px-4 py-2 rounded bg-blue-600 text-white disabled:opacity-50">Retry</button>
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-8">
        <Calendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
        <p className="text-gray-500">No activities to display</p>
      </div>
    );
  }

  // Sort activities by date (most recent first)
  const sortedActivities = [...activities].sort((a, b) => 
    new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'meeting':
        return '👥';
      case 'event':
        return '🎉';
      case 'fundraiser':
        return '💰';
      case 'social':
        return '🎊';
      case 'memorial':
        return '🕊️';
      default:
        return '📅';
    }
  };

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'meeting':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'event':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'fundraiser':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'social':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'memorial':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (variant === 'horizontal') {
    return (
      <div data-testid="timeline" className={`overflow-x-auto timeline-horizontal ${className}`}>
        <div className="flex space-x-6 pb-4 min-w-max">
          {sortedActivities.map((activity, index) => (
            <div
              key={activity.id}
              className={`flex-shrink-0 w-80 ${interactive ? 'cursor-pointer hover:shadow-lg transition-shadow' : ''}`}
            >
              <div className="bg-white rounded-lg border border-gray-200 p-4 h-full" tabIndex={interactive ? 0 : undefined}>
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div data-testid={`activity-icon-${activity.type}`} className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${getActivityColor(activity.type)}`}>
                      {getActivityIcon(activity.type)}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    {showDates && (
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <Calendar className="w-4 h-4 mr-1" />
                        {format(new Date(activity.date), 'MMM dd, yyyy')}
                      </div>
                    )}
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {activity.title}
                    </h4>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-3">
                      {activity.description}
                    </p>
                    {activity.location && (
                      <div className="flex items-center text-sm text-gray-500 mb-2">
                        <MapPin className="w-4 h-4 mr-1" />
                        {activity.location}
                      </div>
                    )}
                    {activity.photos && activity.photos.length > 0 && (
                      <div data-testid="photo-indicator" className="flex items-center text-sm text-gray-500">
                        <Camera className="w-4 h-4 mr-1" />
                        {activity.photos.length} photo{activity.photos.length !== 1 ? 's' : ''}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div data-testid="timeline" role="list" className={`space-y-6 timeline-vertical ${className}`}>
      {sortedActivities.map((activity, index) => (
        <div
          role="listitem"
          key={activity.id}
          className={`relative ${interactive ? 'cursor-pointer' : ''}`}
          tabIndex={0}
          onKeyDown={(e) => {
            if (!interactive) return;
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              onActivityClick?.(activity);
            }
          }}
          onClick={() => {
            if (!interactive) return;
            onActivityClick?.(activity);
          }}
        >
          {/* Timeline Line */}
          {index < sortedActivities.length - 1 && (
            <div className="absolute left-5 top-12 w-0.5 h-16 bg-gray-200" />
          )}
          
          <div className="flex items-start space-x-4">
            {/* Timeline Dot */}
            <div className="flex-shrink-0">
              <div data-testid={`activity-icon-${activity.type}`} className={`w-10 h-10 rounded-full flex items-center justify-center text-lg border-2 ${getActivityColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
            </div>
            
            {/* Content */}
            <div className={`flex-1 bg-white rounded-lg border border-gray-200 p-6 ${interactive ? 'hover:shadow-lg transition-shadow' : ''}`} tabIndex={interactive ? 0 : undefined}>
              <div className="flex items-start justify-between mb-3">
                <div tabIndex={interactive ? 0 : undefined}>
                  <h4 className="text-lg font-semibold text-gray-900 mb-1">
                    {activity.title}
                  </h4>
                  {showDates && (!groupByDate || index === 0 || format(new Date(activity.date), 'MMM dd, yyyy') !== format(new Date(sortedActivities[index - 1]?.date ?? activity.date), 'MMM dd, yyyy')) && (
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="w-4 h-4 mr-1" />
                      {format(new Date(activity.date), 'MMM dd, yyyy')}
                    </div>
                  )}
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getActivityColor(activity.type)}`}>
                  {activity.type}
                </span>
              </div>
              
              <p className="text-gray-600 mb-4">
                {activity.description}
              </p>
              
              <div className="flex items-center justify-between">
                {activity.location && (
                  <div className="flex items-center text-sm text-gray-500">
                    <MapPin className="w-4 h-4 mr-1" />
                    {activity.location}
                  </div>
                )}
                
                {activity.photos && activity.photos.length > 0 && (
                  <div data-testid="photo-indicator" className="flex items-center text-sm text-gray-500">
                    <Camera className="w-4 h-4 mr-1" />
                    {activity.photos.length} photo{activity.photos.length !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
              
              {/* Photo Preview */}
              {activity.photos && activity.photos.length > 0 && (
                <div className="mt-4 flex space-x-2 overflow-x-auto">
                  {activity.photos.slice(0, 3).map((photo, photoIndex) => (
                    <div key={photoIndex} className="flex-shrink-0">
                      <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-gray-100">
                        <Image
                          src={photo}
                          alt={`${activity.title} photo ${photoIndex + 1}`}
                          fill
                          className="object-cover"
                          sizes="80px"
                        />
                      </div>
                    </div>
                  ))}
                  {activity.photos.length > 3 && (
                    <div className="flex-shrink-0 w-20 h-20 rounded-lg bg-gray-100 flex items-center justify-center text-sm text-gray-500">
                      +{activity.photos.length - 3}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}