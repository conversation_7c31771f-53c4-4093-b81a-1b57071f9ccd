import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  imageCount: number;
  memoryUsage?: number;
}

interface UsePerformanceMonitorOptions {
  enabled?: boolean;
  onMetrics?: (metrics: PerformanceMetrics) => void;
}

export function usePerformanceMonitor({
  enabled = process.env.NODE_ENV === 'development',
  onMetrics
}: UsePerformanceMonitorOptions = {}) {
  const startTimeRef = useRef<number>();
  const renderStartRef = useRef<number>();
  const imageCountRef = useRef(0);

  useEffect(() => {
    if (!enabled) return;

    startTimeRef.current = performance.now();
    renderStartRef.current = performance.now();

    return () => {
      if (startTimeRef.current && renderStartRef.current) {
        const loadTime = performance.now() - startTimeRef.current;
        const renderTime = performance.now() - renderStartRef.current;
        
        const metrics: PerformanceMetrics = {
          loadTime,
          renderTime,
          imageCount: imageCountRef.current,
          memoryUsage: (performance as any).memory?.usedJSHeapSize
        };

        onMetrics?.(metrics);

        if (process.env.NODE_ENV === 'development') {
          console.group('🎯 Gallery Performance Metrics');
          console.log(`⏱️ Load Time: ${loadTime.toFixed(2)}ms`);
          console.log(`🎨 Render Time: ${renderTime.toFixed(2)}ms`);
          console.log(`🖼️ Images Loaded: ${imageCountRef.current}`);
          if (metrics.memoryUsage) {
            console.log(`💾 Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
          }
          console.groupEnd();
        }
      }
    };
  }, [enabled, onMetrics]);

  const incrementImageCount = () => {
    imageCountRef.current += 1;
  };

  const resetImageCount = () => {
    imageCountRef.current = 0;
  };

  return {
    incrementImageCount,
    resetImageCount
  };
}

// Hook for monitoring Core Web Vitals
export function useCoreWebVitals() {
  useEffect(() => {
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') return;

    // Dynamically import web-vitals to avoid SSR issues
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(console.log);
      getFID(console.log);
      getFCP(console.log);
      getLCP(console.log);
      getTTFB(console.log);
    }).catch(() => {
      // web-vitals not available, skip monitoring
    });
  }, []);
}

// Hook for monitoring image loading performance
export function useImageLoadingMetrics() {
  const metricsRef = useRef({
    totalImages: 0,
    loadedImages: 0,
    failedImages: 0,
    averageLoadTime: 0,
    loadTimes: [] as number[]
  });

  const recordImageLoad = (loadTime: number, success: boolean = true) => {
    const metrics = metricsRef.current;
    
    if (success) {
      metrics.loadedImages += 1;
      metrics.loadTimes.push(loadTime);
      metrics.averageLoadTime = metrics.loadTimes.reduce((a, b) => a + b, 0) / metrics.loadTimes.length;
    } else {
      metrics.failedImages += 1;
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`📸 Image ${success ? 'loaded' : 'failed'} in ${loadTime.toFixed(2)}ms`);
      console.log(`📊 Success rate: ${((metrics.loadedImages / (metrics.loadedImages + metrics.failedImages)) * 100).toFixed(1)}%`);
    }
  };

  const getMetrics = () => ({ ...metricsRef.current });

  const resetMetrics = () => {
    metricsRef.current = {
      totalImages: 0,
      loadedImages: 0,
      failedImages: 0,
      averageLoadTime: 0,
      loadTimes: []
    };
  };

  return {
    recordImageLoad,
    getMetrics,
    resetMetrics
  };
}