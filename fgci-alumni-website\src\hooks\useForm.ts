'use client';

import { useState, useCallback } from 'react';
import { ValidationSchema, FormState, FormErrors } from '@/lib/types';
import { validateField } from '@/lib/validation';

interface UseFormOptions<T> {
  initialData: T;
  validationSchema: ValidationSchema;
  onSubmit?: (data: T) => void | Promise<void>;
}

export function useForm<T extends Record<string, unknown>>({
  initialData,
  validationSchema,
  onSubmit
}: UseFormOptions<T>) {
  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validate a single field
  const validateSingleField = useCallback((fieldName: keyof T, value: unknown): string[] => {
    const fieldRules = validationSchema[fieldName as string];
    if (!fieldRules) return [];
    
    return validateField(value, fieldRules, fieldName as string);
  }, [validationSchema]);

  // Validate all fields
  const validateAllFields = useCallback((): { isValid: boolean; errors: FormErrors } => {
    const newErrors: FormErrors = {};
    let isValid = true;

    Object.keys(validationSchema).forEach((fieldName) => {
      const fieldErrors = validateSingleField(fieldName as keyof T, data[fieldName as keyof T]);
      if (fieldErrors.length > 0) {
        newErrors[fieldName] = fieldErrors;
        isValid = false;
      }
    });

    return { isValid, errors: newErrors };
  }, [data, validationSchema, validateSingleField]);

  // Handle field change
  const handleChange = useCallback((
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = event.target;
    
    // Handle different input types
    let processedValue: unknown = value;
    if (type === 'checkbox') {
      processedValue = (event.target as HTMLInputElement).checked;
    } else if (type === 'number') {
      processedValue = value === '' ? '' : Number(value);
    }

    setData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  }, [errors]);

  // Handle field blur (for validation)
  const handleBlur = useCallback((
    event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = event.target;
    
    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field
    const fieldErrors = validateSingleField(name as keyof T, value);
    setErrors(prev => ({
      ...prev,
      [name]: fieldErrors
    }));
  }, [validateSingleField]);

  // Handle form submission
  const handleSubmit = useCallback((
    submitHandler?: (data: T) => void | Promise<void>
  ) => {
    return async (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      
      // Mark all fields as touched
      const allTouched = Object.keys(data).reduce((acc, key) => {
        acc[key as keyof T] = true;
        return acc;
      }, {} as Record<keyof T, boolean>);
      setTouched(allTouched);

      // Validate all fields
      const { isValid, errors: validationErrors } = validateAllFields();
      setErrors(validationErrors);

      if (!isValid) {
        return;
      }

      setIsSubmitting(true);
      
      try {
        const handler = submitHandler || onSubmit;
        if (handler) {
          await handler(data);
        }
      } catch (error) {
        console.error('Form submission error:', error);
        // You might want to set a general error state here
      } finally {
        setIsSubmitting(false);
      }
    };
  }, [data, validateAllFields, onSubmit]);

  // Set field value programmatically
  const setFieldValue = useCallback((fieldName: keyof T, value: unknown) => {
    setData(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // Clear field error
    if (errors[fieldName as string]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName as string];
        return newErrors;
      });
    }
  }, [errors]);

  // Set field error programmatically
  const setFieldError = useCallback((fieldName: keyof T, error: string | string[]) => {
    const errorArray = Array.isArray(error) ? error : [error];
    setErrors(prev => ({
      ...prev,
      [fieldName as string]: errorArray
    }));
  }, []);

  // Reset form
  const reset = useCallback((newData?: Partial<T>) => {
    setData(newData ? { ...initialData, ...newData } : initialData);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
    setIsSubmitting(false);
  }, [initialData]);

  // Check if form is valid
  const isValid = Object.keys(errors).length === 0 && Object.keys(touched).length > 0;

  // Get field props for easy integration
  const getFieldProps = useCallback((fieldName: keyof T) => ({
    name: fieldName as string,
    value: data[fieldName] || '',
    onChange: handleChange,
    onBlur: handleBlur,
  }), [data, handleChange, handleBlur]);

  return {
    // Form state
    data,
    errors,
    touched,
    isSubmitting,
    isValid,
    
    // Handlers
    handleChange,
    handleBlur,
    handleSubmit,
    
    // Utilities
    setFieldValue,
    setFieldError,
    reset,
    getFieldProps,
    validateField: validateSingleField,
    validateForm: validateAllFields,
  };
}