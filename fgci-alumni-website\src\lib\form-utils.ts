import { ValidationSchema, FormErrors } from './types';

/**
 * Utility functions for form handling and validation
 */

/**
 * Debounce function for validation
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Format form field name for display
 */
export function formatFieldName(fieldName: string): string {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
}

/**
 * Get first error message for a field
 */
export function getFirstError(errors: string[] | undefined): string | undefined {
  return errors && errors.length > 0 ? errors[0] : undefined;
}

/**
 * Check if field has any errors
 */
export function hasFieldError(errors: FormErrors, fieldName: string): boolean {
  return Boolean(errors[fieldName] && errors[fieldName].length > 0);
}

/**
 * Get all error messages as a flat array
 */
export function getAllErrors(errors: FormErrors): string[] {
  return Object.values(errors).flat();
}

/**
 * Count total number of errors
 */
export function getErrorCount(errors: FormErrors): number {
  return getAllErrors(errors).length;
}

/**
 * Generate unique field ID
 */
export function generateFieldId(name: string, prefix = 'field'): string {
  return `${prefix}-${name}`;
}

/**
 * Generate error ID for field
 */
export function generateErrorId(name: string): string {
  return `${generateFieldId(name)}-error`;
}

/**
 * Generate help text ID for field
 */
export function generateHelpId(name: string): string {
  return `${generateFieldId(name)}-help`;
}

/**
 * Generate description ID for field
 */
export function generateDescriptionId(name: string): string {
  return `${generateFieldId(name)}-desc`;
}

/**
 * Create aria-describedby attribute value
 */
export function createAriaDescribedBy(
  fieldName: string,
  hasError: boolean,
  hasHelp: boolean,
  hasDescription: boolean
): string | undefined {
  const ids: string[] = [];
  
  if (hasError) {
    ids.push(generateErrorId(fieldName));
  }
  
  if (hasHelp) {
    ids.push(generateHelpId(fieldName));
  }
  
  if (hasDescription) {
    ids.push(generateDescriptionId(fieldName));
  }
  
  return ids.length > 0 ? ids.join(' ') : undefined;
}

/**
 * Sanitize form data by removing empty strings and undefined values
 */
export function sanitizeFormData<T extends Record<string, unknown>>(data: T): Partial<T> {
  const sanitized: Partial<T> = {};
  
  Object.entries(data).forEach(([key, value]) => {
    if (value !== '' && value !== undefined && value !== null) {
      sanitized[key as keyof T] = value;
    }
  });
  
  return sanitized;
}

/**
 * Convert form data to URL search params
 */
export function formDataToSearchParams<T extends Record<string, unknown>>(
  data: T
): URLSearchParams {
  const params = new URLSearchParams();
  const sanitized = sanitizeFormData(data);
  
  Object.entries(sanitized).forEach(([key, value]) => {
    if (value !== undefined) {
      params.append(key, String(value));
    }
  });
  
  return params;
}

/**
 * Validate required fields
 */
export function validateRequiredFields<T extends Record<string, unknown>>(
  data: T,
  requiredFields: (keyof T)[]
): FormErrors {
  const errors: FormErrors = {};
  
  requiredFields.forEach((field) => {
    const value = data[field];
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      errors[field as string] = [`${formatFieldName(field as string)} is required`];
    }
  });
  
  return errors;
}

/**
 * Merge multiple error objects
 */
export function mergeErrors(...errorObjects: FormErrors[]): FormErrors {
  const merged: FormErrors = {};
  
  errorObjects.forEach((errors) => {
    Object.entries(errors).forEach(([field, fieldErrors]) => {
      if (merged[field]) {
        merged[field] = [...merged[field], ...fieldErrors];
      } else {
        merged[field] = [...fieldErrors];
      }
    });
  });
  
  return merged;
}

/**
 * Create validation summary for screen readers
 */
export function createValidationSummary(errors: FormErrors): string {
  const errorCount = getErrorCount(errors);
  
  if (errorCount === 0) {
    return 'Form is valid';
  }
  
  const errorMessages = getAllErrors(errors);
  const summary = `Form has ${errorCount} error${errorCount > 1 ? 's' : ''}`;
  
  return `${summary}: ${errorMessages.join(', ')}`;
}

/**
 * Focus management utilities
 */
export const focusUtils = {
  /**
   * Focus first field with error
   */
  focusFirstError(errors: FormErrors): void {
    const firstErrorField = Object.keys(errors)[0];
    if (firstErrorField) {
      const element = document.getElementById(generateFieldId(firstErrorField));
      if (element) {
        element.focus();
      }
    }
  },

  /**
   * Focus specific field
   */
  focusField(fieldName: string): void {
    const element = document.getElementById(generateFieldId(fieldName));
    if (element) {
      element.focus();
    }
  },

  /**
   * Scroll to first error
   */
  scrollToFirstError(errors: FormErrors): void {
    const firstErrorField = Object.keys(errors)[0];
    if (firstErrorField) {
      const element = document.getElementById(generateFieldId(firstErrorField));
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.focus();
      }
    }
  },
};

/**
 * Form submission utilities
 */
export const submissionUtils = {
  /**
   * Create form submission payload
   */
  createPayload<T extends Record<string, unknown>>(
    data: T,
    options: {
      sanitize?: boolean;
      includeTimestamp?: boolean;
      includeMetadata?: boolean;
    } = {}
  ): Record<string, unknown> {
    const { sanitize = true, includeTimestamp = false, includeMetadata = false } = options;
    
    let payload: Record<string, unknown> = sanitize ? sanitizeFormData(data) : { ...data };
    
    if (includeTimestamp) {
      payload.submittedAt = new Date().toISOString();
    }
    
    if (includeMetadata) {
      payload._metadata = {
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      };
    }
    
    return payload;
  },

  /**
   * Handle form submission with loading state
   */
  async handleSubmission<T>(
    submitFn: () => Promise<T>,
    options: {
      onStart?: () => void;
      onSuccess?: (result: T) => void;
      onError?: (error: unknown) => void;
      onFinally?: () => void;
    } = {}
  ): Promise<T | null> {
    const { onStart, onSuccess, onError, onFinally } = options;
    
    try {
      onStart?.();
      const result = await submitFn();
      onSuccess?.(result);
      return result;
    } catch (error) {
      onError?.(error);
      return null;
    } finally {
      onFinally?.();
    }
  },
};

/**
 * Accessibility utilities
 */
export const a11yUtils = {
  /**
   * Announce message to screen readers
   */
  announce(message: string, priority: 'polite' | 'assertive' = 'polite'): void {
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', priority);
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = message;
    
    document.body.appendChild(announcer);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcer);
    }, 1000);
  },

  /**
   * Create accessible error message
   */
  createErrorMessage(fieldName: string, errors: string[]): string {
    if (errors.length === 0) return '';
    
    const fieldLabel = formatFieldName(fieldName);
    const errorText = errors.join(', ');
    
    return `${fieldLabel}: ${errorText}`;
  },

  /**
   * Create accessible form status
   */
  createFormStatus(errors: FormErrors, isSubmitting: boolean): string {
    if (isSubmitting) {
      return 'Form is being submitted';
    }
    
    const errorCount = getErrorCount(errors);
    if (errorCount > 0) {
      return `Form has ${errorCount} error${errorCount > 1 ? 's' : ''}`;
    }
    
    return 'Form is ready for submission';
  },
};

/**
 * Character count utilities
 */
export const characterUtils = {
  /**
   * Get character count status
   */
  getCharacterStatus(
    current: number,
    max: number
  ): {
    count: string;
    percentage: number;
    status: 'normal' | 'warning' | 'danger';
  } {
    const percentage = (current / max) * 100;
    let status: 'normal' | 'warning' | 'danger' = 'normal';
    
    if (percentage >= 100) {
      status = 'danger';
    } else if (percentage >= 90) {
      status = 'warning';
    }
    
    return {
      count: `${current}/${max}`,
      percentage,
      status,
    };
  },

  /**
   * Get remaining characters
   */
  getRemainingCharacters(current: number, max: number): number {
    return Math.max(0, max - current);
  },

  /**
   * Create character count message for screen readers
   */
  createCharacterMessage(current: number, max: number): string {
    const remaining = characterUtils.getRemainingCharacters(current, max);
    
    if (remaining === 0) {
      return 'Character limit reached';
    } else if (remaining < 10) {
      return `${remaining} characters remaining`;
    }
    
    return `${current} of ${max} characters used`;
  },
};