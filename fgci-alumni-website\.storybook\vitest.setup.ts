import { expect, beforeAll } from 'vitest';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers for Storybook tests
expect.extend(matchers);

// Setup for Storybook tests
beforeAll(() => {
  // Mock any global objects needed for Storybook tests (browser env)
  (window as any).ResizeObserver = class ResizeObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  };

  (window as any).IntersectionObserver = class IntersectionObserver {
    observe() {}
    unobserve() {}
    disconnect() {}
  };
});