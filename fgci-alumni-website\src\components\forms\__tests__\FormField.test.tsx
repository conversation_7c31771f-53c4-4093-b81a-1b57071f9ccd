import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FormField } from '../FormField';

describe('FormField', () => {
  const defaultProps = {
    label: 'Test Field',
    name: 'testField',
    value: '',
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with basic props', () => {
    render(<FormField {...defaultProps} />);
    
    expect(screen.getByLabelText('Test Field')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('shows required indicator when required', () => {
    render(<FormField {...defaultProps} required />);
    
    expect(screen.getByLabelText('Test Field *')).toBeInTheDocument();
    expect(screen.getByLabelText('required')).toBeInTheDocument();
  });

  it('displays help text when provided', () => {
    const helpText = 'This is help text';
    render(<FormField {...defaultProps} helpText={helpText} />);
    
    expect(screen.getByText(helpText)).toBeInTheDocument();
  });

  it('shows error messages when field has errors and is touched', () => {
    const errors = ['This field is required', 'Must be at least 2 characters'];
    render(
      <FormField 
        {...defaultProps} 
        errors={errors} 
        touched={true} 
      />
    );
    
    errors.forEach(error => {
      expect(screen.getByText(error)).toBeInTheDocument();
    });
    
    // Should have error styling
    const input = screen.getByRole('textbox');
    expect(input).toHaveClass('border-red-500');
  });

  it('does not show errors when field is not touched', () => {
    const errors = ['This field is required'];
    render(
      <FormField 
        {...defaultProps} 
        errors={errors} 
        touched={false} 
      />
    );
    
    expect(screen.queryByText('This field is required')).not.toBeInTheDocument();
  });

  it('calls onChange when input value changes', async () => {
    const user = userEvent.setup();
    const onChange = jest.fn();
    
    render(<FormField {...defaultProps} onChange={onChange} />);
    
    const input = screen.getByRole('textbox');
    await user.type(input, 'test');
    
    expect(onChange).toHaveBeenCalledTimes(4); // Once for each character
  });

  it('calls onBlur when input loses focus', async () => {
    const user = userEvent.setup();
    const onBlur = jest.fn();
    
    render(<FormField {...defaultProps} onBlur={onBlur} />);
    
    const input = screen.getByRole('textbox');
    await user.click(input);
    await user.tab(); // Move focus away
    
    expect(onBlur).toHaveBeenCalledTimes(1);
  });

  it('handles different input types correctly', () => {
    const { rerender } = render(
      <FormField {...defaultProps} type="email" />
    );
    
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'email');
    
    rerender(<FormField {...defaultProps} type="tel" />);
    expect(screen.getByRole('textbox')).toHaveAttribute('type', 'tel');
    
    rerender(<FormField {...defaultProps} type="number" />);
    expect(screen.getByRole('spinbutton')).toHaveAttribute('type', 'number');
  });

  it('sets proper accessibility attributes', () => {
    const errors = ['Error message'];
    render(
      <FormField 
        {...defaultProps} 
        errors={errors} 
        touched={true}
        helpText="Help text"
      />
    );
    
    const input = screen.getByRole('textbox');
    
    expect(input).toHaveAttribute('aria-invalid', 'true');
    expect(input).toHaveAttribute('aria-describedby');
    
    const describedBy = input.getAttribute('aria-describedby');
    expect(describedBy).toContain('error');
  });

  it('disables input when disabled prop is true', () => {
    render(<FormField {...defaultProps} disabled />);
    
    const input = screen.getByRole('textbox');
    expect(input).toBeDisabled();
    expect(input).toHaveClass('disabled:bg-gray-50');
  });

  it('sets min and max attributes for number inputs', () => {
    render(
      <FormField 
        {...defaultProps} 
        type="number" 
        min={0} 
        max={100} 
      />
    );
    
    const input = screen.getByRole('spinbutton');
    expect(input).toHaveAttribute('min', '0');
    expect(input).toHaveAttribute('max', '100');
  });

  it('applies custom className', () => {
    const customClass = 'custom-class';
    render(<FormField {...defaultProps} className={customClass} />);
    
    const container = screen.getByRole('textbox').closest('div');
    expect(container).toHaveClass(customClass);
  });

  it('sets autoComplete attribute', () => {
    render(<FormField {...defaultProps} autoComplete="email" />);
    
    const input = screen.getByRole('textbox');
    expect(input).toHaveAttribute('autocomplete', 'email');
  });
});