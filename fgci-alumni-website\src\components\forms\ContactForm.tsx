'use client';

import React from 'react';
import { Button } from '@/components/ui';
import { FormField, FormTextArea, FormSelect, FormMessage } from './index';
import { useContactForm } from '@/hooks/useContactForm';

const inquiryTypes = [
  { value: 'general', label: 'General Information' },
  { value: 'membership', label: 'Membership Inquiry' },
  { value: 'events', label: 'Events & Activities' },
  { value: 'chapters', label: 'Chapter Information' },
  { value: 'donations', label: 'Donations & Fundraising' },
  { value: 'website', label: 'Website Issues' },
  { value: 'other', label: 'Other' }
];

const chapters = [
  { value: 'lagos', label: 'Lagos Chapter' },
  { value: 'abuja', label: 'Abuja Chapter' },
  { value: 'usa-diaspora', label: 'USA Diaspora Chapter' },
  { value: 'uk-diaspora', label: 'UK Diaspora Chapter' },
  { value: 'port-harcourt', label: 'Port Harcourt Chapter' },
  { value: 'school-based', label: 'School-Based Chapter' },
  { value: 'other', label: 'Other' }
];

interface ContactFormProps {
  className?: string;
}

export function ContactForm({ className }: ContactFormProps) {
  const {
    formData,
    errors,
    touched,
    isSubmitting,
    submitStatus,
    submitMessage,
    handleChange,
    handleBlur,
    handleSubmit,
    beginSubmit,
    getFieldProps,
  } = useContactForm();
  const submitButtonRef = React.useRef<HTMLButtonElement>(null);


  return (
    <div className={className}>
      {/* Success/Error Messages */}
      {submitStatus === 'success' && (
        <FormMessage
          type="success"
          title="Message Sent Successfully!"
          message={submitMessage}
          className="mb-6"
        />
      )}

      {submitStatus === 'error' && (
        <FormMessage
          type="error"
          title="Error Sending Message"
          message={submitMessage}
          className="mb-6"
        />
      )}

      <form onSubmit={handleSubmit()} noValidate className="space-y-6">

        {/* Personal Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Full Name"
            {...getFieldProps('name')}
            required
            autoComplete="name"
            errors={errors.name}
            touched={touched.name}
            helpText="Enter your full name as you'd like to be addressed"
          />

          <FormField
            label="Email Address"
            type="email"
            {...getFieldProps('email')}
            required
            autoComplete="email"
            errors={errors.email}
            touched={touched.email}
            helpText="We'll use this to respond to your inquiry"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Phone Number"
            type="tel"
            {...getFieldProps('phone')}
            autoComplete="tel"
            errors={errors.phone}
            touched={touched.phone}
            helpText="Optional - for urgent inquiries"
            placeholder="+234-XXX-XXX-XXXX"
          />

          <FormField
            label="Graduation Year"
            type="number"
            name="graduationYear"
            value={formData.graduationYear || ''}
            onChange={handleChange}
            onBlur={handleBlur}
            min={1970}
            max={new Date().getFullYear()}
            helpText="Optional - helps us route your inquiry"
            placeholder="e.g. 1995"
            errors={errors.graduationYear}
            touched={touched.graduationYear}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormSelect
            label="Inquiry Type"
            name="inquiryType"
            value={formData.inquiryType || ''}
            onChange={handleChange}
            onBlur={handleBlur}
            options={inquiryTypes}
            required
            errors={errors.inquiryType}
            touched={touched.inquiryType}
            placeholder="Select inquiry type"
            helpText="This helps us route your message to the right person"
          />

          <FormSelect
            label="Chapter Affiliation"
            name="chapter"
            value={formData.chapter || ''}
            onChange={handleChange}
            onBlur={handleBlur}
            options={chapters}
            placeholder="Select your chapter (optional)"
            helpText="Optional - your current or preferred chapter"
          />
        </div>

        <FormField
          label="Subject"
          {...getFieldProps('subject')}
          required
          errors={errors.subject}
          touched={touched.subject}
          helpText="Brief description of your inquiry"
          placeholder="e.g. Question about upcoming events"
        />

        <FormTextArea
          label="Message"
          {...getFieldProps('message')}
          required
          rows={6}
          maxLength={2000}
          showCharCount
          errors={errors.message}
          touched={touched.message}
          helpText="Please provide details about your inquiry"
          placeholder="Tell us how we can help you..."
        />

        <div className="pt-4">
          <button
            ref={submitButtonRef}
            type="submit"
            className="btn-base focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 bg-maroon-700 text-white hover:bg-maroon-800 active:bg-maroon-900 disabled:bg-gray-300 px-6 py-3 text-lg gap-2.5 w-full"
            disabled={isSubmitting}
            aria-disabled={isSubmitting}
            onClick={() => beginSubmit()}
          >
            {isSubmitting ? 'Sending Message...' : 'Send Message'}
          </button>
        </div>

        <div className="text-sm text-gray-600 text-center">
          <p>
            We typically respond within 24-48 hours. For urgent matters,
            please call us directly at{' '}
            <a
              href="tel:+234-************"
              className="text-maroon-700 hover:text-maroon-800 transition-colors"
            >
              +234-************
            </a>
          </p>
        </div>
      </form>
    </div>
  );
}