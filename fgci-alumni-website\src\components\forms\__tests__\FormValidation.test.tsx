import { describe, it, expect } from 'vitest';
import { validateField, validateForm, contactFormSchema } from '@/lib/validation';
import { ContactFormData } from '@/lib/types';

describe('Form Validation', () => {
  describe('validateField', () => {
    it('validates required fields correctly', () => {
      const nameRules = contactFormSchema.name;
      
      // Empty value should fail
      const emptyErrors = validateField('', nameRules);
      expect(emptyErrors).toContain('This field is required');
      
      // Valid value should pass
      const validErrors = validateField('<PERSON>', nameRules);
      expect(validErrors).toHaveLength(0);
    });

    it('validates email format correctly', () => {
      const emailRules = contactFormSchema.email;
      
      // Invalid email should fail
      const invalidErrors = validateField('invalid-email', emailRules);
      expect(invalidErrors).toContain('Please enter a valid email address');
      
      // Valid email should pass
      const validErrors = validateField('<EMAIL>', emailRules);
      expect(validErrors).toHaveLength(0);
    });

    it('validates phone format correctly', () => {
      const phoneRules = contactFormSchema.phone;
      
      // Invalid phone should fail
      const invalidErrors = validateField('123', phoneRules);
      expect(invalidErrors).toContain('Please enter a valid phone number');
      
      // Valid phone should pass
      const validErrors = validateField('+234-************', phoneRules);
      expect(validErrors).toHaveLength(0);
      
      // Empty phone should pass (optional field)
      const emptyErrors = validateField('', phoneRules);
      expect(emptyErrors).toHaveLength(0);
    });

    it('validates message length correctly', () => {
      const messageRules = contactFormSchema.message;
      
      // Too short message should fail
      const shortErrors = validateField('short', messageRules);
      expect(shortErrors).toContain('Must be at least 10 characters long');
      
      // Valid message should pass
      const validErrors = validateField('This is a valid message that is long enough', messageRules);
      expect(validErrors).toHaveLength(0);
    });

    it('validates graduation year range correctly', () => {
      const yearRules = contactFormSchema.graduationYear;
      
      // Too early year should fail
      const earlyErrors = validateField(1950, yearRules);
      expect(earlyErrors).toContain('Year must be between 1970');
      
      // Future year should fail
      const futureErrors = validateField(2030, yearRules);
      expect(futureErrors).toContain('Year must be between 1970');
      
      // Valid year should pass
      const validErrors = validateField(1995, yearRules);
      expect(validErrors).toHaveLength(0);
      
      // Empty year should pass (optional field)
      const emptyErrors = validateField('', yearRules);
      expect(emptyErrors).toHaveLength(0);
    });
  });

  describe('validateForm', () => {
    it('validates complete form correctly', () => {
      const validFormData: ContactFormData = {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+234-************',
        subject: 'Test Subject',
        message: 'This is a test message that is long enough to pass validation',
        graduationYear: 1995,
        inquiryType: 'general',
        chapter: 'lagos',
      };

      const { isValid, errors } = validateForm(validFormData, contactFormSchema);
      expect(isValid).toBe(true);
      expect(Object.keys(errors)).toHaveLength(0);
    });

    it('returns errors for invalid form data', () => {
      const invalidFormData: ContactFormData = {
        name: '',
        email: 'invalid-email',
        phone: '123',
        subject: '',
        message: 'short',
        graduationYear: 1950,
        inquiryType: '',
        chapter: '',
      };

      const { isValid, errors } = validateForm(invalidFormData, contactFormSchema);
      expect(isValid).toBe(false);
      expect(errors.name).toContain('This field is required');
      expect(errors.email).toContain('Please enter a valid email address');
      expect(errors.phone).toContain('Please enter a valid phone number');
      expect(errors.subject).toContain('This field is required');
      expect(errors.message).toContain('Must be at least 10 characters long');
      expect(errors.graduationYear[0]).toContain('Year must be between 1970');
      expect(errors.inquiryType).toContain('This field is required');
    });

    it('validates minimal required form data', () => {
      const minimalFormData: ContactFormData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Subject',
        message: 'This is a test message that is long enough to pass validation',
        inquiryType: 'general',
      };

      const { isValid, errors } = validateForm(minimalFormData, contactFormSchema);
      expect(isValid).toBe(true);
      expect(Object.keys(errors)).toHaveLength(0);
    });
  });
});