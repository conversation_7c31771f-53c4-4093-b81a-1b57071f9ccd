'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, But<PERSON> } from '@/components/ui';
import { 
  CalendarDaysIcon, 
  MapPinIcon,
  UsersIcon,
  ClockIcon,
  TagIcon,
  LinkIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import type { Event } from '@/lib/types';
import Link from 'next/link';
import Image from 'next/image';

interface EventCardProps {
  event: Event;
  showRegistration?: boolean;
  compact?: boolean;
}

export function EventCard({ event, showRegistration = true, compact = false }: EventCardProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'ongoing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEventStatusLabel = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'Upcoming';
      case 'ongoing':
        return 'Ongoing';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  const isRegistrationAvailable = () => {
    return event.status === 'upcoming' && event.registrationUrl;
  };

  const isEventPast = () => {
    return event.status === 'completed' || new Date(event.date) < new Date();
  };

  return (
    <Card
      variant="elevated"
      hover
      className="group overflow-hidden h-full flex flex-col"
    >
      {/* Event Image */}
      {event.heroImage && (
        <div className={cn(
          "relative overflow-hidden",
          compact ? "h-32" : "h-48"
        )}>
          <Image
            src={event.heroImage}
            alt={event.title}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute top-4 left-4">
            <span className={cn(
              'px-2 py-1 text-xs font-medium rounded-full border',
              getEventStatusColor(event.status)
            )}>
              {getEventStatusLabel(event.status)}
            </span>
          </div>
          {isEventPast() && (
            <div className="absolute inset-0 bg-black/20" />
          )}
        </div>
      )}

      <CardHeader className={compact ? "pb-2" : undefined}>
        <CardTitle className={cn(
          "line-clamp-2",
          compact ? "text-lg" : "text-xl"
        )}>
          {event.title}
        </CardTitle>
        <div className="flex items-center text-sm text-gray-500 mt-2 space-x-4">
          <div className="flex items-center">
            <CalendarDaysIcon className="w-4 h-4 mr-1" />
            {formatDate(event.date)}
          </div>
          <div className="flex items-center">
            <ClockIcon className="w-4 h-4 mr-1" />
            {formatTime(event.date)}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0 flex-1 flex flex-col">
        <p className={cn(
          "text-gray-600 mb-4",
          compact ? "line-clamp-2 text-sm" : "line-clamp-3"
        )}>
          {event.description}
        </p>

        {/* Event Details */}
        <div className="space-y-2 mb-4 text-sm flex-1">
          {event.location && (
            <div className="flex items-center text-gray-600">
              <MapPinIcon className="w-4 h-4 mr-2 flex-shrink-0" />
              <span className="line-clamp-1">{event.location}</span>
            </div>
          )}
          {event.organizer && (
            <div className="flex items-center text-gray-600">
              <TagIcon className="w-4 h-4 mr-2 flex-shrink-0" />
              <span className="line-clamp-1">Organized by {event.organizer}</span>
            </div>
          )}
          {event.endDate && (
            <div className="flex items-center text-gray-600">
              <CalendarDaysIcon className="w-4 h-4 mr-2 flex-shrink-0" />
              <span>Ends {formatDate(event.endDate)}</span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-auto">
          <Link href={`/events/${event.slug}`} className="flex-1">
            <Button
              variant="outline"
              size="sm"
              className="w-full"
            >
              View Details
            </Button>
          </Link>
          {showRegistration && isRegistrationAvailable() && (
            <a 
              href={event.registrationUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1"
            >
              <Button
                variant="primary"
                size="sm"
                className="w-full"
              >
                <LinkIcon className="w-4 h-4 mr-1" />
                Register
              </Button>
            </a>
          )}
        </div>
      </CardContent>
    </Card>
  );
}