/**
 * Service Worker for caching static assets and API responses
 */

const CACHE_NAME = 'fgci-alumni-v1';
const STATIC_CACHE_NAME = 'fgci-alumni-static-v1';
const API_CACHE_NAME = 'fgci-alumni-api-v1';
const IMAGE_CACHE_NAME = 'fgci-alumni-images-v1';

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/chapters',
  '/sets',
  '/events',
  '/gallery',
  '/excobot',
  '/_next/static/css/',
  '/_next/static/js/',
];

// API endpoints to cache
const API_ENDPOINTS = [
  '/api/chapters',
  '/api/sets',
  '/api/events',
  '/api/gallery',
  '/api/memorial',
];

// Cache strategies
const CACHE_STRATEGIES = {
  // Static assets: Cache first, fallback to network
  CACHE_FIRST: 'cache-first',
  // API data: Network first, fallback to cache
  NETWORK_FIRST: 'network-first',
  // Images: Cache first with stale-while-revalidate
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
};

// Cache durations (in seconds)
const CACHE_DURATIONS = {
  STATIC: 60 * 60 * 24 * 30, // 30 days
  API: 60 * 60 * 24,         // 1 day
  IMAGES: 60 * 60 * 24 * 7,  // 7 days
};

/**
 * Install event - cache static assets
 */
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME).then((cache) => {
        return cache.addAll(STATIC_ASSETS);
      }),
      
      // Skip waiting to activate immediately
      self.skipWaiting(),
    ])
  );
});

/**
 * Activate event - clean up old caches
 */
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    Promise.all([
      // Clean up old caches
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (
              cacheName !== CACHE_NAME &&
              cacheName !== STATIC_CACHE_NAME &&
              cacheName !== API_CACHE_NAME &&
              cacheName !== IMAGE_CACHE_NAME
            ) {
              console.log('Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      
      // Take control of all clients
      self.clients.claim(),
    ])
  );
});

/**
 * Fetch event - implement caching strategies
 */
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Only handle GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Determine caching strategy based on request type
  if (isStaticAsset(url)) {
    event.respondWith(handleStaticAsset(request));
  } else if (isAPIRequest(url)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isImageRequest(url)) {
    event.respondWith(handleImageRequest(request));
  } else {
    event.respondWith(handleOtherRequest(request));
  }
});

/**
 * Check if request is for a static asset
 */
function isStaticAsset(url) {
  return (
    url.pathname.startsWith('/_next/static/') ||
    url.pathname.endsWith('.js') ||
    url.pathname.endsWith('.css') ||
    url.pathname.endsWith('.woff2') ||
    url.pathname.endsWith('.woff') ||
    url.pathname === '/' ||
    url.pathname.startsWith('/chapters') ||
    url.pathname.startsWith('/sets') ||
    url.pathname.startsWith('/events') ||
    url.pathname.startsWith('/gallery') ||
    url.pathname.startsWith('/excobot')
  );
}

/**
 * Check if request is for API data
 */
function isAPIRequest(url) {
  return url.pathname.startsWith('/api/');
}

/**
 * Check if request is for an image
 */
function isImageRequest(url) {
  return (
    url.pathname.endsWith('.jpg') ||
    url.pathname.endsWith('.jpeg') ||
    url.pathname.endsWith('.png') ||
    url.pathname.endsWith('.webp') ||
    url.pathname.endsWith('.avif') ||
    url.pathname.endsWith('.svg') ||
    url.pathname.includes('/_next/image')
  );
}

/**
 * Handle static asset requests - Cache First strategy
 */
async function handleStaticAsset(request) {
  try {
    const cache = await caches.open(STATIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // Check if cache is still valid
      const cacheDate = new Date(cachedResponse.headers.get('date') || 0);
      const now = new Date();
      const age = (now.getTime() - cacheDate.getTime()) / 1000;
      
      if (age < CACHE_DURATIONS.STATIC) {
        return cachedResponse;
      }
    }
    
    // Fetch from network and update cache
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const responseClone = networkResponse.clone();
      cache.put(request, responseClone);
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Static asset fetch failed:', error);
    
    // Try to return cached version as fallback
    const cache = await caches.open(STATIC_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page or error response
    return new Response('Offline', { status: 503 });
  }
}

/**
 * Handle API requests - Network First strategy
 */
async function handleAPIRequest(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(API_CACHE_NAME);
      const responseClone = networkResponse.clone();
      
      // Add timestamp header for cache validation
      const headers = new Headers(responseClone.headers);
      headers.set('sw-cache-date', new Date().toISOString());
      
      const cachedResponse = new Response(responseClone.body, {
        status: responseClone.status,
        statusText: responseClone.statusText,
        headers: headers,
      });
      
      cache.put(request, cachedResponse);
    }
    
    return networkResponse;
  } catch (error) {
    console.error('API request failed, trying cache:', error);
    
    // Fallback to cache
    const cache = await caches.open(API_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      // Check if cached data is not too old
      const cacheDate = new Date(cachedResponse.headers.get('sw-cache-date') || 0);
      const now = new Date();
      const age = (now.getTime() - cacheDate.getTime()) / 1000;
      
      if (age < CACHE_DURATIONS.API) {
        // Add header to indicate this is cached data
        const headers = new Headers(cachedResponse.headers);
        headers.set('x-served-from', 'cache');
        
        return new Response(cachedResponse.body, {
          status: cachedResponse.status,
          statusText: cachedResponse.statusText,
          headers: headers,
        });
      }
    }
    
    // Return error response if no valid cache
    return new Response(
      JSON.stringify({ error: 'Network unavailable and no cached data' }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}

/**
 * Handle image requests - Stale While Revalidate strategy
 */
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE_NAME);
  const cachedResponse = await cache.match(request);
  
  // Return cached version immediately if available
  if (cachedResponse) {
    // Revalidate in background
    fetch(request)
      .then((networkResponse) => {
        if (networkResponse.ok) {
          cache.put(request, networkResponse.clone());
        }
      })
      .catch(console.error);
    
    return cachedResponse;
  }
  
  // No cache, fetch from network
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Image fetch failed:', error);
    
    // Return placeholder image or error
    return new Response('Image unavailable', { status: 503 });
  }
}

/**
 * Handle other requests - Network only
 */
async function handleOtherRequest(request) {
  try {
    return await fetch(request);
  } catch (error) {
    console.error('Request failed:', error);
    return new Response('Network error', { status: 503 });
  }
}

/**
 * Background sync for offline actions
 */
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

/**
 * Handle background sync
 */
async function doBackgroundSync() {
  // Handle any queued offline actions
  console.log('Background sync triggered');
}

/**
 * Handle push notifications (if implemented)
 */
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    
    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        tag: data.tag || 'default',
        data: data.data,
      })
    );
  }
});

/**
 * Handle notification clicks
 */
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  event.waitUntil(
    clients.openWindow(event.notification.data?.url || '/')
  );
});

/**
 * Periodic cache cleanup
 */
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEANUP_CACHE') {
    event.waitUntil(cleanupCache());
  }
});

/**
 * Clean up old cache entries
 */
async function cleanupCache() {
  const cacheNames = [STATIC_CACHE_NAME, API_CACHE_NAME, IMAGE_CACHE_NAME];
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName);
    const requests = await cache.keys();
    
    for (const request of requests) {
      const response = await cache.match(request);
      if (response) {
        const cacheDate = new Date(response.headers.get('date') || 0);
        const now = new Date();
        const age = (now.getTime() - cacheDate.getTime()) / 1000;
        
        // Remove entries older than their cache duration
        let maxAge = CACHE_DURATIONS.STATIC;
        if (cacheName === API_CACHE_NAME) maxAge = CACHE_DURATIONS.API;
        if (cacheName === IMAGE_CACHE_NAME) maxAge = CACHE_DURATIONS.IMAGES;
        
        if (age > maxAge) {
          await cache.delete(request);
        }
      }
    }
  }
}