'use client';

import { useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import { useForm } from './useForm';
import { ContactFormData, ApiResponse } from '@/lib/types';
import { contactFormSchema } from '@/lib/validation';

interface ContactFormResponse {
  id: string;
  message: string;
  submittedAt: string;
}

const initialFormData: ContactFormData = {
  name: '',
  email: '',
  phone: '',
  subject: '',
  message: '',
  graduationYear: undefined,
  inquiryType: '',
  chapter: '',
};

export function useContactForm() {
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [submitMessage, setSubmitMessage] = useState<string>('');
  const [uiSubmitting, setUiSubmitting] = useState(false);

  // Form mutation for API submission
  const submitMutation = useMutation({
    mutationFn: async (data: ContactFormData): Promise<ApiResponse<ContactFormResponse>> => {
      return apiClient.post('/contact', data);
    },
    onSuccess: (response) => {
      setSubmitStatus('success');
      setSubmitMessage(response.data?.message || 'Thank you for your message. We will get back to you soon!');
      reset(); // Reset form on successful submission
    },
    onError: (error: any) => {
      setSubmitStatus('error');

      // Handle validation errors
      if (error?.fieldErrors) {
        Object.entries(error.fieldErrors).forEach(([field, errors]) => {
          if (Array.isArray(errors) && errors.length > 0) {
            setFieldError(field as keyof ContactFormData, errors);
          }
        });
        setSubmitMessage('Please correct the errors below and try again.');
      } else {
        setSubmitMessage(
          error?.message ||
          'There was an error sending your message. Please try again or contact us directly.'
        );
      }
    },
  });

  // Form hook with validation
  const {
    data,
    errors,
    touched,
    isSubmitting,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldError,
    reset,
    getFieldProps,
  } = useForm({
    initialData: initialFormData,
    validationSchema: contactFormSchema,
    onSubmit: async (formData) => {
      setSubmitStatus('idle');
      setSubmitMessage('');
      setUiSubmitting(true);
      try {
        await submitMutation.mutateAsync(formData);
      } finally {
        setUiSubmitting(false);
      }
    },
  });

  // Clear submit status when user starts typing
  const handleFieldChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    if (submitStatus !== 'idle') {
      setSubmitStatus('idle');
      setSubmitMessage('');
    }
    handleChange(e);
  };

  // Reset form and status
  const resetForm = () => {
    reset();
    setSubmitStatus('idle');
    setSubmitMessage('');
  };

  const beginSubmit = () => setUiSubmitting(true);

  return {
    // Form data and state
    formData: data,
    errors,
    touched,
    isSubmitting: isSubmitting || submitMutation.isPending || uiSubmitting,
    isValid,

    // Submit status
    submitStatus,
    submitMessage,

    // Handlers
    handleChange: handleFieldChange,
    handleBlur,
    handleSubmit,
    beginSubmit,
    setFieldValue,
    setFieldError,
    reset: resetForm,
    getFieldProps: (fieldName: keyof ContactFormData) => ({
      ...getFieldProps(fieldName),
      onChange: handleFieldChange,
    }),

    // Mutation state
    isLoading: submitMutation.isPending || uiSubmitting,
    error: submitMutation.error,
  };
}