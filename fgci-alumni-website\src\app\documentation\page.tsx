'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, <PERSON><PERSON> } from '@/components/ui';
import { 
  BookOpenIcon,
  CodeBracketIcon,
  PaintBrushIcon,
  CogIcon,
  RocketLaunchIcon,
  DocumentTextIcon,
  BeakerIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

interface DocSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  content: React.ReactNode;
}

export default function DocumentationPage() {
  const [activeSection, setActiveSection] = useState('overview');

  const sections: DocSection[] = [
    {
      id: 'overview',
      title: 'Project Overview',
      icon: <BookOpenIcon className="w-5 h-5" />,
      description: 'Introduction to the FGC Ikom Alumni Website',
      content: (
        <div className="prose max-w-none">
          <h2>FGC Ikom Alumni Association Website</h2>
          <p>
            A modern, accessible, and performant website built for the Federal Government College Ikom Alumni Association. 
            The platform connects alumni worldwide through chapters, sets, events, and a comprehensive directory.
          </p>
          
          <h3>Key Features</h3>
          <ul>
            <li><strong>Alumni Directory (ExcoBot)</strong> - Searchable database of alumni with advanced filtering</li>
            <li><strong>Chapter Management</strong> - Regional chapters with executive committees and activities</li>
            <li><strong>Set Organization</strong> - Graduation year-based groupings with coordinators</li>
            <li><strong>Event System</strong> - Reunions, networking events, and celebrations</li>
            <li><strong>Photo Gallery</strong> - Memories and moments from school and alumni events</li>
            <li><strong>Roll of Honour</strong> - Memorial section for departed alumni</li>
            <li><strong>Contact System</strong> - Communication tools and contact forms</li>
          </ul>

          <h3>Technical Stack</h3>
          <ul>
            <li><strong>Framework:</strong> Next.js 15 with App Router and Turbopack</li>
            <li><strong>Language:</strong> TypeScript for type safety</li>
            <li><strong>Styling:</strong> Tailwind CSS with custom design tokens</li>
            <li><strong>Testing:</strong> Vitest, Playwright, and comprehensive test suites</li>
            <li><strong>Documentation:</strong> Storybook for component documentation</li>
            <li><strong>Performance:</strong> Optimized for Core Web Vitals</li>
            <li><strong>Accessibility:</strong> WCAG AA compliant</li>
          </ul>
        </div>
      )
    },
    {
      id: 'design-system',
      title: 'Design System',
      icon: <PaintBrushIcon className="w-5 h-5" />,
      description: 'Colors, typography, and design tokens',
      content: (
        <div className="prose max-w-none">
          <h2>Design System</h2>
          
          <h3>Brand Colors</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 not-prose mb-6">
            <div className="text-center">
              <div className="w-full h-16 bg-maroon-700 rounded-lg mb-2"></div>
              <div className="text-sm font-semibold">Maroon 700</div>
              <div className="text-xs text-gray-600">#8B4513</div>
            </div>
            <div className="text-center">
              <div className="w-full h-16 bg-gold-500 rounded-lg mb-2"></div>
              <div className="text-sm font-semibold">Gold 500</div>
              <div className="text-xs text-gray-600">#FFD700</div>
            </div>
          </div>

          <h3>Typography</h3>
          <ul>
            <li><strong>Display Font:</strong> Playfair Display - Used for headings and titles</li>
            <li><strong>Body Font:</strong> Inter - Used for body text and UI elements</li>
          </ul>

          <h3>Component Classes</h3>
          <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
            <div>.heading-1 - Main page titles</div>
            <div>.heading-2 - Section titles</div>
            <div>.heading-3 - Subsection titles</div>
            <div>.body-large - Introductory text</div>
            <div>.body-base - Standard body text</div>
            <div>.body-small - Captions and metadata</div>
          </div>
        </div>
      )
    },
    {
      id: 'components',
      title: 'Components',
      icon: <CodeBracketIcon className="w-5 h-5" />,
      description: 'Reusable UI components and their usage',
      content: (
        <div className="prose max-w-none">
          <h2>Component Library</h2>
          
          <h3>Core Components</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 not-prose">
            <Card variant="outlined">
              <CardHeader>
                <CardTitle>Button</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">
                  Versatile button component with variants, sizes, and states.
                </p>
                <div className="space-y-2 text-xs font-mono bg-gray-100 p-2 rounded">
                  <div>variants: primary, secondary, outline, ghost, danger</div>
                  <div>sizes: sm, md, lg</div>
                  <div>states: disabled, loading</div>
                </div>
              </CardContent>
            </Card>

            <Card variant="outlined">
              <CardHeader>
                <CardTitle>Card</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-3">
                  Flexible container with header, title, and content sections.
                </p>
                <div className="space-y-2 text-xs font-mono bg-gray-100 p-2 rounded">
                  <div>variants: default, elevated, outlined</div>
                  <div>features: hover effects, clickable</div>
                  <div>sections: CardHeader, CardTitle, CardContent</div>
                </div>
              </CardContent>
            </Card>
          </div>

          <h3>Layout Components</h3>
          <ul>
            <li><strong>Header:</strong> Main navigation with responsive mobile menu</li>
            <li><strong>Footer:</strong> Site links, contact info, and social media</li>
            <li><strong>HeroCarousel:</strong> Image carousel with auto-play and navigation</li>
            <li><strong>HighlightCards:</strong> Feature showcase grid with presets</li>
          </ul>

          <h3>Specialized Components</h3>
          <ul>
            <li><strong>SearchFilters:</strong> Advanced filtering interface</li>
            <li><strong>Lightbox:</strong> Image gallery with keyboard navigation</li>
            <li><strong>Breadcrumbs:</strong> Navigation breadcrumbs with structured data</li>
            <li><strong>Announcements:</strong> Priority-based announcement system</li>
          </ul>
        </div>
      )
    },
    {
      id: 'development',
      title: 'Development',
      icon: <CogIcon className="w-5 h-5" />,
      description: 'Setup, scripts, and development workflow',
      content: (
        <div className="prose max-w-none">
          <h2>Development Guide</h2>
          
          <h3>Getting Started</h3>
          <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
            <div># Clone the repository</div>
            <div>git clone [repository-url]</div>
            <div className="mt-2"># Install dependencies</div>
            <div>npm install</div>
            <div className="mt-2"># Start development server</div>
            <div>npm run dev</div>
            <div className="mt-2"># Start with mock data</div>
            <div>npm run dev:mock</div>
          </div>

          <h3>Available Scripts</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 not-prose">
            <div>
              <h4 className="font-semibold mb-2">Development</h4>
              <div className="bg-gray-100 p-3 rounded text-sm font-mono space-y-1">
                <div>npm run dev</div>
                <div>npm run dev:mock</div>
                <div>npm run build</div>
                <div>npm run start</div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Testing</h4>
              <div className="bg-gray-100 p-3 rounded text-sm font-mono space-y-1">
                <div>npm run test</div>
                <div>npm run test:coverage</div>
                <div>npm run test:e2e</div>
                <div>npm run test:e2e:ui</div>
              </div>
            </div>
          </div>

          <h3>Project Structure</h3>
          <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
            <div>src/</div>
            <div>├── app/                 # Next.js app router pages</div>
            <div>├── components/          # Reusable components</div>
            <div>│   ├── ui/             # Basic UI components</div>
            <div>│   ├── layout/         # Layout components</div>
            <div>│   └── accessibility/  # A11y components</div>
            <div>├── lib/                # Utilities and helpers</div>
            <div>├── mocks/              # MSW mock data</div>
            <div>└── styles/             # Global styles</div>
          </div>
        </div>
      )
    },
    {
      id: 'testing',
      title: 'Testing',
      icon: <BeakerIcon className="w-5 h-5" />,
      description: 'Testing strategy and implementation',
      content: (
        <div className="prose max-w-none">
          <h2>Testing Strategy</h2>
          
          <h3>Testing Pyramid</h3>
          <ul>
            <li><strong>Unit Tests:</strong> Component logic and utilities (Vitest)</li>
            <li><strong>Integration Tests:</strong> Component interactions and API integration</li>
            <li><strong>E2E Tests:</strong> User journeys and workflows (Playwright)</li>
            <li><strong>Performance Tests:</strong> Core Web Vitals and bundle analysis</li>
          </ul>

          <h3>Test Coverage Goals</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 not-prose">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">85%</div>
              <div className="text-sm text-green-700">Unit Tests</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">78%</div>
              <div className="text-sm text-blue-700">Integration</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">72%</div>
              <div className="text-sm text-purple-700">E2E Tests</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">90%</div>
              <div className="text-sm text-orange-700">Performance</div>
            </div>
          </div>

          <h3>Testing Tools</h3>
          <ul>
            <li><strong>Vitest:</strong> Fast unit test runner with coverage</li>
            <li><strong>Testing Library:</strong> Component testing utilities</li>
            <li><strong>Playwright:</strong> Cross-browser E2E testing</li>
            <li><strong>MSW:</strong> API mocking for development and testing</li>
          </ul>
        </div>
      )
    },
    {
      id: 'accessibility',
      title: 'Accessibility',
      icon: <ShieldCheckIcon className="w-5 h-5" />,
      description: 'WCAG compliance and accessibility features',
      content: (
        <div className="prose max-w-none">
          <h2>Accessibility Implementation</h2>
          
          <h3>WCAG AA Compliance</h3>
          <p>The website meets WCAG 2.1 AA standards with the following features:</p>
          
          <ul>
            <li><strong>Keyboard Navigation:</strong> All interactive elements accessible via keyboard</li>
            <li><strong>Screen Reader Support:</strong> Proper ARIA labels and semantic HTML</li>
            <li><strong>Color Contrast:</strong> Minimum 4.5:1 ratio for normal text</li>
            <li><strong>Focus Management:</strong> Visible focus indicators and logical tab order</li>
            <li><strong>Alternative Text:</strong> Descriptive alt text for all images</li>
          </ul>

          <h3>Accessibility Components</h3>
          <ul>
            <li><strong>SkipToContent:</strong> Skip navigation link for keyboard users</li>
            <li><strong>FocusTrap:</strong> Manages focus within modals and dialogs</li>
            <li><strong>ScreenReaderOnly:</strong> Content visible only to screen readers</li>
          </ul>

          <h3>Testing Tools</h3>
          <ul>
            <li><strong>axe-core:</strong> Automated accessibility testing</li>
            <li><strong>Lighthouse:</strong> Accessibility audits and scoring</li>
            <li><strong>Manual Testing:</strong> Keyboard navigation and screen reader testing</li>
          </ul>
        </div>
      )
    },
    {
      id: 'performance',
      title: 'Performance',
      icon: <ChartBarIcon className="w-5 h-5" />,
      description: 'Optimization strategies and metrics',
      content: (
        <div className="prose max-w-none">
          <h2>Performance Optimization</h2>
          
          <h3>Core Web Vitals Targets</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 not-prose">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-xl font-bold text-green-600">LCP</div>
              <div className="text-2xl font-bold text-green-600">&lt; 2.5s</div>
              <div className="text-sm text-green-700">Largest Contentful Paint</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-xl font-bold text-blue-600">FID</div>
              <div className="text-2xl font-bold text-blue-600">&lt; 100ms</div>
              <div className="text-sm text-blue-700">First Input Delay</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-xl font-bold text-purple-600">CLS</div>
              <div className="text-2xl font-bold text-purple-600">&lt; 0.1</div>
              <div className="text-sm text-purple-700">Cumulative Layout Shift</div>
            </div>
          </div>

          <h3>Optimization Strategies</h3>
          <ul>
            <li><strong>Image Optimization:</strong> Next.js Image component with WebP/AVIF</li>
            <li><strong>Code Splitting:</strong> Dynamic imports and route-based splitting</li>
            <li><strong>Caching:</strong> Static generation with ISR for dynamic content</li>
            <li><strong>Bundle Analysis:</strong> Regular bundle size monitoring</li>
            <li><strong>Performance Budgets:</strong> Automated performance regression detection</li>
          </ul>

          <h3>Monitoring</h3>
          <ul>
            <li><strong>Web Vitals:</strong> Real-time performance monitoring</li>
            <li><strong>Lighthouse CI:</strong> Automated performance audits</li>
            <li><strong>Bundle Analyzer:</strong> Bundle size analysis and optimization</li>
          </ul>
        </div>
      )
    },
    {
      id: 'deployment',
      title: 'Deployment',
      icon: <RocketLaunchIcon className="w-5 h-5" />,
      description: 'CI/CD pipeline and deployment process',
      content: (
        <div className="prose max-w-none">
          <h2>Deployment Guide</h2>
          
          <h3>CI/CD Pipeline</h3>
          <p>Automated deployment pipeline with GitHub Actions:</p>
          
          <ol>
            <li><strong>Code Quality:</strong> ESLint, Prettier, and TypeScript checks</li>
            <li><strong>Testing:</strong> Unit tests, integration tests, and E2E tests</li>
            <li><strong>Performance:</strong> Lighthouse audits and bundle analysis</li>
            <li><strong>Security:</strong> Dependency vulnerability scanning</li>
            <li><strong>Deployment:</strong> Automatic deployment to Vercel</li>
          </ol>

          <h3>Environment Configuration</h3>
          <div className="bg-gray-100 p-4 rounded-lg font-mono text-sm">
            <div># Environment Variables</div>
            <div>NEXT_PUBLIC_USE_MOCKS=false</div>
            <div>NEXT_PUBLIC_API_URL=https://api.example.com</div>
            <div>DATABASE_URL=postgresql://...</div>
            <div>NEXTAUTH_SECRET=your-secret-key</div>
          </div>

          <h3>Deployment Checklist</h3>
          <ul>
            <li>✅ All tests passing</li>
            <li>✅ Performance audits meet targets</li>
            <li>✅ Accessibility compliance verified</li>
            <li>✅ SEO meta tags configured</li>
            <li>✅ Environment variables set</li>
            <li>✅ Domain and SSL configured</li>
          </ul>
        </div>
      )
    }
  ];

  const activeDoc = sections.find(section => section.id === activeSection);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container-custom py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
              Documentation
            </h1>
            <p className="body-large text-gray-600">
              Comprehensive documentation for the FGC Ikom Alumni website including 
              design system, components, development guide, and deployment instructions.
            </p>
          </div>
        </div>
      </div>

      <div className="container-custom py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <nav className="space-y-2">
                {sections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeSection === section.id
                        ? 'bg-maroon-700 text-white'
                        : 'bg-white hover:bg-gray-50 text-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      {section.icon}
                      <div>
                        <div className="font-medium">{section.title}</div>
                        <div className={`text-sm ${
                          activeSection === section.id ? 'text-white/80' : 'text-gray-500'
                        }`}>
                          {section.description}
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Card variant="elevated">
              <CardContent className="p-8">
                {activeDoc?.content}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
