import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/stories/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary maroon colors
        maroon: {
          DEFAULT: "#7B0F17",
          50: "#F5E6E8",
          100: "#E8C1C6",
          200: "#D99BA2",
          300: "#CA757E",
          400: "#BE4F5A",
          500: "#B22936",
          600: "#9E1F2C",
          700: "#7B0F17", // Primary maroon
          800: "#5C0B12", // Maroon dark
          900: "#3D0708",
        },
        // Gold colors
        gold: {
          DEFAULT: "#D4A017",
          50: "#FDF9F0",
          100: "#FAF0D9",
          200: "#F7E6C0", // Gold light (accent)
          300: "#F0D898",
          400: "#E8CA70",
          500: "#D4A017", // Primary gold
          600: "#B8900F",
          700: "#9C7A0C",
          800: "#806409",
          900: "#644E07",
        },
        // Neutral grays
        gray: {
          50: "#f8fafc",
          100: "#f1f5f9", // --gray-100
          200: "#e2e8f0",
          300: "#cbd5e1",
          400: "#94a3b8",
          500: "#64748b",
          600: "#475569",
          700: "#334155", // --gray-700
          800: "#1e293b",
          900: "#0f172a", // --gray-900
        },
        // Semantic colors
        success: "#10b981",
        warning: "#f59e0b",
        error: "#ef4444",
        info: "#3b82f6",
      },
      fontFamily: {
        heading: ["var(--font-heading)", "Merriweather", "serif"],
        body: ["var(--font-body)", "Open Sans", "sans-serif"],
      },
      fontSize: {
        xs: ["0.75rem", { lineHeight: "1.25" }],
        sm: ["0.875rem", { lineHeight: "1.375" }],
        base: ["1rem", { lineHeight: "1.5" }],
        lg: ["1.125rem", { lineHeight: "1.5" }],
        xl: ["1.25rem", { lineHeight: "1.5" }],
        "2xl": ["1.5rem", { lineHeight: "1.375" }],
        "3xl": ["1.875rem", { lineHeight: "1.25" }],
        "4xl": ["2.25rem", { lineHeight: "1.25" }],
        "5xl": ["3rem", { lineHeight: "1.25" }],
        "6xl": ["3.75rem", { lineHeight: "1.25" }],
      },
      spacing: {
        "18": "4.5rem",
        "88": "22rem",
        "128": "32rem",
      },
      animation: {
        "fade-in": "fadeIn 0.3s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "scale-in": "scaleIn 0.15s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        scaleIn: {
          "0%": { transform: "scale(0.95)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
      },
      boxShadow: {
        "hover": "0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        "focus": "0 0 0 3px rgba(212, 160, 23, 0.25)",
      },
      transitionDuration: {
        "150": "150ms",
        "300": "300ms",
        "500": "500ms",
      },
      transitionTimingFunction: {
        "ease-in": "cubic-bezier(0.4, 0, 1, 1)",
        "ease-out": "cubic-bezier(0, 0, 0.2, 1)",
        "ease-in-out": "cubic-bezier(0.4, 0, 0.2, 1)",
      },
      // Accessibility-focused utilities
      contrast: {
        'aa': '4.5',
        'aaa': '7',
      },
      // High contrast color variants
      'high-contrast': {
        'maroon': '#5C0B12',
        'gold': '#B8900F',
        'text': '#000000',
        'bg': '#FFFFFF',
      },
    },
  },
  plugins: [
    // Custom plugin for accessibility utilities
    function({ addUtilities, theme, addComponents }: any) {
      // Screen reader only utilities
      addUtilities({
        '.sr-only': {
          position: 'absolute',
          width: '1px',
          height: '1px',
          padding: '0',
          margin: '-1px',
          overflow: 'hidden',
          clip: 'rect(0, 0, 0, 0)',
          whiteSpace: 'nowrap',
          border: '0',
        },
        '.not-sr-only': {
          position: 'static',
          width: 'auto',
          height: 'auto',
          padding: '0',
          margin: '0',
          overflow: 'visible',
          clip: 'auto',
          whiteSpace: 'normal',
        },
        // Focus utilities
        '.focus-visible-only': {
          '&:focus:not(:focus-visible)': {
            outline: 'none',
          },
        },
        // High contrast utilities
        '@media (prefers-contrast: high)': {
          '.hc-text': {
            color: '#000000 !important',
            fontWeight: '600 !important',
          },
          '.hc-bg': {
            backgroundColor: '#FFFFFF !important',
          },
          '.hc-border': {
            borderColor: '#000000 !important',
            borderWidth: '2px !important',
          },
        },
        // Reduced motion utilities
        '@media (prefers-reduced-motion: reduce)': {
          '.motion-reduce': {
            animationDuration: '0.01ms !important',
            animationIterationCount: '1 !important',
            transitionDuration: '0.01ms !important',
            scrollBehavior: 'auto !important',
          },
        },
      });

      // Accessible component styles
      addComponents({
        '.btn-accessible': {
          minHeight: '44px',
          minWidth: '44px',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: '0.5rem 1rem',
          fontSize: '1rem',
          fontWeight: '500',
          borderRadius: '0.375rem',
          border: '2px solid transparent',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:focus-visible': {
            outline: '3px solid ' + theme('colors.gold.500'),
            outlineOffset: '2px',
          },
          '&:disabled': {
            opacity: '0.5',
            cursor: 'not-allowed',
          },
        },
        '.link-accessible': {
          color: theme('colors.maroon.700'),
          textDecoration: 'underline',
          textDecorationThickness: '1px',
          textUnderlineOffset: '2px',
          fontWeight: '500',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            color: theme('colors.maroon.800'),
            textDecorationThickness: '2px',
          },
          '&:focus-visible': {
            outline: '3px solid ' + theme('colors.gold.500'),
            outlineOffset: '2px',
            borderRadius: '2px',
          },
          '@media (prefers-contrast: high)': {
            textDecorationThickness: '2px !important',
            fontWeight: '600 !important',
          },
        },
        '.card-accessible': {
          backgroundColor: theme('colors.white'),
          borderRadius: '0.5rem',
          border: '1px solid ' + theme('colors.gray.200'),
          boxShadow: theme('boxShadow.sm'),
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            boxShadow: theme('boxShadow.hover'),
            borderColor: theme('colors.gold.500'),
          },
          '&:focus-visible': {
            outline: '3px solid ' + theme('colors.gold.500'),
            outlineOffset: '2px',
          },
          '@media (prefers-contrast: high)': {
            border: '2px solid ' + theme('colors.gray.800') + ' !important',
          },
        },
      });
    },
  ],
};

export default config;