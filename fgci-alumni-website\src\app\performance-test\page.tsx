'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, Button } from '@/components/ui';
import { 
  measureWebVitals, 
  checkPerformanceBudget, 
  getMemoryUsage,
  type PerformanceMetrics 
} from '@/lib/performance';
import { debounce, throttle } from '@/lib/utils';
import { 
  ChartBarIcon,
  ClockIcon,
  CpuChipIcon,
  PhotoIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

export default function PerformanceTestPage() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [budgetStatus, setBudgetStatus] = useState<{ passed: boolean; violations: string[] } | null>(null);
  const [memoryInfo, setMemoryInfo] = useState<MemoryInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  // Debounced search function for testing
  const debouncedSearch = debounce((query: string) => {
    console.log('Debounced search:', query);
    setTestResults(prev => [...prev, `Debounced search: ${query}`]);
  }, 300);

  // Throttled scroll function for testing
  const throttledScroll = throttle(() => {
    console.log('Throttled scroll event');
    setTestResults(prev => [...prev, 'Throttled scroll event triggered']);
  }, 100);

  useEffect(() => {
    // Add scroll listener for throttle testing
    window.addEventListener('scroll', throttledScroll);
    return () => window.removeEventListener('scroll', throttledScroll);
  }, [throttledScroll]);

  const runPerformanceTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      // Measure Web Vitals
      const webVitals = await measureWebVitals();
      setMetrics(webVitals);

      // Check performance budget
      const budget = checkPerformanceBudget(webVitals);
      setBudgetStatus(budget);

      // Get memory usage
      const memory = getMemoryUsage();
      setMemoryInfo(memory);

      setTestResults(prev => [
        ...prev,
        'Performance metrics collected',
        `FCP: ${webVitals.fcp.toFixed(2)}ms`,
        `LCP: ${webVitals.lcp.toFixed(2)}ms`,
        `FID: ${webVitals.fid.toFixed(2)}ms`,
        `CLS: ${webVitals.cls.toFixed(4)}`,
        `TTFB: ${webVitals.ttfb.toFixed(2)}ms`,
        budget.passed ? 'All performance budgets passed!' : `${budget.violations.length} budget violations`
      ]);

      if (memory) {
        setTestResults(prev => [
          ...prev,
          `Memory usage: ${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`
        ]);
      }

    } catch (error) {
      setTestResults(prev => [...prev, 'Error running performance tests']);
    } finally {
      setIsLoading(false);
    }
  };

  const testImageLoading = () => {
    setTestResults(prev => [...prev, 'Testing optimized image loading...']);
  };

  const testDebounce = (query: string) => {
    debouncedSearch(query);
  };

  const getMetricColor = (metric: keyof PerformanceMetrics, value: number) => {
    const thresholds = {
      fcp: { good: 1800, poor: 3000 },
      lcp: { good: 2500, poor: 4000 },
      fid: { good: 100, poor: 300 },
      cls: { good: 0.1, poor: 0.25 },
      ttfb: { good: 600, poor: 1500 }
    };

    const threshold = thresholds[metric];
    if (value <= threshold.good) return 'text-green-600';
    if (value <= threshold.poor) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container-custom py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
              Performance Testing
            </h1>
            <p className="body-large text-gray-600">
              Monitor and test the performance optimizations implemented throughout 
              the FGC Ikom Alumni website.
            </p>
          </div>
        </div>
      </div>

      <div className="container-custom py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Performance Metrics */}
          <div className="space-y-6">
            <Card variant="elevated">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <ChartBarIcon className="w-5 h-5" />
                    Web Vitals
                  </CardTitle>
                  <Button 
                    onClick={runPerformanceTests}
                    disabled={isLoading}
                    variant="outline"
                    size="sm"
                  >
                    {isLoading ? 'Testing...' : 'Run Tests'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {metrics ? (
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">First Contentful Paint</span>
                      <span className={`text-sm font-mono ${getMetricColor('fcp', metrics.fcp)}`}>
                        {metrics.fcp.toFixed(0)}ms
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Largest Contentful Paint</span>
                      <span className={`text-sm font-mono ${getMetricColor('lcp', metrics.lcp)}`}>
                        {metrics.lcp.toFixed(0)}ms
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">First Input Delay</span>
                      <span className={`text-sm font-mono ${getMetricColor('fid', metrics.fid)}`}>
                        {metrics.fid.toFixed(0)}ms
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Cumulative Layout Shift</span>
                      <span className={`text-sm font-mono ${getMetricColor('cls', metrics.cls)}`}>
                        {metrics.cls.toFixed(3)}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Time to First Byte</span>
                      <span className={`text-sm font-mono ${getMetricColor('ttfb', metrics.ttfb)}`}>
                        {metrics.ttfb.toFixed(0)}ms
                      </span>
                    </div>

                    {budgetStatus && (
                      <div className="pt-3 border-t">
                        <div className={`text-sm font-medium ${budgetStatus.passed ? 'text-green-600' : 'text-red-600'}`}>
                          {budgetStatus.passed ? '✅ All budgets passed' : `❌ ${budgetStatus.violations.length} violations`}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Click "Run Tests" to measure performance metrics</p>
                )}
              </CardContent>
            </Card>

            {/* Memory Usage */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CpuChipIcon className="w-5 h-5" />
                  Memory Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                {memoryInfo ? (
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Used Heap Size</span>
                      <span className="text-sm font-mono">
                        {(memoryInfo.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Total Heap Size</span>
                      <span className="text-sm font-mono">
                        {(memoryInfo.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">Heap Size Limit</span>
                      <span className="text-sm font-mono">
                        {(memoryInfo.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB
                      </span>
                    </div>

                    <div className="pt-3 border-t">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ 
                            width: `${(memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100}%` 
                          }}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {((memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100).toFixed(1)}% of limit used
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">Memory information not available</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Performance Tests */}
          <div className="space-y-6">
            {/* Image Optimization Test */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PhotoIcon className="w-5 h-5" />
                  Image Optimization
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-600">
                    Testing optimized image loading with lazy loading and responsive images.
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <img
                      src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&w=300&h=200"
                      alt="Test image 1"
                      width={150}
                      height={100}
                      className="rounded-lg object-cover"
                      onLoad={() => setTestResults(prev => [...prev, 'Image 1 loaded'])}
                    />
                    <img
                      src="https://images.unsplash.com/photo-1541339907198-e08756dedf3f?ixlib=rb-4.0.3&w=300&h=200"
                      alt="Test image 2"
                      width={150}
                      height={100}
                      className="rounded-lg object-cover"
                      onLoad={() => setTestResults(prev => [...prev, 'Image 2 loaded'])}
                    />
                  </div>
                  
                  <Button onClick={testImageLoading} variant="outline" size="sm">
                    Test Image Loading
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Debounce/Throttle Test */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ClockIcon className="w-5 h-5" />
                  Debounce & Throttle
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Debounced Search (300ms delay)
                    </label>
                    <input
                      type="text"
                      placeholder="Type to test debouncing..."
                      onChange={(e) => testDebounce(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
                    />
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">
                      Scroll the page to test throttled scroll events (100ms limit).
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Test Results */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <RocketLaunchIcon className="w-5 h-5" />
                  Test Results
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {testResults.length > 0 ? (
                    testResults.map((result, index) => (
                      <div key={index} className="text-sm text-gray-700 font-mono bg-gray-50 px-2 py-1 rounded">
                        {result}
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-sm">No test results yet</p>
                  )}
                </div>
                
                {testResults.length > 0 && (
                  <Button 
                    onClick={() => setTestResults([])} 
                    variant="ghost" 
                    size="sm" 
                    className="mt-3"
                  >
                    Clear Results
                  </Button>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
