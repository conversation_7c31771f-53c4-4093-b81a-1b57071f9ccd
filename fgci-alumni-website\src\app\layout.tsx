import type { <PERSON><PERSON><PERSON> } from "next";
import { <PERSON>reader, <PERSON> } from "next/font/google";
import "../styles/globals.css";
import { <PERSON><PERSON>, Footer } from "@/components/layout";
import { MSWProvider } from "@/components/providers/MSWProvider";
import { AnimationProvider } from "@/components/providers/AnimationProvider";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { SkipToContent } from "@/components/accessibility";
import { PerformanceMonitor } from "@/components/performance";
// import { PageTransition } from "@/components/animations";
import { generateOrganizationStructuredData, generateEducationalOrganizationStructuredData } from "@/lib/seo";

// Optimized font loading with next/font (Google)
const newsreader = Newsreader({
  variable: "--font-heading",
  subsets: ["latin"],
  display: "swap",
  preload: true,
  fallback: ["Georgia", "Times New Roman", "serif"],
  adjustFontFallback: true,
});

const inter = Inter({
  variable: "--font-body",
  subsets: ["latin"],
  display: "swap",
  preload: true,
  fallback: ["system-ui", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "sans-serif"],
  adjustFontFallback: true,
});

export const metadata: Metadata = {
  title: "FGC Ikom Alumni Association",
  description: "Official website of the Federal Government College Ikom Alumni Association. Connect with fellow alumni, explore chapters, sets, events, and galleries.",
  keywords: "FGC, Ikom, Alumni, Federal Government College, Cross River, Nigeria, Education",
  authors: [{ name: "FGC Ikom Alumni Association" }],
  creator: "FGC Ikom Alumni Association",
  publisher: "FGC Ikom Alumni Association",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://www.fgcikomalumni.org.ng",
    siteName: "FGC Ikom Alumni Association",
    title: "FGC Ikom Alumni Association",
    description: "Official website of the Federal Government College Ikom Alumni Association.",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "FGC Ikom Alumni Association",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "FGC Ikom Alumni Association",
    description: "Official website of the Federal Government College Ikom Alumni Association.",
    images: ["/images/og-image.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const organizationData = generateOrganizationStructuredData();
  const educationalOrgData = generateEducationalOrganizationStructuredData();

  return (
    <html lang="en" className={`${newsreader.variable} ${inter.variable}`}>
      <head>
        {/* Critical resource hints for Core Web Vitals */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://fonts.gstatic.com" />

        {/* Viewport with proper scaling */}
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#7B0F17" />
        <meta name="color-scheme" content="light" />

        {/* Organization Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(organizationData)
          }}
        />

        {/* Educational Organization Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(educationalOrgData)
          }}
        />
      </head>
      <body className="font-body antialiased">
        <QueryProvider>
          <MSWProvider>
            <AnimationProvider>
              <SkipToContent />
              {/* Layout container with proper sizing to prevent CLS */}
              <div className="flex min-h-screen flex-col" style={{ minHeight: '100vh' }}>
              <Header />
              <main
                id="main-content"
                className="flex-1 w-full overflow-x-hidden"
                tabIndex={-1}
                style={{ minHeight: 'calc(100vh - 200px)' }} // Prevent layout shift
              >
                {children}
              </main>
              <Footer />
            </div>
            <PerformanceMonitor showInDevelopment={true} />
            </AnimationProvider>
          </MSWProvider>
        </QueryProvider>
      </body>
    </html>
  );
}
