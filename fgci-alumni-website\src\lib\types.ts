// Core data types for the FGCI Alumni Website

export interface Person {
  id: string;
  name: string;
  role?: string;
  phone?: string;
  email?: string;
  photo?: string;
  graduationYear?: number;
  chapter?: string;
  currentLocation?: string;
  bio?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
}

export interface Chapter {
  id: string;
  slug: string;
  name: string;
  region?: string;
  type: 'regional' | 'diaspora' | 'school-based';
  summary?: string;
  description?: string;
  heroImage?: string;
  exco: Person[];
  activities: Activity[];
  contactEmail?: string;
  establishedDate?: string;
  lastActivityDate?: string;
  memberCount?: number;
  gallery?: string[];
}

export interface Set {
  id: string;
  slug: string;
  name: string;
  year: number;
  motto?: string;
  description?: string;
  heroImage?: string;
  leader?: Person;
  exco: Person[];
  activities: Activity[];
  gallery: string[];
  documents?: Document[];
  memberCount?: number;
}

export interface Activity {
  id: string;
  date: string;
  title: string;
  description: string;
  photos?: string[];
  location?: string;
  type: 'meeting' | 'event' | 'fundraiser' | 'social' | 'memorial';
}

export interface Event {
  id: string;
  slug: string;
  title: string;
  description: string;
  date: string;
  endDate?: string;
  location: string;
  heroImage?: string;
  gallery?: string[];
  organizer: string;
  registrationUrl?: string;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
}

export interface GalleryImage {
  id: string;
  url: string;
  thumbnailUrl: string;
  alt: string;
  caption?: string;
  photographer?: string;
  date?: string;
  album?: string;
  tags?: string[];
  width?: number;
  height?: number;
  fileSize?: number;
}

export interface GalleryAlbum {
  id: string;
  name: string;
  description?: string;
  coverImage?: GalleryImage;
  images: GalleryImage[];
  createdDate: string;
  updatedDate?: string;
  isPublic: boolean;
  tags?: string[];
}

export interface Memorial {
  id: string;
  person: Person;
  dateOfPassing: string;
  tribute: string;
  condolences?: Condolence[];
  photos?: string[];
}

export interface Condolence {
  id: string;
  name: string;
  message: string;
  date: string;
  approved: boolean;
}

export interface Document {
  id: string;
  title: string;
  description?: string;
  url: string;
  type: 'pdf' | 'doc' | 'image' | 'other';
  size?: number;
  uploadDate: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasNext?: boolean;
    timestamp?: string;
  };
  error?: string;
  success: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrevious: boolean;
    totalPages: number;
    timestamp: string;
  };
}

export interface SearchResponse<T> extends PaginatedResponse<T> {
  meta: PaginatedResponse<T>['meta'] & {
    query?: string;
    filters?: Record<string, unknown>;
    searchTime?: number;
  };
}

// Error Types
export interface ApiError {
  status: number;
  message: string;
  code?: string;
  details?: Record<string, unknown>;
  timestamp?: string;
  path?: string;
}

export interface ValidationError extends ApiError {
  fieldErrors?: FormErrors;
}

export interface NetworkError {
  type: 'network';
  message: string;
  isOffline?: boolean;
  retryable: boolean;
}

export interface ServerError extends ApiError {
  type: 'server';
  retryable: boolean;
}

export interface ClientError extends ApiError {
  type: 'client';
  retryable: false;
}

export type AppError = NetworkError | ServerError | ClientError | ValidationError;

// Form Types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  graduationYear?: number;
  inquiryType?: string;
  chapter?: string;
}

export interface CondolenceFormData {
  name: string;
  message: string;
  relationship?: string;
}

export interface SearchFilters {
  query?: string;
  chapter?: string;
  graduationYear?: number;
  role?: string;
  location?: string;
  type?: string;
  region?: string;
  position?: string;
  termStatus?: string;
}

export interface ExcoBotFilters extends SearchFilters {
  currentRole?: string;
  availability?: 'available' | 'busy' | 'any';
}

// Validation Types
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  custom?: (value: unknown) => string | null;
}

export interface ValidationSchema {
  [fieldName: string]: ValidationRule[];
}

export interface FormErrors {
  [fieldName: string]: string[];
}

export interface FormState<T> {
  data: T;
  errors: FormErrors;
  isSubmitting: boolean;
  isValid: boolean;
  touched: Record<keyof T, boolean>;
}

// Component Props Types
export interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export interface CardProps {
  variant: 'default' | 'elevated' | 'outlined';
  padding: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  hover?: boolean;
  onClick?: () => void;
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size: 'sm' | 'md' | 'lg' | 'xl' | 'fullscreen';
  children: React.ReactNode;
  closeOnOverlayClick?: boolean;
  className?: string;
}

// API Endpoint Types
export interface ApiEndpoints {
  chapters: {
    list: () => string;
    detail: (slug: string) => string;
    activities: (slug: string) => string;
    gallery: (slug: string) => string;
  };
  sets: {
    list: () => string;
    detail: (slug: string) => string;
    activities: (slug: string) => string;
    gallery: (slug: string) => string;
  };
  events: {
    list: () => string;
    detail: (slug: string) => string;
  };
  gallery: {
    list: () => string;
    albums: () => string;
    album: (id: string) => string;
  };
  excobot: {
    search: () => string;
  };
  memorial: {
    list: () => string;
    condolences: (id: string) => string;
  };
  contact: {
    submit: () => string;
  };
}

export interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: unknown;
  params?: Record<string, string | number | boolean>;
  timeout?: number;
  retries?: number;
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface QueryState<T> extends AsyncState<T> {
  isStale: boolean;
  lastFetch?: Date;
  refetch: () => Promise<void>;
}

// Theme Types
export interface ThemeColors {
  maroon: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    DEFAULT: string;
  };
  gold: {
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
    DEFAULT: string;
  };
}