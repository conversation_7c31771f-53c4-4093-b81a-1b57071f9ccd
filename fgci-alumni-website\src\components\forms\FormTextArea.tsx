'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface FormTextAreaProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  touched?: boolean;
  rows?: number;
  maxLength?: number;
  className?: string;
  helpText?: string;
  showCharCount?: boolean;
}

export function FormTextArea({
  label,
  name,
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  errors = [],
  touched = false,
  rows = 4,
  maxLength,
  className,
  helpText,
  showCharCount = false,
}: FormTextAreaProps) {
  const hasError = touched && errors.length > 0;
  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;
  const helpId = `${fieldId}-help`;
  const charCount = value.length;

  return (
    <div className={cn('space-y-2', className)}>
      <label 
        htmlFor={fieldId} 
        className="block text-sm font-medium text-gray-700"
      >
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </label>
      
      <textarea
        id={fieldId}
        name={name}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        rows={rows}
        maxLength={maxLength}
        className={cn(
          'w-full px-3 py-2 border rounded-lg transition-colors resize-vertical',
          'focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500',
          'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
          hasError 
            ? 'border-red-500 focus:ring-red-500 focus:border-red-500' 
            : 'border-gray-300'
        )}
        aria-invalid={hasError}
        aria-describedby={cn(
          hasError && errorId,
          helpText && helpId
        )}
      />
      
      <div className="flex justify-between items-start">
        <div className="flex-1">
          {helpText && !hasError && (
            <p id={helpId} className="text-sm text-gray-600">
              {helpText}
            </p>
          )}
          
          {hasError && (
            <div id={errorId} className="space-y-1" role="alert">
              {errors.map((error, index) => (
                <p key={index} className="text-sm text-red-600">
                  {error}
                </p>
              ))}
            </div>
          )}
        </div>
        
        {showCharCount && maxLength && (
          <p className={cn(
            'text-sm ml-4 flex-shrink-0',
            charCount > maxLength * 0.9 
              ? 'text-orange-600' 
              : charCount === maxLength 
                ? 'text-red-600' 
                : 'text-gray-500'
          )}>
            {charCount}/{maxLength}
          </p>
        )}
      </div>
    </div>
  );
}