'use client';

import React, { useEffect, useRef, useState } from 'react';
import { animateWithPreset, createScrollAnimationObserver, prefersReducedMotion } from '@/lib/animations';
import { cn } from '@/lib/utils';

interface FadeInProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  duration?: number;
  triggerOnce?: boolean;
  threshold?: number;
  rootMargin?: string;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
}

export function FadeIn({
  children,
  className,
  delay = 0,
  duration = 600,
  triggerOnce = true,
  threshold = 0.1,
  rootMargin = '0px 0px -10% 0px',
  direction = 'up'
}: FadeInProps) {
  const elementRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // If reduced motion is preferred, show immediately
    if (prefersReducedMotion()) {
      setIsVisible(true);
      return;
    }

    const observer = createScrollAnimationObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && (!triggerOnce || !hasAnimated)) {
            setIsVisible(true);
            setHasAnimated(true);
            
            // Animate based on direction
            const preset = direction === 'up' ? 'slideInUp' :
                          direction === 'down' ? 'slideInDown' :
                          direction === 'left' ? 'slideInLeft' :
                          direction === 'right' ? 'slideInRight' :
                          'fadeIn';
            
            setTimeout(() => {
              animateWithPreset(element, preset, delay);
            }, 50);
            
            if (triggerOnce) {
              observer.unobserve(element);
            }
          } else if (!entry.isIntersecting && !triggerOnce) {
            setIsVisible(false);
          }
        });
      },
      {
        threshold,
        rootMargin
      }
    );

    observer.observe(element);

    return () => observer.disconnect();
  }, [delay, triggerOnce, threshold, rootMargin, direction, hasAnimated]);

  const getInitialStyles = () => {
    if (prefersReducedMotion() || isVisible) {
      return {};
    }

    const baseStyles = { opacity: 0 };
    
    switch (direction) {
      case 'up':
        return { ...baseStyles, transform: 'translateY(30px)' };
      case 'down':
        return { ...baseStyles, transform: 'translateY(-30px)' };
      case 'left':
        return { ...baseStyles, transform: 'translateX(-30px)' };
      case 'right':
        return { ...baseStyles, transform: 'translateX(30px)' };
      default:
        return baseStyles;
    }
  };

  return (
    <div
      ref={elementRef}
      className={cn('transition-opacity', className)}
      style={getInitialStyles()}
    >
      {children}
    </div>
  );
}

export default FadeIn;
