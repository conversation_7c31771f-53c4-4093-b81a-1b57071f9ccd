import { Metadata } from 'next';
import { ExcoBotSearch } from '@/components/excobot/ExcoBotSearch';

export const metadata: Metadata = {
  title: 'ExcoBot Alumni Directory | FGC Ikom Alumni',
  description: 'Search and connect with FGC Ikom alumni across all chapters and graduation sets. Find contact information, roles, and locations.',
  keywords: 'FGC Ikom alumni directory, ExcoBot, alumni search, contact alumni, chapter executives',
  openGraph: {
    title: 'ExcoBot Alumni Directory | FGC Ikom Alumni',
    description: 'Search and connect with FGC Ikom alumni across all chapters and graduation sets.',
    type: 'website',
  },
};

export default function ExcoBotPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-maroon-700 to-maroon-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold font-heading mb-6">
              ExcoBot Alumni Directory
            </h1>
            <p className="text-xl md:text-2xl text-maroon-100 mb-8">
              Connect with fellow FGC Ikom alumni across all chapters and graduation sets
            </p>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-left max-w-2xl mx-auto">
              <h2 className="text-lg font-semibold mb-3 text-gold-300">
                🤖 What is ExcoBot?
              </h2>
              <p className="text-maroon-100 leading-relaxed">
                ExcoBot is your intelligent alumni directory assistant. Search by name, chapter, 
                graduation year, role, or location to find and connect with fellow alumni. 
                Access contact information, current positions, and professional profiles.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Search Interface */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <ExcoBotSearch />
        </div>
      </section>
    </div>
  );
}