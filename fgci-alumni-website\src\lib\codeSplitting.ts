/**
 * Code splitting utilities for lazy loading components and routes
 */

import { lazy, ComponentType, LazyExoticComponent } from 'react';

/**
 * Enhanced lazy loading with error boundaries and loading states
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: {
    fallback?: ComponentType;
    errorBoundary?: ComponentType<{ error: Error; retry: () => void }>;
    preload?: boolean;
  } = {}
): LazyExoticComponent<T> {
  const LazyComponent = lazy(importFn);
  
  // Preload the component if requested
  if (options.preload) {
    importFn().catch(console.error);
  }
  
  return LazyComponent;
}

/**
 * Preload a lazy component
 */
export function preloadComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
): Promise<{ default: T }> {
  return importFn();
}

/**
 * Create a lazy route component with enhanced error handling
 */
export function createLazyRoute<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  routeName: string
): LazyExoticComponent<T> {
  return createLazyComponent(importFn, {
    preload: false, // Routes are loaded on demand
  });
}

/**
 * Lazy load heavy components that are not immediately visible
 */
export const LazyComponents = {
  // Gallery components (heavy due to image processing)
  Lightbox: createLazyComponent(
    () => import('@/components/gallery/Lightbox'),
    { preload: false }
  ),
  
  GalleryGrid: createLazyComponent(
    () => import('@/components/gallery/GalleryGrid'),
    { preload: false }
  ),
  
  // Chart/visualization components (if any)
  Timeline: createLazyComponent(
    () => import('@/components/ui/Timeline'),
    { preload: false }
  ),
  
  // Form components (loaded when needed)
  ContactForm: createLazyComponent(
    () => import('@/components/forms/ContactForm'),
    { preload: false }
  ),
  
  CondolenceForm: createLazyComponent(
    () => import('@/components/memorial/CondolenceForm'),
    { preload: false }
  ),
  
  // Search components (loaded when search is initiated)
  ExcoBotSearch: createLazyComponent(
    () => import('@/components/excobot/ExcoBotSearch'),
    { preload: false }
  ),
  
  // Modal components (loaded when opened)
  MemorialModal: createLazyComponent(
    () => import('@/components/memorial/MemorialModal'),
    { preload: false }
  ),
} as const;

/**
 * Route-level lazy loading
 */
export const LazyRoutes = {
  ChaptersPage: createLazyRoute(
    () => import('@/app/chapters/page'),
    'chapters'
  ),
  
  ChapterDetailPage: createLazyRoute(
    () => import('@/app/chapters/[slug]/page'),
    'chapter-detail'
  ),
  
  SetsPage: createLazyRoute(
    () => import('@/app/sets/page'),
    'sets'
  ),
  
  SetDetailPage: createLazyRoute(
    () => import('@/app/sets/[slug]/page'),
    'set-detail'
  ),
  
  EventsPage: createLazyRoute(
    () => import('@/app/events/page'),
    'events'
  ),
  
  EventDetailPage: createLazyRoute(
    () => import('@/app/events/[slug]/page'),
    'event-detail'
  ),
  
  GalleryPage: createLazyRoute(
    () => import('@/app/gallery/page'),
    'gallery'
  ),
  
  ExcoBotPage: createLazyRoute(
    () => import('@/app/excobot/page'),
    'excobot'
  ),
} as const;

/**
 * Intersection Observer for component preloading
 */
export class ComponentPreloader {
  private observer: IntersectionObserver | null = null;
  private preloadMap = new Map<string, () => Promise<any>>();
  
  constructor() {
    if (typeof window !== 'undefined' && 'IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const componentId = entry.target.getAttribute('data-preload');
              if (componentId && this.preloadMap.has(componentId)) {
                const preloadFn = this.preloadMap.get(componentId)!;
                preloadFn().catch(console.error);
                this.observer?.unobserve(entry.target);
                this.preloadMap.delete(componentId);
              }
            }
          });
        },
        {
          rootMargin: '200px 0px',
          threshold: 0.1,
        }
      );
    }
  }
  
  registerPreload(
    element: Element,
    componentId: string,
    preloadFn: () => Promise<any>
  ): void {
    if (!this.observer) return;
    
    element.setAttribute('data-preload', componentId);
    this.preloadMap.set(componentId, preloadFn);
    this.observer.observe(element);
  }
  
  disconnect(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.preloadMap.clear();
    }
  }
}

/**
 * Global preloader instance
 */
export const componentPreloader = new ComponentPreloader();

/**
 * Hook for preloading components on hover or focus
 */
export function useComponentPreload() {
  const preloadOnHover = (
    componentId: keyof typeof LazyComponents,
    element: HTMLElement | null
  ) => {
    if (!element) return;
    
    const preloadFn = () => {
      switch (componentId) {
        case 'Lightbox':
          return import('@/components/gallery/Lightbox');
        case 'GalleryGrid':
          return import('@/components/gallery/GalleryGrid');
        case 'ContactForm':
          return import('@/components/forms/ContactForm');
        case 'ExcoBotSearch':
          return import('@/components/excobot/ExcoBotSearch');
        default:
          return Promise.resolve();
      }
    };
    
    const handleMouseEnter = () => {
      preloadFn().catch(console.error);
      element.removeEventListener('mouseenter', handleMouseEnter);
    };
    
    element.addEventListener('mouseenter', handleMouseEnter, { once: true });
  };
  
  return { preloadOnHover };
}

/**
 * Bundle splitting configuration for webpack
 */
export const bundleSplitConfig = {
  // Vendor chunks
  vendor: {
    react: ['react', 'react-dom'],
    query: ['@tanstack/react-query'],
    framer: ['framer-motion'],
    ui: ['@headlessui/react', '@heroicons/react'],
  },
  
  // Feature chunks
  features: {
    gallery: ['@/components/gallery', '@/hooks/useGallery'],
    forms: ['@/components/forms', '@/hooks/useForm'],
    search: ['@/components/excobot', '@/hooks/useAlumniSearch'],
    memorial: ['@/components/memorial', '@/hooks/useMemorials'],
  },
} as const;

/**
 * Dynamic import with retry logic
 */
export async function dynamicImportWithRetry<T>(
  importFn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000
): Promise<T> {
  for (let i = 0; i < retries; i++) {
    try {
      return await importFn();
    } catch (error) {
      if (i === retries - 1) throw error;
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
    }
  }
  
  throw new Error('Dynamic import failed after retries');
}

/**
 * Prefetch critical routes based on user behavior
 */
export function prefetchCriticalRoutes(): void {
  if (typeof window === 'undefined') return;
  
  // Prefetch likely next routes based on current page
  const currentPath = window.location.pathname;
  
  if (currentPath === '/') {
    // From homepage, users likely go to chapters or sets
    preloadComponent(() => import('@/app/chapters/page')).catch(console.error);
    preloadComponent(() => import('@/app/sets/page')).catch(console.error);
  } else if (currentPath.startsWith('/chapters')) {
    // From chapters, users might go to gallery or sets
    preloadComponent(() => import('@/app/gallery/page')).catch(console.error);
    preloadComponent(() => import('@/app/sets/page')).catch(console.error);
  }
}

/**
 * Service Worker registration for caching
 */
export function registerServiceWorker(): void {
  if (
    typeof window !== 'undefined' &&
    'serviceWorker' in navigator &&
    process.env.NODE_ENV === 'production'
  ) {
    window.addEventListener('load', () => {
      navigator.serviceWorker
        .register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    });
  }
}