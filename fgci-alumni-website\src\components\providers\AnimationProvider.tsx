'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { createReducedMotionListener } from '@/utils/animationUtils';

interface AnimationContextType {
  prefersReducedMotion: boolean;
  disableAnimations: boolean;
  setDisableAnimations: (disabled: boolean) => void;
}

const AnimationContext = createContext<AnimationContextType | undefined>(undefined);

interface AnimationProviderProps {
  children: React.ReactNode;
}

export function AnimationProvider({ children }: AnimationProviderProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  const [disableAnimations, setDisableAnimations] = useState(false);

  useEffect(() => {
    // Set up listener for reduced motion preference changes
    const cleanup = createReducedMotionListener((matches) => {
      setPrefersReducedMotion(matches);
      
      // Update CSS custom property for global animation control
      document.documentElement.style.setProperty(
        '--animation-duration',
        matches ? '0.01ms' : '300ms'
      );
      
      // Add/remove class for CSS-based animation control
      if (matches) {
        document.documentElement.classList.add('reduce-motion');
      } else {
        document.documentElement.classList.remove('reduce-motion');
      }
    });

    return cleanup;
  }, []);

  // Effect to handle manual animation disable
  useEffect(() => {
    if (disableAnimations) {
      document.documentElement.classList.add('disable-animations');
    } else {
      document.documentElement.classList.remove('disable-animations');
    }
  }, [disableAnimations]);

  const value: AnimationContextType = {
    prefersReducedMotion: prefersReducedMotion || disableAnimations,
    disableAnimations,
    setDisableAnimations,
  };

  return (
    <AnimationContext.Provider value={value}>
      {children}
    </AnimationContext.Provider>
  );
}

export function useAnimation() {
  const context = useContext(AnimationContext);
  if (context === undefined) {
    throw new Error('useAnimation must be used within an AnimationProvider');
  }
  return context;
}

// Hook that combines the context with the existing useReducedMotion hook
export function useMotionPreference() {
  const context = useContext(AnimationContext);
  
  // Fallback if provider is not available
  if (context === undefined) {
    const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
    
    useEffect(() => {
      if (typeof window === 'undefined') return;
      
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setPrefersReducedMotion(mediaQuery.matches);
      
      const handleChange = (event: MediaQueryListEvent) => {
        setPrefersReducedMotion(event.matches);
      };
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }, []);
    
    return { prefersReducedMotion, disableAnimations: false, setDisableAnimations: () => {} };
  }
  
  return context;
}