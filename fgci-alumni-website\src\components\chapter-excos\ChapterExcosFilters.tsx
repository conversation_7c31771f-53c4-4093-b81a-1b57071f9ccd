'use client';

import { useState } from 'react';
import { FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { SearchFilters } from '@/lib/types';

interface ChapterExcosFiltersProps {
  filters: SearchFilters;
  onFilterChange: (filters: SearchFilters) => void;
  onClearFilters: () => void;
}

const CHAPTER_OPTIONS = [
  { value: 'lagos', label: 'Lagos Chapter' },
  { value: 'abuja', label: 'Abuja Chapter' },
  { value: 'uk-diaspora', label: 'UK Diaspora' },
  { value: 'usa-diaspora', label: 'USA Diaspora' },
  { value: 'canada-diaspora', label: 'Canada Diaspora' },
  { value: 'port-harcourt', label: 'Port Harcourt Chapter' },
  { value: 'kano', label: 'Kano Chapter' },
  { value: 'ibadan', label: 'Ibadan Chapter' },
];

const POSITION_OPTIONS = [
  { value: 'President', label: 'President' },
  { value: 'Vice President', label: 'Vice President' },
  { value: 'Secretary', label: 'Secretary' },
  { value: 'Treasurer', label: 'Treasurer' },
  { value: 'Financial Secretary', label: 'Financial Secretary' },
  { value: 'Public Relations Officer', label: 'PRO' },
  { value: 'Social Secretary', label: 'Social Secretary' },
  { value: 'Welfare Officer', label: 'Welfare Officer' },
];

const POSITION_TYPE_OPTIONS = [
  { value: 'executive', label: 'Executive Positions' },
  { value: 'officer', label: 'Officer Positions' },
  { value: 'secretary', label: 'Secretary Positions' },
  { value: 'financial', label: 'Financial Positions' },
];

const TERM_STATUS_OPTIONS = [
  { value: 'current', label: 'Current Term' },
  { value: 'past', label: 'Past Term' },
  { value: 'upcoming', label: 'Upcoming Term' },
];

const REGION_OPTIONS = [
  { value: 'Lagos State', label: 'Lagos State' },
  { value: 'Federal Capital Territory', label: 'FCT Abuja' },
  { value: 'Rivers State', label: 'Rivers State' },
  { value: 'Kano State', label: 'Kano State' },
  { value: 'Oyo State', label: 'Oyo State' },
  { value: 'United Kingdom', label: 'United Kingdom' },
  { value: 'United States', label: 'United States' },
  { value: 'Canada', label: 'Canada' },
];

export function ChapterExcosFilters({
  filters,
  onFilterChange,
  onClearFilters
}: ChapterExcosFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleFilterUpdate = (key: keyof SearchFilters, value: string) => {
    const newFilters = { ...filters };
    if (value === '') {
      delete newFilters[key];
    } else {
      newFilters[key] = value;
    }
    onFilterChange(newFilters);
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Filter Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 text-gray-700 hover:text-maroon-600 transition-colors"
        >
          <FunnelIcon className="h-5 w-5" />
          <span className="font-medium">Filters</span>
          {hasActiveFilters && (
            <span className="bg-maroon-100 text-maroon-800 text-xs px-2 py-1 rounded-full">
              {Object.keys(filters).length}
            </span>
          )}
        </button>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <XMarkIcon className="h-4 w-4 mr-1" />
            Clear all
          </Button>
        )}
      </div>

      {/* Filter Content */}
      {isExpanded && (
        <div className="p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
            {/* Chapter Filter */}
            <div>
              <label htmlFor="chapter-filter" className="block text-sm font-medium text-gray-700 mb-2">
                Chapter
              </label>
              <select
                id="chapter-filter"
                value={filters.chapter || ''}
                onChange={(e) => handleFilterUpdate('chapter', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
              >
                <option value="">All Chapters</option>
                {CHAPTER_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Position Type Filter */}
            <div>
              <label htmlFor="position-type-filter" className="block text-sm font-medium text-gray-700 mb-2">
                Position Type
              </label>
              <select
                id="position-type-filter"
                value={filters.type || ''}
                onChange={(e) => handleFilterUpdate('type', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
              >
                <option value="">All Types</option>
                {POSITION_TYPE_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Position Filter */}
            <div>
              <label htmlFor="position-filter" className="block text-sm font-medium text-gray-700 mb-2">
                Specific Position
              </label>
              <select
                id="position-filter"
                value={filters.position || ''}
                onChange={(e) => handleFilterUpdate('position', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
              >
                <option value="">All Positions</option>
                {POSITION_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Region Filter */}
            <div>
              <label htmlFor="region-filter" className="block text-sm font-medium text-gray-700 mb-2">
                Region
              </label>
              <select
                id="region-filter"
                value={filters.region || ''}
                onChange={(e) => handleFilterUpdate('region', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
              >
                <option value="">All Regions</option>
                {REGION_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Term Status Filter */}
            <div>
              <label htmlFor="term-status-filter" className="block text-sm font-medium text-gray-700 mb-2">
                Term Status
              </label>
              <select
                id="term-status-filter"
                value={filters.termStatus || ''}
                onChange={(e) => handleFilterUpdate('termStatus', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
              >
                <option value="">All Terms</option>
                {TERM_STATUS_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="pt-4 border-t border-gray-200">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm text-gray-600">Active filters:</span>
                {Object.entries(filters).map(([key, value]) => (
                  <span
                    key={key}
                    className="inline-flex items-center gap-1 bg-maroon-100 text-maroon-800 text-xs px-2 py-1 rounded-full"
                  >
                    {key}: {value}
                    <button
                      onClick={() => handleFilterUpdate(key as keyof SearchFilters, '')}
                      className="hover:text-maroon-900"
                      aria-label={`Remove ${key} filter`}
                    >
                      <XMarkIcon className="h-3 w-3" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}