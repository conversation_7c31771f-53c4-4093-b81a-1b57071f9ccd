'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ScreenReaderOnlyProps {
  children: React.ReactNode;
  className?: string;
  as?: React.ElementType;
}

/**
 * Component that renders content only for screen readers
 * Content is visually hidden but accessible to assistive technologies
 */
export function ScreenReaderOnly({ 
  children, 
  className,
  as: Component = 'span'
}: ScreenReaderOnlyProps) {
  return React.createElement(
    Component,
    {
      className: cn('sr-only', className)
    },
    children
  );
}

/**
 * Component that can toggle between screen reader only and visible
 */
interface ConditionalScreenReaderProps {
  children: React.ReactNode;
  showVisually?: boolean;
  className?: string;
  as?: React.ElementType;
}

export function ConditionalScreenReader({ 
  children, 
  showVisually = false,
  className,
  as: Component = 'span'
}: ConditionalScreenReaderProps) {
  return React.createElement(
    Component,
    {
      className: cn(
        showVisually ? 'not-sr-only' : 'sr-only',
        className
      )
    },
    children
  );
}