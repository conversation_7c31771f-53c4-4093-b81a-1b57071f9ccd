'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, Button } from '@/components/ui';
import { FadeIn, StaggeredList, HoverCard } from '@/components/animations';
import { 
  SparklesIcon,
  EyeIcon,
  CursorArrowRaysIcon,
  ArrowPathIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

export default function AnimationsTestPage() {
  const [refreshKey, setRefreshKey] = useState(0);
  const [showStaggered, setShowStaggered] = useState(false);

  const refreshAnimations = () => {
    setRefreshKey(prev => prev + 1);
    setShowStaggered(false);
    setTimeout(() => setShowStaggered(true), 100);
  };

  const testItems = [
    { title: 'First Item', description: 'This is the first animated item' },
    { title: 'Second Item', description: 'This is the second animated item' },
    { title: 'Third Item', description: 'This is the third animated item' },
    { title: 'Fourth Item', description: 'This is the fourth animated item' },
    { title: 'Fifth Item', description: 'This is the fifth animated item' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container-custom py-12">
          <FadeIn direction="down">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
                Animations & Microinteractions
              </h1>
              <p className="body-large text-gray-600">
                Experience the smooth animations and delightful microinteractions 
                implemented throughout the FGC Ikom Alumni website.
              </p>
              <div className="mt-6">
                <Button 
                  onClick={refreshAnimations}
                  variant="primary"
                  icon={<ArrowPathIcon className="w-4 h-4" />}
                >
                  Refresh Animations
                </Button>
              </div>
            </div>
          </FadeIn>
        </div>
      </div>

      <div className="container-custom py-12">
        <div className="space-y-12">
          {/* Fade In Animations */}
          <section key={`fade-${refreshKey}`}>
            <FadeIn direction="up">
              <h2 className="heading-2 text-3xl mb-8 text-center">Fade In Animations</h2>
            </FadeIn>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <FadeIn direction="up" delay={100}>
                <Card variant="elevated">
                  <CardContent className="p-6 text-center">
                    <EyeIcon className="w-8 h-8 text-maroon-700 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Fade Up</h3>
                    <p className="text-sm text-gray-600">Slides up while fading in</p>
                  </CardContent>
                </Card>
              </FadeIn>

              <FadeIn direction="down" delay={200}>
                <Card variant="elevated">
                  <CardContent className="p-6 text-center">
                    <SparklesIcon className="w-8 h-8 text-maroon-700 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Fade Down</h3>
                    <p className="text-sm text-gray-600">Slides down while fading in</p>
                  </CardContent>
                </Card>
              </FadeIn>

              <FadeIn direction="left" delay={300}>
                <Card variant="elevated">
                  <CardContent className="p-6 text-center">
                    <CursorArrowRaysIcon className="w-8 h-8 text-maroon-700 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Fade Left</h3>
                    <p className="text-sm text-gray-600">Slides from left while fading in</p>
                  </CardContent>
                </Card>
              </FadeIn>

              <FadeIn direction="right" delay={400}>
                <Card variant="elevated">
                  <CardContent className="p-6 text-center">
                    <PlayIcon className="w-8 h-8 text-maroon-700 mx-auto mb-3" />
                    <h3 className="font-semibold mb-2">Fade Right</h3>
                    <p className="text-sm text-gray-600">Slides from right while fading in</p>
                  </CardContent>
                </Card>
              </FadeIn>
            </div>
          </section>

          {/* Staggered Animations */}
          <section key={`stagger-${refreshKey}`}>
            <FadeIn direction="up">
              <h2 className="heading-2 text-3xl mb-8 text-center">Staggered List Animation</h2>
            </FadeIn>

            {showStaggered && (
              <StaggeredList staggerDelay={150} direction="up">
                {testItems.map((item, index) => (
                  <Card key={index} variant="elevated">
                    <CardContent className="p-6">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-maroon-100 rounded-full flex items-center justify-center">
                          <span className="text-maroon-700 font-semibold">{index + 1}</span>
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{item.title}</h3>
                          <p className="text-gray-600 text-sm">{item.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </StaggeredList>
            )}
          </section>

          {/* Hover Effects */}
          <section>
            <FadeIn direction="up">
              <h2 className="heading-2 text-3xl mb-8 text-center">Hover Effects</h2>
              <p className="text-center text-gray-600 mb-8">
                Hover over the cards below to see different hover effects
              </p>
            </FadeIn>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <FadeIn direction="up" delay={100}>
                <HoverCard hoverEffect="lift" intensity="medium">
                  <Card variant="elevated">
                    <CardContent className="p-6 text-center">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-blue-600 font-semibold">↑</span>
                      </div>
                      <h3 className="font-semibold mb-2">Lift Effect</h3>
                      <p className="text-sm text-gray-600">Card lifts up with shadow</p>
                    </CardContent>
                  </Card>
                </HoverCard>
              </FadeIn>

              <FadeIn direction="up" delay={200}>
                <HoverCard hoverEffect="scale" intensity="medium">
                  <Card variant="elevated">
                    <CardContent className="p-6 text-center">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-green-600 font-semibold">⚡</span>
                      </div>
                      <h3 className="font-semibold mb-2">Scale Effect</h3>
                      <p className="text-sm text-gray-600">Card scales up slightly</p>
                    </CardContent>
                  </Card>
                </HoverCard>
              </FadeIn>

              <FadeIn direction="up" delay={300}>
                <HoverCard hoverEffect="glow" intensity="medium">
                  <Card variant="elevated">
                    <CardContent className="p-6 text-center">
                      <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-yellow-600 font-semibold">✨</span>
                      </div>
                      <h3 className="font-semibold mb-2">Glow Effect</h3>
                      <p className="text-sm text-gray-600">Card glows with brand color</p>
                    </CardContent>
                  </Card>
                </HoverCard>
              </FadeIn>

              <FadeIn direction="up" delay={400}>
                <HoverCard hoverEffect="tilt" intensity="subtle">
                  <Card variant="elevated">
                    <CardContent className="p-6 text-center">
                      <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span className="text-purple-600 font-semibold">↗</span>
                      </div>
                      <h3 className="font-semibold mb-2">Tilt Effect</h3>
                      <p className="text-sm text-gray-600">Card tilts in 3D space</p>
                    </CardContent>
                  </Card>
                </HoverCard>
              </FadeIn>
            </div>
          </section>

          {/* Interactive Elements */}
          <section>
            <FadeIn direction="up">
              <h2 className="heading-2 text-3xl mb-8 text-center">Interactive Elements</h2>
            </FadeIn>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FadeIn direction="up" delay={100}>
                <Card variant="elevated">
                  <CardHeader>
                    <CardTitle>Button Animations</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Button variant="primary" className="w-full">
                      Primary Button
                    </Button>
                    <Button variant="outline" className="w-full">
                      Outline Button
                    </Button>
                    <Button variant="ghost" className="w-full">
                      Ghost Button
                    </Button>
                  </CardContent>
                </Card>
              </FadeIn>

              <FadeIn direction="up" delay={200}>
                <Card variant="elevated">
                  <CardHeader>
                    <CardTitle>Form Interactions</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <input
                      type="text"
                      placeholder="Focus me for animation"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500 transition-all duration-200"
                    />
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500 transition-all duration-200">
                      <option>Select an option</option>
                      <option>Option 1</option>
                      <option>Option 2</option>
                    </select>
                  </CardContent>
                </Card>
              </FadeIn>

              <FadeIn direction="up" delay={300}>
                <Card variant="elevated">
                  <CardHeader>
                    <CardTitle>Loading States</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-maroon-700 border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-sm">Loading...</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-maroon-700 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                    </div>
                  </CardContent>
                </Card>
              </FadeIn>
            </div>
          </section>

          {/* Reduced Motion Notice */}
          <section>
            <FadeIn direction="up">
              <Card variant="elevated" className="bg-blue-50 border-blue-200">
                <CardContent className="p-6 text-center">
                  <h3 className="font-semibold text-blue-900 mb-2">Accessibility Note</h3>
                  <p className="text-blue-800 text-sm">
                    All animations respect the user's motion preferences. If you have 
                    "reduce motion" enabled in your system settings, animations will be 
                    minimized or disabled entirely.
                  </p>
                </CardContent>
              </Card>
            </FadeIn>
          </section>
        </div>
      </div>
    </div>
  );
}
