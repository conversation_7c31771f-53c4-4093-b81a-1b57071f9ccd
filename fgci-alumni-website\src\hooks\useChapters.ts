import { useQuery } from '@tanstack/react-query';
import { apiClient, apiEndpoints } from '@/lib/api-client';
import type { Chapter, PaginatedResponse } from '@/lib/types';

interface UseChaptersOptions {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  region?: string;
  enabled?: boolean;
}

export function useChapters(options: UseChaptersOptions = {}) {
  const {
    page = 1,
    limit = 10,
    search,
    type,
    region,
    enabled = true
  } = options;

  return useQuery({
    queryKey: ['chapters', { page, limit, search, type, region }],
    queryFn: async () => {
      const params: Record<string, string | number> = {
        page,
        limit
      };

      if (search) params.search = search;
      if (type && type !== 'all') params.type = type;
      if (region) params.region = region;

      const response = await apiClient.get<Chapter[]>(
        apiEndpoints.chapters.list(),
        params
      );

      return response as PaginatedResponse<Chapter>;
    },
    enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useChapter(slug: string, enabled: boolean = true) {
  return useQuery({
    queryKey: ['chapter', slug],
    queryFn: async () => {
      const response = await apiClient.get<Chapter>(
        apiEndpoints.chapters.detail(slug)
      );
      return response.data;
    },
    enabled: enabled && !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}