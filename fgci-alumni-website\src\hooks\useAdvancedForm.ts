'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { ValidationSchema, FormErrors } from '@/lib/types';
import { validateField, validateForm } from '@/lib/validation';

interface UseAdvancedFormOptions<T> {
  initialData: T;
  validationSchema: ValidationSchema;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
  onSubmit?: (data: T) => void | Promise<void>;
  onFieldChange?: (fieldName: keyof T, value: unknown, data: T) => void;
  onValidationChange?: (isValid: boolean, errors: FormErrors) => void;
}

export function useAdvancedForm<T extends Record<string, unknown>>({
  initialData,
  validationSchema,
  validateOnChange = false,
  validateOnBlur = true,
  debounceMs = 300,
  onSubmit,
  onFieldChange,
  onValidationChange,
}: UseAdvancedFormOptions<T>) {
  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  
  const debounceTimeouts = useRef<Record<string, NodeJS.Timeout>>({});
  const formRef = useRef<HTMLFormElement>(null);

  // Clear debounce timeout for a field
  const clearDebounceTimeout = useCallback((fieldName: string) => {
    if (debounceTimeouts.current[fieldName]) {
      clearTimeout(debounceTimeouts.current[fieldName]);
      delete debounceTimeouts.current[fieldName];
    }
  }, []);

  // Validate a single field with optional debouncing
  const validateSingleField = useCallback((
    fieldName: keyof T,
    value: unknown,
    immediate = false
  ): Promise<string[]> => {
    return new Promise((resolve) => {
      const fieldNameStr = fieldName as string;
      
      // Clear existing timeout
      clearDebounceTimeout(fieldNameStr);
      
      const performValidation = () => {
        const fieldRules = validationSchema[fieldNameStr];
        if (!fieldRules) {
          resolve([]);
          return;
        }
        
        setIsValidating(true);
        const fieldErrors = validateField(value, fieldRules);
        
        setErrors(prev => {
          const newErrors = {
            ...prev,
            [fieldNameStr]: fieldErrors
          };
          
          // Call validation change callback
          const hasErrors = Object.values(newErrors).some(errs => errs.length > 0);
          onValidationChange?.(!hasErrors, newErrors);
          
          return newErrors;
        });
        
        setIsValidating(false);
        resolve(fieldErrors);
      };

      if (immediate || debounceMs === 0) {
        performValidation();
      } else {
        debounceTimeouts.current[fieldNameStr] = setTimeout(performValidation, debounceMs);
      }
    });
  }, [validationSchema, debounceMs, clearDebounceTimeout, onValidationChange]);

  // Validate all fields
  const validateAllFields = useCallback((formData: T = data): { isValid: boolean; errors: FormErrors } => {
    setIsValidating(true);
    const result = validateForm(formData, validationSchema);
    setErrors(result.errors);
    setIsValidating(false);
    
    onValidationChange?.(result.isValid, result.errors);
    
    return result;
  }, [data, validationSchema, onValidationChange]);

  // Handle field change
  const handleChange = useCallback((
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = event.target;
    
    // Handle different input types
    let processedValue: unknown = value;
    if (type === 'checkbox') {
      processedValue = (event.target as HTMLInputElement).checked;
    } else if (type === 'number') {
      processedValue = value === '' ? '' : Number(value);
    } else if (type === 'file') {
      const fileInput = event.target as HTMLInputElement;
      processedValue = fileInput.files?.[0] || null;
    }

    setData(prev => {
      const newData = {
        ...prev,
        [name]: processedValue
      };
      
      // Call field change callback
      onFieldChange?.(name as keyof T, processedValue, newData);
      
      return newData;
    });

    // Handle validation on change
    if (validateOnChange || (submitAttempted && touched[name as keyof T])) {
      validateSingleField(name as keyof T, processedValue);
    } else {
      // Clear error if field was previously invalid
      if (errors[name]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[name];
          return newErrors;
        });
      }
    }
  }, [validateOnChange, submitAttempted, touched, errors, validateSingleField, onFieldChange]);

  // Handle field blur
  const handleBlur = useCallback((
    event: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = event.target;
    
    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));

    // Validate field on blur
    if (validateOnBlur) {
      validateSingleField(name as keyof T, value, true);
    }
  }, [validateOnBlur, validateSingleField]);

  // Handle form submission
  const handleSubmit = useCallback((
    submitHandler?: (data: T) => void | Promise<void>
  ) => {
    return async (event: React.FormEvent<HTMLFormElement>) => {
      event.preventDefault();
      setSubmitAttempted(true);
      
      // Mark all fields as touched
      const allTouched = Object.keys(data).reduce((acc, key) => {
        acc[key as keyof T] = true;
        return acc;
      }, {} as Record<keyof T, boolean>);
      setTouched(allTouched);

      // Validate all fields
      const { isValid, errors: validationErrors } = validateAllFields();
      
      if (!isValid) {
        // Focus on first error field
        const firstErrorField = Object.keys(validationErrors)[0];
        if (firstErrorField && formRef.current) {
          const errorElement = formRef.current.querySelector(`[name="${firstErrorField}"]`) as HTMLElement;
          errorElement?.focus();
        }
        return;
      }

      setIsSubmitting(true);
      
      try {
        const handler = submitHandler || onSubmit;
        if (handler) {
          await handler(data);
        }
      } catch (error) {
        console.error('Form submission error:', error);
        // You might want to set a general error state here
      } finally {
        setIsSubmitting(false);
      }
    };
  }, [data, validateAllFields, onSubmit]);

  // Set field value programmatically
  const setFieldValue = useCallback((fieldName: keyof T, value: unknown) => {
    setData(prev => {
      const newData = {
        ...prev,
        [fieldName]: value
      };
      
      onFieldChange?.(fieldName, value, newData);
      
      return newData;
    });

    // Clear field error
    if (errors[fieldName as string]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName as string];
        return newErrors;
      });
    }
  }, [errors, onFieldChange]);

  // Set field error programmatically
  const setFieldError = useCallback((fieldName: keyof T, error: string | string[]) => {
    const errorArray = Array.isArray(error) ? error : [error];
    setErrors(prev => ({
      ...prev,
      [fieldName as string]: errorArray
    }));
  }, []);

  // Set multiple field errors
  const setFieldErrors = useCallback((fieldErrors: Record<keyof T, string | string[]>) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      Object.entries(fieldErrors).forEach(([field, error]) => {
        newErrors[field] = Array.isArray(error) ? error : [error];
      });
      return newErrors;
    });
  }, []);

  // Clear field error
  const clearFieldError = useCallback((fieldName: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName as string];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Reset form
  const reset = useCallback((newData?: Partial<T>) => {
    const resetData = newData ? { ...initialData, ...newData } : initialData;
    setData(resetData);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
    setIsSubmitting(false);
    setIsValidating(false);
    setSubmitAttempted(false);
    
    // Clear all debounce timeouts
    Object.values(debounceTimeouts.current).forEach(clearTimeout);
    debounceTimeouts.current = {};
  }, [initialData]);

  // Get field props for easy integration
  const getFieldProps = useCallback((fieldName: keyof T) => ({
    name: fieldName as string,
    value: data[fieldName] || '',
    onChange: handleChange,
    onBlur: handleBlur,
  }), [data, handleChange, handleBlur]);

  // Get field state
  const getFieldState = useCallback((fieldName: keyof T) => ({
    error: errors[fieldName as string] || [],
    hasError: Boolean(errors[fieldName as string]?.length),
    touched: Boolean(touched[fieldName]),
    isValidating: isValidating,
  }), [errors, touched, isValidating]);

  // Check if form is valid
  const isValid = Object.keys(errors).length === 0 && Object.keys(touched).length > 0;
  const hasErrors = Object.keys(errors).length > 0;

  // Auto-save functionality (optional)
  const enableAutoSave = useCallback((
    saveHandler: (data: T) => void | Promise<void>,
    delay = 2000
  ) => {
    const timeoutRef = useRef<NodeJS.Timeout>();
    
    useEffect(() => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        if (isValid && !isSubmitting) {
          saveHandler(data);
        }
      }, delay);
      
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, [data, isValid, isSubmitting, saveHandler, delay]);
  }, [data, isValid, isSubmitting]);

  return {
    // Form state
    data,
    errors,
    touched,
    isSubmitting,
    isValidating,
    isValid,
    hasErrors,
    submitAttempted,
    formRef,
    
    // Handlers
    handleChange,
    handleBlur,
    handleSubmit,
    
    // Utilities
    setFieldValue,
    setFieldError,
    setFieldErrors,
    clearFieldError,
    clearAllErrors,
    reset,
    getFieldProps,
    getFieldState,
    validateField: validateSingleField,
    validateForm: validateAllFields,
    enableAutoSave,
  };
}