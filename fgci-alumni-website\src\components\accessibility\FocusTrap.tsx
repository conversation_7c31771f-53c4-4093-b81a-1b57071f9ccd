'use client';

import React, { useEffect, useRef } from 'react';
import { useFocusTrap } from '@/hooks/useKeyboardNavigation';

interface FocusTrapProps {
  children: React.ReactNode;
  isActive?: boolean;
  className?: string;
}

/**
 * Focus trap component that contains focus within its children
 * Useful for modals, dropdowns, and other overlay components
 */
export function FocusTrap({ 
  children, 
  isActive = true, 
  className 
}: FocusTrapProps) {
  const containerRef = useFocusTrap(isActive);

  return (
    <div ref={containerRef} className={className}>
      {children}
    </div>
  );
}