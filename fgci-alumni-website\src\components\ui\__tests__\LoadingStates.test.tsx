import { render, screen } from '@/lib/test-utils';
import { 
  LoadingSpinner, 
  LoadingSkeleton, 
  LoadingCard, 
  LoadingGrid,
  LoadingButton 
} from '../LoadingStates';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Loading Components', () => {
  describe('LoadingSpinner', () => {
    it('renders with default size', () => {
      render(<LoadingSpinner />);
      
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toBeInTheDocument();
      expect(spinner).toHaveClass('spinner-md');
    });

    it('renders with different sizes', () => {
      const { rerender } = render(<LoadingSpinner size="sm" />);
      expect(screen.getByTestId('loading-spinner')).toHaveClass('spinner-sm');

      rerender(<LoadingSpinner size="lg" />);
      expect(screen.getByTestId('loading-spinner')).toHaveClass('spinner-lg');

      rerender(<LoadingSpinner size="xl" />);
      expect(screen.getByTestId('loading-spinner')).toHaveClass('spinner-xl');
    });

    it('renders with custom label', () => {
      render(<LoadingSpinner label="Loading content..." />);
      
      expect(screen.getByText('Loading content...')).toBeInTheDocument();
    });

    it('has proper accessibility attributes', () => {
      render(<LoadingSpinner />);
      
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toHaveAttribute('role', 'status');
      expect(spinner).toHaveAttribute('aria-label', 'Loading');
    });

    it('has no accessibility violations', async () => {
      const { container } = render(<LoadingSpinner />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('LoadingSkeleton', () => {
    it('renders with default props', () => {
      render(<LoadingSkeleton />);
      
      const skeleton = screen.getByTestId('loading-skeleton');
      expect(skeleton).toBeInTheDocument();
      expect(skeleton).toHaveClass('skeleton');
    });

    it('renders with different variants', () => {
      const { rerender } = render(<LoadingSkeleton variant="text" />);
      expect(screen.getByTestId('loading-skeleton')).toHaveClass('skeleton-text');

      rerender(<LoadingSkeleton variant="circular" />);
      expect(screen.getByTestId('loading-skeleton')).toHaveClass('skeleton-circular');

      rerender(<LoadingSkeleton variant="rectangular" />);
      expect(screen.getByTestId('loading-skeleton')).toHaveClass('skeleton-rectangular');
    });

    it('renders with custom width and height', () => {
      render(<LoadingSkeleton width="200px" height="100px" />);
      
      const skeleton = screen.getByTestId('loading-skeleton');
      expect(skeleton).toHaveStyle({
        width: '200px',
        height: '100px',
      });
    });

    it('renders multiple lines for text variant', () => {
      render(<LoadingSkeleton variant="text" lines={3} />);
      
      const skeletons = screen.getAllByTestId('loading-skeleton');
      expect(skeletons).toHaveLength(3);
    });

    it('has proper accessibility attributes', () => {
      render(<LoadingSkeleton />);
      
      const skeleton = screen.getByTestId('loading-skeleton');
      expect(skeleton).toHaveAttribute('aria-label', 'Loading content');
    });

    it('has no accessibility violations', async () => {
      const { container } = render(<LoadingSkeleton />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('LoadingCard', () => {
    it('renders card skeleton structure', () => {
      render(<LoadingCard />);
      
      expect(screen.getByTestId('loading-card')).toBeInTheDocument();
      expect(screen.getByTestId('card-image-skeleton')).toBeInTheDocument();
      expect(screen.getByTestId('card-title-skeleton')).toBeInTheDocument();
      expect(screen.getByTestId('card-content-skeleton')).toBeInTheDocument();
    });

    it('renders without image when showImage is false', () => {
      render(<LoadingCard showImage={false} />);
      
      expect(screen.getByTestId('loading-card')).toBeInTheDocument();
      expect(screen.queryByTestId('card-image-skeleton')).not.toBeInTheDocument();
    });

    it('renders with custom number of content lines', () => {
      render(<LoadingCard contentLines={5} />);
      
      const contentSkeletons = screen.getAllByTestId('card-content-line');
      expect(contentSkeletons).toHaveLength(5);
    });

    it('has no accessibility violations', async () => {
      const { container } = render(<LoadingCard />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('LoadingGrid', () => {
    it('renders grid of loading cards', () => {
      render(<LoadingGrid count={6} />);
      
      const loadingCards = screen.getAllByTestId('loading-card');
      expect(loadingCards).toHaveLength(6);
    });

    it('renders with custom grid columns', () => {
      render(<LoadingGrid count={4} columns={2} />);
      
      const grid = screen.getByTestId('loading-grid');
      expect(grid).toHaveClass('grid-cols-2');
    });

    it('renders with responsive columns', () => {
      render(<LoadingGrid count={6} columns={{ sm: 1, md: 2, lg: 3 }} />);
      
      const grid = screen.getByTestId('loading-grid');
      expect(grid).toHaveClass('grid-cols-1', 'md:grid-cols-2', 'lg:grid-cols-3');
    });

    it('has no accessibility violations', async () => {
      const { container } = render(<LoadingGrid count={3} />);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('LoadingButton', () => {
    it('renders loading button with spinner', () => {
      render(<LoadingButton loading>Save</LoadingButton>);
      
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('renders normal button when not loading', () => {
      render(<LoadingButton loading={false}>Save</LoadingButton>);
      
      const button = screen.getByRole('button');
      expect(button).not.toBeDisabled();
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      expect(screen.getByText('Save')).toBeInTheDocument();
    });

    it('shows loading text when provided', () => {
      render(
        <LoadingButton loading loadingText="Saving...">
          Save
        </LoadingButton>
      );
      
      expect(screen.getByText('Saving...')).toBeInTheDocument();
      expect(screen.queryByText('Save')).not.toBeInTheDocument();
    });

    it('handles click events when not loading', () => {
      const handleClick = jest.fn();
      render(
        <LoadingButton loading={false} onClick={handleClick}>
          Save
        </LoadingButton>
      );
      
      const button = screen.getByRole('button');
      button.click();
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('does not handle click events when loading', () => {
      const handleClick = jest.fn();
      render(
        <LoadingButton loading onClick={handleClick}>
          Save
        </LoadingButton>
      );
      
      const button = screen.getByRole('button');
      button.click();
      
      expect(handleClick).not.toHaveBeenCalled();
    });

    it('has proper accessibility attributes when loading', () => {
      render(<LoadingButton loading>Save</LoadingButton>);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('aria-busy', 'true');
      expect(button).toHaveAttribute('aria-disabled', 'true');
    });

    it('has no accessibility violations', async () => {
      const { container } = render(<LoadingButton loading>Save</LoadingButton>);
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Animation and Performance', () => {
    it('respects reduced motion preferences', () => {
      // Mock prefers-reduced-motion
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      render(<LoadingSpinner />);
      
      const spinner = screen.getByTestId('loading-spinner');
      expect(spinner).toHaveClass('motion-reduce:animate-none');
    });

    it('renders efficiently with many skeleton items', () => {
      const startTime = performance.now();
      
      render(<LoadingGrid count={50} />);
      
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      // Should render quickly (less than 100ms for 50 items)
      expect(renderTime).toBeLessThan(100);
    });
  });
});