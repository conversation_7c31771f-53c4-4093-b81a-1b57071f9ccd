import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import { Modal } from '../Modal';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('Modal Component', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    title: 'Test Modal',
    children: <div>Modal content</div>,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(<Modal {...defaultProps} />);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Test Modal')).toBeInTheDocument();
    expect(screen.getByText('Modal content')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<Modal {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  });

  it('renders with different sizes', () => {
    const { rerender } = render(<Modal {...defaultProps} size="sm" />);
    expect(screen.getByRole('dialog')).toHaveClass('modal-sm');

    rerender(<Modal {...defaultProps} size="lg" />);
    expect(screen.getByRole('dialog')).toHaveClass('modal-lg');

    rerender(<Modal {...defaultProps} size="xl" />);
    expect(screen.getByRole('dialog')).toHaveClass('modal-xl');

    rerender(<Modal {...defaultProps} size="fullscreen" />);
    expect(screen.getByRole('dialog')).toHaveClass('modal-fullscreen');
  });

  it('calls onClose when close button is clicked', () => {
    const onClose = jest.fn();
    render(<Modal {...defaultProps} onClose={onClose} />);
    
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when escape key is pressed', () => {
    const onClose = jest.fn();
    render(<Modal {...defaultProps} onClose={onClose} />);
    
    fireEvent.keyDown(document, { key: 'Escape' });
    
    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when overlay is clicked by default', () => {
    const onClose = jest.fn();
    render(<Modal {...defaultProps} onClose={onClose} />);
    
    const overlay = screen.getByTestId('modal-overlay');
    fireEvent.click(overlay);
    
    expect(onClose).toHaveBeenCalledTimes(1);
  });

  it('does not call onClose when overlay is clicked if closeOnOverlayClick is false', () => {
    const onClose = jest.fn();
    render(
      <Modal {...defaultProps} onClose={onClose} closeOnOverlayClick={false} />
    );
    
    const overlay = screen.getByTestId('modal-overlay');
    fireEvent.click(overlay);
    
    expect(onClose).not.toHaveBeenCalled();
  });

  it('does not call onClose when modal content is clicked', () => {
    const onClose = jest.fn();
    render(<Modal {...defaultProps} onClose={onClose} />);
    
    const content = screen.getByText('Modal content');
    fireEvent.click(content);
    
    expect(onClose).not.toHaveBeenCalled();
  });

  it('traps focus within modal', async () => {
    render(
      <Modal {...defaultProps}>
        <button>First button</button>
        <button>Second button</button>
      </Modal>
    );
    
    const firstButton = screen.getByText('First button');
    const secondButton = screen.getByText('Second button');
    const closeButton = screen.getByRole('button', { name: /close/i });
    
    // Focus should start on the close button or first focusable element
    await waitFor(() => {
      expect(document.activeElement).toBeInstanceOf(HTMLElement);
    });
    
    // Tab should cycle through focusable elements
    fireEvent.keyDown(document.activeElement!, { key: 'Tab' });
    fireEvent.keyDown(document.activeElement!, { key: 'Tab' });
    fireEvent.keyDown(document.activeElement!, { key: 'Tab' });
    
    // Should cycle back to first element
    expect([firstButton, secondButton, closeButton]).toContain(document.activeElement);
  });

  it('restores focus when closed', async () => {
    const triggerButton = document.createElement('button');
    triggerButton.textContent = 'Open Modal';
    document.body.appendChild(triggerButton);
    triggerButton.focus();
    
    const { rerender } = render(<Modal {...defaultProps} />);
    
    // Close the modal
    rerender(<Modal {...defaultProps} isOpen={false} />);
    
    await waitFor(() => {
      expect(document.activeElement).toBe(triggerButton);
    });
    
    document.body.removeChild(triggerButton);
  });

  it('renders without title', () => {
    render(
      <Modal isOpen onClose={jest.fn()}>
        <div>Content without title</div>
      </Modal>
    );
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Content without title')).toBeInTheDocument();
    expect(screen.queryByRole('heading')).not.toBeInTheDocument();
  });

  it('renders with custom className', () => {
    render(<Modal {...defaultProps} className="custom-modal" />);
    
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveClass('custom-modal');
  });

  it('prevents body scroll when open', () => {
    render(<Modal {...defaultProps} />);
    
    expect(document.body.style.overflow).toBe('hidden');
  });

  it('restores body scroll when closed', () => {
    const { rerender } = render(<Modal {...defaultProps} />);
    
    rerender(<Modal {...defaultProps} isOpen={false} />);
    
    expect(document.body.style.overflow).toBe('');
  });

  it('has proper ARIA attributes', () => {
    render(<Modal {...defaultProps} />);
    
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveAttribute('aria-modal', 'true');
    expect(modal).toHaveAttribute('aria-labelledby');
    
    const title = screen.getByText('Test Modal');
    expect(title).toHaveAttribute('id');
    expect(modal.getAttribute('aria-labelledby')).toBe(title.getAttribute('id'));
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<Modal {...defaultProps} />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('handles animation states', async () => {
    const { rerender } = render(<Modal {...defaultProps} />);
    
    const modal = screen.getByRole('dialog');
    expect(modal).toHaveClass('modal-enter');
    
    // Simulate animation completion
    await waitFor(() => {
      expect(modal).toHaveClass('modal-enter-active');
    });
    
    // Close modal
    rerender(<Modal {...defaultProps} isOpen={false} />);
    
    // Should have exit classes during animation
    expect(modal).toHaveClass('modal-exit');
  });

  it('supports custom header content', () => {
    render(
      <Modal
        {...defaultProps}
        title={
          <div>
            <span>Custom</span> <strong>Header</strong>
          </div>
        }
      />
    );
    
    expect(screen.getByText('Custom')).toBeInTheDocument();
    expect(screen.getByText('Header')).toBeInTheDocument();
  });

  it('supports footer content', () => {
    render(
      <Modal
        {...defaultProps}
        footer={
          <div>
            <button>Cancel</button>
            <button>Save</button>
          </div>
        }
      />
    );
    
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Save')).toBeInTheDocument();
  });
});