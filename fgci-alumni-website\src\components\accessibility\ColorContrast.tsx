'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ColorContrastProps {
  children: React.ReactNode;
  level?: 'aa' | 'aaa';
  className?: string;
}

/**
 * Component that ensures proper color contrast ratios
 * Automatically adjusts colors based on contrast preferences
 */
export function ColorContrast({ 
  children, 
  level = 'aa', 
  className 
}: ColorContrastProps) {
  return (
    <div className={cn(`contrast-${level}`, className)}>
      {children}
    </div>
  );
}

/**
 * Status indicator component with color and non-color indicators
 */
interface StatusIndicatorProps {
  status: 'success' | 'error' | 'warning' | 'info';
  children: React.ReactNode;
  showPattern?: boolean;
  className?: string;
}

export function StatusIndicator({ 
  status, 
  children, 
  showPattern = false,
  className 
}: StatusIndicatorProps) {
  const statusClasses = {
    success: 'text-green-700 bg-green-50 border-green-200',
    error: 'text-red-700 bg-red-50 border-red-200',
    warning: 'text-yellow-700 bg-yellow-50 border-yellow-200',
    info: 'text-blue-700 bg-blue-50 border-blue-200'
  };

  const patternClasses = {
    success: 'pattern-success',
    error: 'pattern-error',
    warning: 'pattern-warning',
    info: ''
  };

  return (
    <div
      className={cn(
        'px-3 py-2 rounded-md border',
        `status-${status}`,
        statusClasses[status],
        showPattern && patternClasses[status],
        className
      )}
      role="status"
      aria-live="polite"
    >
      {children}
    </div>
  );
}

/**
 * High contrast text component
 */
interface HighContrastTextProps {
  children: React.ReactNode;
  as?: React.ElementType;
  className?: string;
}

export function HighContrastText({ 
  children, 
  as: Component = 'span',
  className 
}: HighContrastTextProps) {
  return React.createElement(
    Component,
    {
      className: cn(
        'text-gray-900 font-medium high-contrast-text',
        className
      )
    },
    children
  );
}

/**
 * Accessible link component with proper contrast
 */
interface AccessibleLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
  className?: string;
}

export function AccessibleLink({ 
  href, 
  children, 
  external = false,
  className 
}: AccessibleLinkProps) {
  const linkProps = external 
    ? { 
        target: '_blank', 
        rel: 'noopener noreferrer',
        'aria-describedby': 'external-link-desc'
      }
    : {};

  return (
    <>
      <a
        href={href}
        className={cn(
          'text-maroon-700 underline hover:text-maroon-800 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2',
          'font-medium transition-colors duration-200',
          '@media (prefers-contrast: high) { text-decoration-thickness: 2px !important; }',
          className
        )}
        {...linkProps}
      >
        {children}
        {external && (
          <span className="sr-only"> (opens in new tab)</span>
        )}
      </a>
      
      {external && (
        <div id="external-link-desc" className="sr-only">
          External links open in a new tab
        </div>
      )}
    </>
  );
}

/**
 * Color-blind friendly button variants
 */
interface ColorBlindFriendlyButtonProps {
  variant: 'success' | 'error' | 'warning' | 'info';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}

export function ColorBlindFriendlyButton({
  variant,
  children,
  onClick,
  disabled = false,
  className
}: ColorBlindFriendlyButtonProps) {
  const variantConfig = {
    success: {
      classes: 'bg-green-600 text-white border-green-700 hover:bg-green-700',
      icon: '✓',
      label: 'Success'
    },
    error: {
      classes: 'bg-red-600 text-white border-red-700 hover:bg-red-700',
      icon: '✗',
      label: 'Error'
    },
    warning: {
      classes: 'bg-yellow-600 text-white border-yellow-700 hover:bg-yellow-700',
      icon: '⚠',
      label: 'Warning'
    },
    info: {
      classes: 'bg-blue-600 text-white border-blue-700 hover:bg-blue-700',
      icon: 'ℹ',
      label: 'Info'
    }
  };

  const config = variantConfig[variant];

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'px-4 py-2 rounded-md border-2 font-medium transition-colors duration-200',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        config.classes,
        className
      )}
      aria-label={`${config.label}: ${typeof children === 'string' ? children : ''}`}
    >
      <span className="flex items-center gap-2">
        <span aria-hidden="true">{config.icon}</span>
        {children}
      </span>
    </button>
  );
}

/**
 * Utility to check if user prefers high contrast
 */
export function useHighContrast() {
  const [prefersHighContrast, setPrefersHighContrast] = React.useState(false);

  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setPrefersHighContrast(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersHighContrast(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersHighContrast;
}