import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Card, CardHeader, CardTitle, CardContent } from './Card';
import { Button } from './Button';
import { HeartIcon, ShareIcon } from '@heroicons/react/24/outline';

const meta: Meta<typeof Card> = {
  title: 'UI/Card',
  component: Card,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A flexible card component with header, title, and content sections. Supports different variants and interactive states.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'elevated', 'outlined'],
      description: 'Visual style variant of the card',
    },
    hover: {
      control: 'boolean',
      description: 'Whether the card has hover effects',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic card
export const Default: Story = {
  render: (args) => (
    <Card {...args} className="w-80">
      <CardHeader>
        <CardTitle>Card Title</CardTitle>
      </CardHeader>
      <CardContent>
        <p>This is the card content. It can contain any React elements.</p>
      </CardContent>
    </Card>
  ),
};

// Variants
export const Elevated: Story = {
  render: () => (
    <Card variant="elevated" className="w-80">
      <CardHeader>
        <CardTitle>Elevated Card</CardTitle>
      </CardHeader>
      <CardContent>
        <p>This card has an elevated appearance with shadow.</p>
      </CardContent>
    </Card>
  ),
};

export const Outlined: Story = {
  render: () => (
    <Card variant="outlined" className="w-80">
      <CardHeader>
        <CardTitle>Outlined Card</CardTitle>
      </CardHeader>
      <CardContent>
        <p>This card has a border outline.</p>
      </CardContent>
    </Card>
  ),
};

// Interactive card
export const WithHover: Story = {
  render: () => (
    <Card hover className="w-80">
      <CardHeader>
        <CardTitle>Hoverable Card</CardTitle>
      </CardHeader>
      <CardContent>
        <p>This card has hover effects. Try hovering over it!</p>
      </CardContent>
    </Card>
  ),
};

// Clickable card
export const Clickable: Story = {
  render: () => (
    <Card 
      hover 
      onClick={() => alert('Card clicked!')} 
      className="w-80 cursor-pointer"
    >
      <CardHeader>
        <CardTitle>Clickable Card</CardTitle>
      </CardHeader>
      <CardContent>
        <p>This entire card is clickable. Click anywhere on it!</p>
      </CardContent>
    </Card>
  ),
};

// Complex card with actions
export const WithActions: Story = {
  render: () => (
    <Card variant="elevated" className="w-80">
      <CardHeader>
        <CardTitle>Alumni Profile</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
            <div>
              <h3 className="font-semibold">John Doe</h3>
              <p className="text-sm text-gray-600">Class of 1995</p>
            </div>
          </div>
          <p className="text-sm text-gray-700">
            Software Engineer at Tech Company. Passionate about education and community development.
          </p>
          <div className="flex space-x-2">
            <Button size="sm" variant="outline" icon={<HeartIcon className="w-4 h-4" />}>
              Like
            </Button>
            <Button size="sm" variant="outline" icon={<ShareIcon className="w-4 h-4" />}>
              Share
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ),
};

// Card without header
export const ContentOnly: Story = {
  render: () => (
    <Card className="w-80">
      <CardContent>
        <h3 className="font-semibold mb-2">Simple Card</h3>
        <p>This card only has content, no separate header.</p>
      </CardContent>
    </Card>
  ),
};

// Card with custom header
export const CustomHeader: Story = {
  render: () => (
    <Card variant="elevated" className="w-80">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Event Details</CardTitle>
          <span className="text-sm text-green-600 font-medium">Upcoming</span>
        </div>
        <p className="text-sm text-gray-600 mt-1">December 15, 2024</p>
      </CardHeader>
      <CardContent>
        <h3 className="font-semibold mb-2">Annual Alumni Reunion</h3>
        <p className="text-sm text-gray-700 mb-4">
          Join us for our annual reunion celebration with networking, dinner, and entertainment.
        </p>
        <Button size="sm">Register Now</Button>
      </CardContent>
    </Card>
  ),
};

// Multiple cards layout
export const CardGrid: Story = {
  render: () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl">
      <Card variant="elevated" hover>
        <CardHeader>
          <CardTitle>Chapters</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Connect with alumni in your region through local chapters.</p>
          <Button size="sm" className="mt-3">Explore</Button>
        </CardContent>
      </Card>

      <Card variant="elevated" hover>
        <CardHeader>
          <CardTitle>Events</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Discover upcoming reunions, networking events, and celebrations.</p>
          <Button size="sm" className="mt-3">View Events</Button>
        </CardContent>
      </Card>

      <Card variant="elevated" hover>
        <CardHeader>
          <CardTitle>Gallery</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Browse photos from past events and school memories.</p>
          <Button size="sm" className="mt-3">View Photos</Button>
        </CardContent>
      </Card>
    </div>
  ),
  parameters: {
    layout: 'padded',
  },
};

// Accessibility example
export const AccessibilityExample: Story = {
  render: () => (
    <div className="space-y-6 max-w-2xl">
      <div>
        <h3 className="text-lg font-semibold mb-4">Semantic Structure</h3>
        <Card variant="outlined">
          <CardHeader>
            <CardTitle as="h2">Proper Heading Hierarchy</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This card uses semantic HTML with proper heading levels for screen readers.</p>
            <h3 className="font-semibold mt-3 mb-1">Subsection</h3>
            <p className="text-sm text-gray-600">Content follows logical heading structure.</p>
          </CardContent>
        </Card>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Keyboard Navigation</h3>
        <Card hover onClick={() => alert('Card activated!')} className="cursor-pointer">
          <CardHeader>
            <CardTitle>Interactive Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This card can be activated with keyboard (Enter or Space when focused).</p>
            <p className="text-sm text-gray-600 mt-2">
              Try tabbing to this card and pressing Enter.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Examples of accessible card implementations with proper semantic structure and keyboard support.',
      },
    },
  },
};
