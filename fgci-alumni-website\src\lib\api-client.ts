import { 
  ApiResponse, 
  PaginatedResponse, 
  SearchResponse,
  ApiError, 
  NetworkError, 
  ServerError, 
  ClientError,
  AppError,
  RequestConfig,
  ApiEndpoints
} from './types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
const DEFAULT_TIMEOUT = 10000; // 10 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// API Endpoints configuration
export const apiEndpoints: ApiEndpoints = {
  chapters: {
    list: () => '/chapters',
    detail: (slug: string) => `/chapters/${slug}`,
    activities: (slug: string) => `/chapters/${slug}/activities`,
    gallery: (slug: string) => `/chapters/${slug}/gallery`,
  },
  sets: {
    list: () => '/sets',
    detail: (slug: string) => `/sets/${slug}`,
    activities: (slug: string) => `/sets/${slug}/activities`,
    gallery: (slug: string) => `/sets/${slug}/gallery`,
  },
  events: {
    list: () => '/events',
    detail: (slug: string) => `/events/${slug}`,
  },
  gallery: {
    list: () => '/gallery',
    albums: () => '/gallery/albums',
    album: (id: string) => `/gallery/albums/${id}`,
  },
  excobot: {
    search: () => '/excobot/search',
  },
  memorial: {
    list: () => '/memorial',
    condolences: (id: string) => `/memorial/${id}/condolences`,
  },
  contact: {
    submit: () => '/contact',
  },
};

// Utility functions
function isNetworkError(error: unknown): boolean {
  return error instanceof TypeError && error.message.includes('fetch');
}

function createNetworkError(message: string): NetworkError {
  return {
    type: 'network',
    message,
    isOffline: !navigator.onLine,
    retryable: true,
  };
}

function createServerError(status: number, message: string): ServerError {
  return {
    type: 'server',
    status,
    message,
    retryable: status >= 500 && status < 600,
    timestamp: new Date().toISOString(),
  };
}

function createClientError(status: number, message: string): ClientError {
  return {
    type: 'client',
    status,
    message,
    retryable: false,
    timestamp: new Date().toISOString(),
  };
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Build URL with query parameters
function buildUrl(endpoint: string, params?: Record<string, string | number | boolean>): string {
  const url = new URL(`${API_BASE_URL}${endpoint}`);
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });
  }
  
  return url.toString();
}

// Main API client class
export class ApiClient {
  private defaultHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  constructor(private baseUrl: string = API_BASE_URL) {}

  // Set authentication token
  setAuthToken(token: string): void {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  // Remove authentication token
  clearAuthToken(): void {
    delete this.defaultHeaders['Authorization'];
  }

  // Core request method with retry logic
  private async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      params,
      timeout = DEFAULT_TIMEOUT,
      retries = MAX_RETRIES,
    } = config;

    const url = buildUrl(endpoint, params);
    const requestHeaders = { ...this.defaultHeaders, ...headers };

    let lastError: AppError;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
          method,
          headers: requestHeaders,
          body: body ? JSON.stringify(body) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        // Handle HTTP errors
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          
          if (response.status >= 500) {
            lastError = createServerError(
              response.status,
              errorData.message || `Server error: ${response.status}`
            );
          } else {
            lastError = createClientError(
              response.status,
              errorData.message || `Client error: ${response.status}`
            );
          }

          // Don't retry client errors
          if (response.status < 500) {
            throw lastError;
          }
        } else {
          // Success response
          const data = await response.json();
          return {
            ...data,
            success: true,
          };
        }
      } catch (error) {
        if (error instanceof DOMException && error.name === 'AbortError') {
          lastError = createNetworkError('Request timeout');
        } else if (isNetworkError(error)) {
          lastError = createNetworkError('Network error - please check your connection');
        } else if (error && typeof error === 'object' && 'type' in error) {
          lastError = error as AppError;
        } else {
          lastError = createNetworkError('An unexpected error occurred');
        }

        // Don't retry non-retryable errors
        if (!lastError.retryable) {
          throw lastError;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < retries) {
          await sleep(RETRY_DELAY * Math.pow(2, attempt));
        }
      }
    }

    throw lastError!;
  }

  // GET request
  async get<T>(endpoint: string, params?: Record<string, string | number | boolean>): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET', params });
  }

  // POST request
  async post<T>(endpoint: string, body?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'POST', body });
  }

  // PUT request
  async put<T>(endpoint: string, body?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PUT', body });
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // PATCH request
  async patch<T>(endpoint: string, body?: unknown): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'PATCH', body });
  }

  // Paginated GET request
  async getPaginated<T>(
    endpoint: string, 
    params?: Record<string, string | number | boolean>
  ): Promise<PaginatedResponse<T>> {
    return this.request<T[]>(endpoint, { method: 'GET', params }) as Promise<PaginatedResponse<T>>;
  }

  // Search request
  async search<T>(
    endpoint: string,
    query: string,
    filters?: Record<string, unknown>
  ): Promise<SearchResponse<T>> {
    const params = {
      q: query,
      ...filters,
    };
    return this.request<T[]>(endpoint, { method: 'GET', params }) as Promise<SearchResponse<T>>;
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Error boundary helper
export function isApiError(error: unknown): error is AppError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'type' in error &&
    ['network', 'server', 'client'].includes((error as any).type)
  );
}

// Retry helper for React Query
export function shouldRetry(error: unknown): boolean {
  if (isApiError(error)) {
    return error.retryable;
  }
  return false;
}

// Get retry delay for React Query
export function getRetryDelay(attemptIndex: number): number {
  return Math.min(RETRY_DELAY * Math.pow(2, attemptIndex), 30000); // Max 30 seconds
}