/**
 * Advanced image optimization utilities for Next.js
 */

export interface ResponsiveImageConfig {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  aspectRatio?: string;
  priority?: boolean;
  quality?: number;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
}

export interface ImageSizeConfig {
  breakpoint: number;
  width: number;
  quality?: number;
}

/**
 * Generate responsive image sizes string for Next.js Image component
 */
export function generateImageSizes(configs: ImageSizeConfig[]): string {
  return configs
    .sort((a, b) => a.breakpoint - b.breakpoint)
    .map((config, index, array) => {
      const isLast = index === array.length - 1;
      const condition = isLast 
        ? `${config.width}px`
        : `(max-width: ${config.breakpoint}px) ${config.width}px`;
      return condition;
    })
    .join(', ');
}

/**
 * Common responsive image size configurations
 */
export const RESPONSIVE_SIZES = {
  // Gallery grid images
  gallery: generateImageSizes([
    { breakpoint: 640, width: 320 },   // Mobile: 1 column
    { breakpoint: 768, width: 240 },   // Tablet: 2 columns
    { breakpoint: 1024, width: 200 },  // Desktop: 3 columns
    { breakpoint: 1280, width: 160 },  // Large: 4 columns
    { breakpoint: 9999, width: 128 },  // XL: 5+ columns
  ]),
  
  // Hero images
  hero: generateImageSizes([
    { breakpoint: 640, width: 640 },
    { breakpoint: 768, width: 768 },
    { breakpoint: 1024, width: 1024 },
    { breakpoint: 1280, width: 1280 },
    { breakpoint: 1920, width: 1920 },
    { breakpoint: 9999, width: 2048 },
  ]),
  
  // Card images
  card: generateImageSizes([
    { breakpoint: 640, width: 300 },
    { breakpoint: 768, width: 350 },
    { breakpoint: 1024, width: 400 },
    { breakpoint: 9999, width: 450 },
  ]),
  
  // Avatar images
  avatar: generateImageSizes([
    { breakpoint: 640, width: 64 },
    { breakpoint: 768, width: 80 },
    { breakpoint: 1024, width: 96 },
    { breakpoint: 9999, width: 128 },
  ]),
  
  // Lightbox images
  lightbox: generateImageSizes([
    { breakpoint: 640, width: 640 },
    { breakpoint: 768, width: 768 },
    { breakpoint: 1024, width: 1024 },
    { breakpoint: 1280, width: 1280 },
    { breakpoint: 1920, width: 1920 },
    { breakpoint: 9999, width: 2560 },
  ]),
};

/**
 * Generate blur data URL for image placeholders
 */
export function generateBlurDataURL(
  width: number = 10,
  height: number = 10,
  color: string = '#f3f4f6'
): string {
  const canvas = typeof window !== 'undefined' ? document.createElement('canvas') : null;
  
  if (!canvas) {
    // Fallback for SSR
    return `data:image/svg+xml;base64,${Buffer.from(
      `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="${color}"/>
      </svg>`
    ).toString('base64')}`;
  }
  
  canvas.width = width;
  canvas.height = height;
  
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.fillStyle = color;
    ctx.fillRect(0, 0, width, height);
  }
  
  return canvas.toDataURL('image/jpeg', 0.1);
}

/**
 * Calculate optimal image quality based on image type and usage
 */
export function calculateOptimalQuality(
  usage: 'hero' | 'gallery' | 'card' | 'avatar' | 'thumbnail',
  isRetina: boolean = false
): number {
  const baseQuality = {
    hero: 85,
    gallery: 80,
    card: 75,
    avatar: 70,
    thumbnail: 65,
  };
  
  const quality = baseQuality[usage];
  
  // Reduce quality slightly for retina displays to save bandwidth
  return isRetina ? Math.max(quality - 10, 50) : quality;
}

/**
 * Preload critical images
 */
export function preloadImage(src: string, priority: 'high' | 'low' = 'high'): void {
  if (typeof window === 'undefined') return;
  
  const link = document.createElement('link');
  link.rel = 'preload';
  link.as = 'image';
  link.href = src;
  link.fetchPriority = priority;
  
  document.head.appendChild(link);
}

/**
 * Preload multiple images with different priorities
 */
export function preloadImages(images: Array<{ src: string; priority?: 'high' | 'low' }>): void {
  images.forEach(({ src, priority = 'low' }) => {
    preloadImage(src, priority);
  });
}

/**
 * Check if WebP is supported
 */
export function supportsWebP(): Promise<boolean> {
  return new Promise((resolve) => {
    if (typeof window === 'undefined') {
      resolve(false);
      return;
    }
    
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
}

/**
 * Check if AVIF is supported
 */
export function supportsAVIF(): Promise<boolean> {
  return new Promise((resolve) => {
    if (typeof window === 'undefined') {
      resolve(false);
      return;
    }
    
    const avif = new Image();
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2);
    };
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
}

/**
 * Get optimal image format based on browser support
 */
export async function getOptimalImageFormat(): Promise<'avif' | 'webp' | 'jpeg'> {
  if (await supportsAVIF()) return 'avif';
  if (await supportsWebP()) return 'webp';
  return 'jpeg';
}

/**
 * Image loading performance observer
 */
export class ImagePerformanceObserver {
  private observer: PerformanceObserver | null = null;
  private metrics: Map<string, number> = new Map();
  
  constructor() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.initiatorType === 'img') {
            this.metrics.set(entry.name, entry.duration);
          }
        });
      });
      
      try {
        this.observer.observe({ entryTypes: ['resource'] });
      } catch (error) {
        console.warn('Failed to observe image performance:', error);
      }
    }
  }
  
  getImageLoadTime(src: string): number | undefined {
    return this.metrics.get(src);
  }
  
  getAllMetrics(): Map<string, number> {
    return new Map(this.metrics);
  }
  
  disconnect(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

/**
 * Intersection Observer for lazy loading with enhanced options
 */
export function createLazyLoadObserver(
  callback: (entry: IntersectionObserverEntry) => void,
  options: {
    rootMargin?: string;
    threshold?: number;
    root?: Element | null;
  } = {}
): IntersectionObserver | null {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return null;
  }
  
  const defaultOptions = {
    rootMargin: '200px 0px',
    threshold: 0.1,
    root: null,
    ...options,
  };
  
  return new IntersectionObserver((entries) => {
    entries.forEach(callback);
  }, defaultOptions);
}

/**
 * Calculate image dimensions while maintaining aspect ratio
 */
export function calculateImageDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight?: number
): { width: number; height: number } {
  const aspectRatio = originalWidth / originalHeight;
  
  let width = Math.min(originalWidth, maxWidth);
  let height = width / aspectRatio;
  
  if (maxHeight && height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }
  
  return {
    width: Math.round(width),
    height: Math.round(height),
  };
}

/**
 * Generate srcset for responsive images
 */
export function generateSrcSet(
  baseSrc: string,
  widths: number[],
  quality: number = 75
): string {
  return widths
    .map((width) => {
      const params = new URLSearchParams({
        w: width.toString(),
        q: quality.toString(),
      });
      return `${baseSrc}?${params.toString()} ${width}w`;
    })
    .join(', ');
}