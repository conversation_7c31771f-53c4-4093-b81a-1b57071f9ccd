# Deployment Guide

This guide covers the deployment process for the FGC Ikom Alumni Association website.

## Overview

The application is deployed using:
- **Hosting**: Vercel for the main application
- **CI/CD**: GitHub Actions for automated testing and deployment
- **Monitoring**: Lighthouse CI for performance monitoring
- **Documentation**: Chromatic for Storybook hosting

## Prerequisites

Before deploying, ensure you have:

1. **GitHub Repository**: Code pushed to GitHub
2. **Vercel Account**: Connected to your GitHub repository
3. **Environment Variables**: Configured in Vercel dashboard
4. **Domain**: Custom domain configured (optional)

## Environment Variables

### Required Environment Variables

```bash
# API Configuration
NEXT_PUBLIC_API_URL=https://api.fgcikomalumni.org
NEXT_PUBLIC_USE_MOCKS=false

# Database
DATABASE_URL=postgresql://username:password@host:port/database

# Authentication
NEXTAUTH_SECRET=your-super-secret-key-here
NEXTAUTH_URL=https://fgcikomalumni.org

# External Services
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=1234567
```

### GitHub Secrets

Configure these secrets in your GitHub repository:

```bash
# Vercel Deployment
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-org-id
VERCEL_PROJECT_ID=your-project-id

# Lighthouse CI
LHCI_GITHUB_APP_TOKEN=your-lighthouse-token

# Security Scanning
SNYK_TOKEN=your-snyk-token

# Storybook
CHROMATIC_PROJECT_TOKEN=your-chromatic-token

# Notifications
SLACK_WEBHOOK=your-slack-webhook-url

# Production URLs
PRODUCTION_URL=https://fgcikomalumni.org
PRODUCTION_API_URL=https://api.fgcikomalumni.org
```

## Deployment Process

### 1. Automatic Deployment

The application automatically deploys when:
- Code is pushed to the `main` branch
- Pull requests are created (preview deployments)

### 2. Manual Deployment

To trigger a manual deployment:

```bash
# Using GitHub CLI
gh workflow run deploy.yml

# Or use the GitHub Actions UI
```

### 3. Vercel CLI Deployment

For direct Vercel deployment:

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to preview
vercel

# Deploy to production
vercel --prod
```

## CI/CD Pipeline

### Continuous Integration (CI)

The CI pipeline runs on every push and pull request:

1. **Code Quality**
   - ESLint linting
   - Prettier formatting check
   - TypeScript type checking

2. **Testing**
   - Unit tests with coverage
   - Integration tests
   - E2E tests with Playwright

3. **Security**
   - Dependency vulnerability scanning
   - Security audit with Snyk

4. **Performance**
   - Lighthouse audits
   - Bundle size analysis

5. **Build**
   - Application build verification
   - Storybook build

### Continuous Deployment (CD)

The CD pipeline runs on pushes to `main`:

1. **Pre-deployment**
   - Run full test suite
   - Build application

2. **Deployment**
   - Deploy to Vercel production
   - Deploy Storybook to Chromatic

3. **Post-deployment**
   - Smoke tests on production
   - Performance audits
   - Slack notifications

## Performance Monitoring

### Lighthouse CI

Lighthouse audits run automatically and enforce:

- **Performance**: ≥85 score
- **Accessibility**: ≥95 score
- **Best Practices**: ≥90 score
- **SEO**: ≥95 score

### Core Web Vitals Targets

- **LCP (Largest Contentful Paint)**: <2.5s
- **FID (First Input Delay)**: <100ms
- **CLS (Cumulative Layout Shift)**: <0.1

## Domain Configuration

### Custom Domain Setup

1. **Add Domain in Vercel**
   ```bash
   vercel domains add fgcikomalumni.org
   ```

2. **Configure DNS Records**
   ```
   Type: CNAME
   Name: www
   Value: cname.vercel-dns.com
   
   Type: A
   Name: @
   Value: ***********
   ```

3. **SSL Certificate**
   - Automatically provisioned by Vercel
   - Includes www and apex domain

### Subdomain Configuration

For additional subdomains:

```bash
# API subdomain
vercel domains add api.fgcikomalumni.org

# Storybook subdomain  
vercel domains add storybook.fgcikomalumni.org
```

## Monitoring and Alerts

### Health Checks

The application includes health check endpoints:

- `/api/health` - Basic health check
- `/api/health/detailed` - Detailed system status

### Error Monitoring

Configure error monitoring with:

- **Sentry**: For error tracking and performance monitoring
- **LogRocket**: For session replay and debugging
- **Vercel Analytics**: For performance insights

### Uptime Monitoring

Set up uptime monitoring with:

- **Pingdom**: External uptime monitoring
- **StatusPage**: Public status page
- **PagerDuty**: Alert management

## Rollback Procedures

### Vercel Rollback

```bash
# List deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url]
```

### GitHub Rollback

```bash
# Revert the last commit
git revert HEAD

# Push the revert
git push origin main
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check environment variables
   - Verify dependencies
   - Review build logs

2. **Performance Issues**
   - Check Lighthouse reports
   - Review bundle analysis
   - Monitor Core Web Vitals

3. **Deployment Failures**
   - Verify Vercel configuration
   - Check GitHub Actions logs
   - Validate environment variables

### Debug Commands

```bash
# Local build test
npm run build

# Local production test
npm run start

# Test with production environment
NEXT_PUBLIC_USE_MOCKS=false npm run build

# Lighthouse audit
npm run lighthouse

# Bundle analysis
npm run bundle-analyzer
```

## Security Considerations

### Headers

Security headers are configured in `vercel.json`:
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- Referrer Policy

### Environment Variables

- Never commit secrets to version control
- Use Vercel's environment variable encryption
- Rotate secrets regularly
- Use different secrets for different environments

### Dependencies

- Regular security audits with `npm audit`
- Automated dependency updates with Dependabot
- Snyk integration for vulnerability scanning

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review performance metrics
   - Check error logs
   - Update dependencies

2. **Monthly**
   - Security audit
   - Performance optimization review
   - Backup verification

3. **Quarterly**
   - Full security review
   - Performance benchmark update
   - Disaster recovery test

### Backup Strategy

- **Code**: Version controlled in GitHub
- **Database**: Automated daily backups
- **Assets**: CDN with redundancy
- **Configuration**: Infrastructure as code

## Support

For deployment issues:

1. Check the [troubleshooting section](#troubleshooting)
2. Review GitHub Actions logs
3. Check Vercel deployment logs
4. Contact the development team

---

**Last Updated**: December 2024
**Version**: 1.0.0
