import { describe, it, expect, beforeAll, afterEach, afterAll } from 'vitest';
import { server } from '../server';
import { mockChapters, mockSets, mockEvents, mockGalleryImages, mockAlumniDirectory } from '../index';

// Setup MSW server for tests
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'warn' });
});

afterEach(() => {
  server.resetHandlers();
});

afterAll(() => {
  server.close();
});

// Allow relative URLs in fetch during Node tests by providing a base URL
beforeAll(() => {
  const originalPatchedFetch = (global.fetch as any).bind(global);
  // @ts-expect-error override for tests
  global.fetch = (input: any, init?: any) => {
    if (typeof input === 'string' && input.startsWith('/')) {
      return originalPatchedFetch(new URL(input, 'http://localhost').toString(), init);
    }
    return originalPatchedFetch(input, init);
  };
});

afterAll(() => {
  // restore default patched fetch from msw by re-listening or resetting is unnecessary in this context
  // tests cleanup will close the server in afterAll above
});


describe('MSW API Handlers', () => {
  describe('Chapters API', () => {
    it('should return paginated chapters list', async () => {
      const response = await fetch('/api/chapters?page=1&limit=2');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(2);
      expect(data.meta.total).toBe(mockChapters.length);
      expect(data.meta.page).toBe(1);
      expect(data.meta.limit).toBe(2);
    });

    it('should filter chapters by search query', async () => {
      const response = await fetch('/api/chapters?search=Lagos');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.every((chapter: any) =>
        chapter.name.toLowerCase().includes('lagos') ||
        chapter.region?.toLowerCase().includes('lagos')
      )).toBe(true);
    });

    it('should filter chapters by type', async () => {
      const response = await fetch('/api/chapters?type=diaspora');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.every((chapter: any) => chapter.type === 'diaspora')).toBe(true);
    });

    it('should return specific chapter by slug', async () => {
      const response = await fetch('/api/chapters/lagos');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.slug).toBe('lagos');
      expect(data.data.name).toBe('Lagos Chapter');
    });

    it('should return 404 for non-existent chapter', async () => {
      const response = await fetch('/api/chapters/non-existent');
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Chapter not found');
    });

    it('should return chapter activities', async () => {
      const response = await fetch('/api/chapters/lagos/activities');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('Sets API', () => {
    it('should return paginated sets list sorted by year', async () => {
      const response = await fetch('/api/sets?page=1&limit=3');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(3);
      expect(data.meta.total).toBe(mockSets.length);

      // Check if sorted by year (descending)
      for (let i = 0; i < data.data.length - 1; i++) {
        expect(data.data[i].year).toBeGreaterThanOrEqual(data.data[i + 1].year);
      }
    });

    it('should filter sets by year', async () => {
      const response = await fetch('/api/sets?year=2015');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.every((set: any) => set.year === 2015)).toBe(true);
    });

    it('should return specific set by slug', async () => {
      const response = await fetch('/api/sets/class-2015');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.slug).toBe('class-2015');
      expect(data.data.year).toBe(2015);
    });
  });

  describe('Events API', () => {
    it('should return paginated events list', async () => {
      const response = await fetch('/api/events?page=1&limit=5');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(5);
      expect(data.meta.total).toBe(mockEvents.length);
    });

    it('should filter events by status', async () => {
      const response = await fetch('/api/events?status=upcoming');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.every((event: any) => event.status === 'upcoming')).toBe(true);
    });

    it('should search events by title and description', async () => {
      const response = await fetch('/api/events?search=homecoming');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.length).toBeGreaterThan(0);
      expect(data.data.some((event: any) =>
        event.title.toLowerCase().includes('homecoming') ||
        event.description.toLowerCase().includes('homecoming')
      )).toBe(true);
    });
  });

  describe('Gallery API', () => {
    it('should return paginated gallery images', async () => {
      const response = await fetch('/api/gallery?page=1&limit=10');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(10);
      expect(data.meta.total).toBe(mockGalleryImages.length);
    });

    it('should filter images by album', async () => {
      const response = await fetch('/api/gallery?album=homecoming-2023');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.every((image: any) => image.album === 'homecoming-2023')).toBe(true);
    });

    it('should return gallery albums', async () => {
      const response = await fetch('/api/gallery/albums');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
    });
  });

  describe('ExcoBot API', () => {
    it('should search alumni directory', async () => {
      const response = await fetch('/api/excobot/search?query=Adaora');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.meta.query).toBe('Adaora');
      expect(data.data.some((person: any) =>
        person.name.toLowerCase().includes('adaora')
      )).toBe(true);
    });

    it('should filter alumni by chapter', async () => {
      const response = await fetch('/api/excobot/search?chapter=Lagos');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.every((person: any) =>
        person.chapter?.toLowerCase().includes('lagos')
      )).toBe(true);
    });

    it('should filter alumni by graduation year', async () => {
      const response = await fetch('/api/excobot/search?graduationYear=2015');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.every((person: any) => person.graduationYear === 2015)).toBe(true);
    });

    it('should include search metadata', async () => {
      const response = await fetch('/api/excobot/search?query=engineer&chapter=Lagos');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.meta.query).toBe('engineer');
      expect(data.meta.filters.chapter).toBe('Lagos');
      expect(typeof data.meta.searchTime).toBe('number');
    });
  });

  describe('Contact API', () => {
    it('should accept valid contact form submission', async () => {
      const formData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test Message',
        message: 'This is a test message'
      };

      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.message).toContain('Thank you for your message');
      expect(data.data.id).toBeDefined();
    });

    it('should reject invalid contact form submission', async () => {
      const formData = {
        name: '',
        email: '<EMAIL>',
        message: ''
      };

      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Missing required fields');
      expect(data.fieldErrors.name).toContain('Name is required');
      expect(data.fieldErrors.message).toContain('Message is required');
    });
  });

  describe('Health Check API', () => {
    it('should return health status', async () => {
      const response = await fetch('/api/health');
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.status).toBe('healthy');
      expect(data.data.environment).toBe('development');
    });
  });
});