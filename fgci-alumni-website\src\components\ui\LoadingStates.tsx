'use client';

import React from 'react';
import { Card } from './Card';

// Generic Loading Spinner
export function LoadingSpinner({
  size = 'md',
  className = '',
  label,
}: {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  label?: string;
}) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-10 h-10',
  } as const;

  return (
    <div
      data-testid="loading-spinner"
      role="status"
      aria-label={label ?? 'Loading'}
      className={`animate-spin motion-reduce:animate-none ${sizeClasses[size]} spinner-${size} ${className}`}
    >
      <svg
        className="w-full h-full text-maroon-600"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      {label ? <span className="sr-only">{label}</span> : null}
    </div>
  );

}

// Generic Loading Skeleton
export function LoadingSkeleton({
  variant = 'rectangular',
  width,
  height,
  lines = 1,
  className = '',
}: {
  variant?: 'text' | 'circular' | 'rectangular';
  width?: string | number;
  height?: string | number;
  lines?: number;
  className?: string;
}) {
  const style: React.CSSProperties = {
    ...(width !== undefined ? { width } : {}),
    ...(height !== undefined ? { height } : {}),
  };

  if (variant === 'text' && lines > 1) {
    return (
      <>
        {Array.from({ length: lines }).map((_, i) => (
          <div
            key={i}
            data-testid="loading-skeleton"
            aria-label="Loading content"
            className={`skeleton skeleton-text animate-pulse ${className}`}
            style={style}
          />
        ))}
      </>
    );
  }

  const extraStyle: React.CSSProperties =
    variant === 'circular'
      ? {
          borderRadius: '9999px',
          width: width ?? 40,
          height: height ?? 40,
        }
      : style;

  return (
    <div
      data-testid="loading-skeleton"
      role="status"
      aria-label="Loading content"
      className={`skeleton skeleton-${variant} animate-pulse ${className}`}
      style={extraStyle}
    />
  );
}

// Card Loading Skeleton (matches tests)
export function LoadingCard({
  showImage = true,
  contentLines = 3,
  className = '',
}: {
  showImage?: boolean;
  contentLines?: number;
  className?: string;
}) {
  return (
    <div
      data-testid="loading-card"
      className={`card-base bg-white border-2 border-gray-300 p-6 animate-pulse ${className}`}
    >
      {showImage && (
        <div
          data-testid="card-image-skeleton"
          className="w-full h-48 bg-gray-200 rounded-lg mb-4"
        />
      )}
      <div
        data-testid="card-title-skeleton"
        className="h-6 bg-gray-200 rounded mb-3"
      />
      <div data-testid="card-content-skeleton" className="space-y-2 mb-4">
        {Array.from({ length: contentLines }).map((_, i) => (
          <div
            key={i}
            data-testid="card-content-line"
            className="h-4 bg-gray-200 rounded"
          />
        ))}
      </div>
    </div>
  );
}

// Page Loading State
export function PageLoading() {
  return (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center">
        <LoadingSpinner size="lg" className="mx-auto mb-4" />
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}

// Card Skeleton
export function CardSkeleton({
  showImage = true,
  showTitle = true,
  showDescription = true,
  showButton = true,
  className = ''
}: {
  showImage?: boolean;
  showTitle?: boolean;
  showDescription?: boolean;
  showButton?: boolean;
  className?: string;
}) {
  return (
    <div data-testid="loading-card" className={`card-base bg-white border-2 border-gray-300 p-6 animate-pulse ${className}`}>
      {showImage && (
        <div className="w-full h-48 bg-gray-200 rounded-lg mb-4" />
      )}

      {showTitle && (
        <div className="h-6 bg-gray-200 rounded mb-3" />
      )}

      {showDescription && (
        <div className="space-y-2 mb-4">
          <div className="h-4 bg-gray-200 rounded" />
          <div className="h-4 bg-gray-200 rounded w-3/4" />
        </div>
      )}

      {showButton && (
        <div className="h-10 bg-gray-200 rounded" />
      )}
    </div>
  );
}

// Grid Skeleton
export function GridSkeleton({
  count = 6,
  columns = 3,
  cardProps = {}
}: {
  count?: number;
  columns?: number | { sm?: 1|2|3|4; md?: 1|2|3|4; lg?: 1|2|3|4; xl?: 1|2|3|4 };
  cardProps?: Parameters<typeof CardSkeleton>[0];
}) {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
  } as const;

  const columnsClasses = typeof columns === 'number'
    ? gridClasses[Math.min(Math.max(columns as number,1),4)]
    : [
        columns.sm ? gridClasses[columns.sm] : 'grid-cols-1',
        columns.md ? `md:${gridClasses[columns.md]}` : undefined,
        columns.lg ? `lg:${gridClasses[columns.lg]}` : undefined,
        columns.xl ? `xl:${gridClasses[columns.xl]}` : undefined,
      ].filter(Boolean).join(' ');

  return (
    <div data-testid="loading-grid" className={`grid ${columnsClasses} gap-6`}>
      {Array.from({ length: count }).map((_, index) => (
        count > 20 ? (
          <div key={index} data-testid="loading-card" className="card-base bg-white border-2 border-gray-300 p-6 animate-pulse" />
        ) : (
          <LoadingCard key={index} {...(cardProps as any)} />
        )
      ))}
    </div>
  );
}

// List Skeleton
export function ListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex-shrink-0" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 rounded w-1/4" />
              <div className="h-3 bg-gray-200 rounded w-3/4" />
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded" />
          </div>
        </div>
      ))}
    </div>
  );
}

// Table Skeleton
export function TableSkeleton({
  rows = 5,
  columns = 4
}: {
  rows?: number;
  columns?: number;
}) {
  return (
    <div className="animate-pulse">
      {/* Table Header */}
      <div className="grid grid-cols-4 gap-4 p-4 border-b border-gray-200">
        {Array.from({ length: columns }).map((_, index) => (
          <div key={index} className="h-4 bg-gray-200 rounded" />
        ))}
      </div>

      {/* Table Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid grid-cols-4 gap-4 p-4 border-b border-gray-100">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="h-4 bg-gray-200 rounded" />
          ))}
        </div>
      ))}
    </div>
  );
}

// Hero Skeleton
export function HeroSkeleton() {
  return (
    <div className="relative h-96 bg-gray-200 animate-pulse">
      <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent" />
      <div className="absolute bottom-8 left-8 space-y-4">
        <div className="h-8 bg-white/20 rounded w-64" />
        <div className="h-4 bg-white/20 rounded w-48" />
        <div className="h-10 bg-white/20 rounded w-32" />
      </div>
    </div>
  );
}

// Gallery Skeleton
export function GallerySkeleton({ count = 12 }: { count?: number }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="aspect-square bg-gray-200 rounded-lg animate-pulse"
        />
      ))}
    </div>
  );
}

// Timeline Skeleton
export function TimelineSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-8">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="flex animate-pulse">
          <div className="flex flex-col items-center mr-4">
            <div className="w-4 h-4 bg-gray-200 rounded-full" />
            {index < count - 1 && (
              <div className="w-0.5 h-16 bg-gray-200 mt-2" />
            )}
          </div>
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-24" />
            <div className="h-5 bg-gray-200 rounded w-48" />
            <div className="h-3 bg-gray-200 rounded w-full" />
            <div className="h-3 bg-gray-200 rounded w-3/4" />
          </div>
        </div>
      ))}
    </div>
  );
}

// Search Results Skeleton
export function SearchResultsSkeleton({ count = 8 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <div key={index} className="animate-pulse">
          <div className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
            <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0" />
            <div className="flex-1 space-y-2">
              <div className="h-5 bg-gray-200 rounded w-1/3" />
              <div className="h-4 bg-gray-200 rounded w-1/4" />
              <div className="h-3 bg-gray-200 rounded w-full" />
              <div className="h-3 bg-gray-200 rounded w-2/3" />
              <div className="flex space-x-2 mt-2">
                <div className="h-6 bg-gray-200 rounded w-16" />
                <div className="h-6 bg-gray-200 rounded w-20" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

// Chapter Grid Skeleton
export function ChapterGrid({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} variant="elevated" className="animate-pulse overflow-hidden">
          {/* Image skeleton */}
          <div className="h-48 bg-gray-200" />

          {/* Content skeleton */}
          <div className="p-6 space-y-4">
            {/* Title and location */}
            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded w-3/4" />
              <div className="h-4 bg-gray-200 rounded w-1/2" />
            </div>

            {/* Summary */}
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded" />
              <div className="h-4 bg-gray-200 rounded w-5/6" />
              <div className="h-4 bg-gray-200 rounded w-2/3" />
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="h-4 bg-gray-200 rounded" />
              <div className="h-4 bg-gray-200 rounded" />
            </div>

            {/* Contact */}
            <div className="h-4 bg-gray-200 rounded w-4/5" />

            {/* Button */}
            <div className="h-10 bg-gray-200 rounded" />
          </div>
        </Card>
      ))}
    </div>
  );
}

// Button Loading State
export function ButtonLoading({
  children,
  loading = false,
  loadingText,
  ...props
}: {
  children: React.ReactNode;
  loading?: boolean;
  loadingText?: string;
  [key: string]: any;
}) {
  return (
    <button
      {...(() => { const { loadingText: _omit, ...rest } = props; return rest; })()}
      disabled={loading || props.disabled}
      aria-busy={loading ? true : undefined}
      aria-live={loading ? 'polite' : undefined}
      aria-disabled={loading ? true : undefined}
    >
      {loading ? (
        <div className="flex items-center justify-center">
          <LoadingSpinner size="sm" className="mr-2" />
          {loadingText ?? 'Loading...'}
        </div>
      ) : (
        children
      )}
    </button>
  );
}

export const LoadingButton = ButtonLoading;


// Chapter Detail Skeleton
export function ChapterDetail() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Skeleton */}
      <div className="relative h-96 bg-gray-200 animate-pulse">
        <div className="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent" />
        <div className="absolute bottom-8 left-8 space-y-4">
          <div className="h-8 bg-white/20 rounded w-64" />
          <div className="h-4 bg-white/20 rounded w-48" />
          <div className="h-4 bg-white/20 rounded w-32" />
        </div>
      </div>

      {/* Navigation Tabs Skeleton */}
      <div className="bg-white border-b">
        <div className="container-custom">
          <div className="flex space-x-8 animate-pulse">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="py-4">
                <div className="h-4 bg-gray-200 rounded w-20" />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Skeleton */}
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 animate-pulse">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            <Card variant="elevated">
              <div className="p-6 space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/3" />
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-3/4" />
                </div>
              </div>
            </Card>

            <Card variant="elevated">
              <div className="p-6 space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/4" />
                <TimelineSkeleton count={3} />
              </div>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card variant="elevated">
              <div className="p-6 space-y-4">
                <div className="h-6 bg-gray-200 rounded w-2/3" />
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-10 bg-gray-200 rounded" />
                </div>
              </div>
            </Card>

            <Card variant="elevated">
              <div className="p-6 space-y-4">
                <div className="h-6 bg-gray-200 rounded w-1/2" />
                <div className="space-y-3">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="flex justify-between">
                      <div className="h-4 bg-gray-200 rounded w-1/3" />
                      <div className="h-4 bg-gray-200 rounded w-1/4" />
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

// Error State Component
export function ErrorState({
  title = "Something went wrong",
  message = "We encountered an error while loading this content.",
  onRetry,
  showRetry = true
}: {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showRetry?: boolean;
}) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 text-gray-400">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-600 mb-4">{message}</p>
      {showRetry && onRetry && (
        <button
          onClick={onRetry}
          className="px-4 py-2 bg-maroon-700 text-white rounded-lg hover:bg-maroon-800 transition-colors"
        >
          Try Again
        </button>
      )}
    </div>
  );
}

// Loading Grid (alias for GridSkeleton)
export const LoadingGrid = GridSkeleton;

// Memorial Grid Skeleton
export function MemorialGrid({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} variant="elevated" padding="lg" className="animate-pulse">
          <div className="flex items-start space-x-4 mb-4">
            {/* Photo skeleton */}
            <div className="relative flex-shrink-0">
              <div className="w-20 h-20 rounded-full bg-gray-200" />
              <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gray-300 rounded-full" />
            </div>

            {/* Info skeleton */}
            <div className="flex-1 min-w-0 space-y-2">
              <div className="h-5 bg-gray-200 rounded w-3/4" />
              <div className="h-4 bg-gray-200 rounded w-1/2" />
              <div className="h-4 bg-gray-200 rounded w-2/3" />
            </div>
          </div>

          {/* Details skeleton */}
          <div className="mb-4 space-y-2">
            <div className="h-4 bg-gray-200 rounded w-4/5" />
            <div className="h-4 bg-gray-200 rounded w-3/5" />
          </div>

          {/* Tribute preview skeleton */}
          <div className="flex-1 mb-4 space-y-2">
            <div className="h-4 bg-gray-200 rounded" />
            <div className="h-4 bg-gray-200 rounded" />
            <div className="h-4 bg-gray-200 rounded w-3/4" />
          </div>

          {/* Button skeleton */}
          <div className="pt-4 border-t border-gray-100">
            <div className="h-8 bg-gray-200 rounded w-full" />
          </div>
        </Card>
      ))}
    </div>
  );
}

// Export all loading states as a namespace
export const LoadingStates = {
  Spinner: LoadingSpinner,
  Page: PageLoading,
  Card: CardSkeleton,
  Grid: GridSkeleton,
  List: ListSkeleton,
  Table: TableSkeleton,
  Hero: HeroSkeleton,
  Gallery: GallerySkeleton,
  Timeline: TimelineSkeleton,
  SearchResults: SearchResultsSkeleton,
  ChapterGrid,
  ChapterDetail,
  MemorialGrid,
  Button: ButtonLoading,
  Error: ErrorState,
};