import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import type { Event, PaginatedResponse, SearchFilters } from '@/lib/types';

interface EventsQueryParams extends SearchFilters {
  page?: number;
  limit?: number;
  status?: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  organizer?: string;
}

export function useEvents(params: EventsQueryParams = {}) {
  return useQuery({
    queryKey: ['events', params],
    queryFn: async () => {
      const response = await apiClient.getPaginated<Event>('/events', params);
      return response;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useEvent(slug: string) {
  return useQuery({
    queryKey: ['events', slug],
    queryFn: async () => {
      const response = await apiClient.get<Event>(`/events/${slug}`);
      return response.data;
    },
    enabled: !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}