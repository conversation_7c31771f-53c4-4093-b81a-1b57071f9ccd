'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/Button';
import { useForm } from '@/hooks/useForm';
import { CondolenceFormData } from '@/lib/types';
import { condolenceFormSchema } from '@/lib/validation';

interface CondolenceFormProps {
  memorialId: string;
  onSubmit: () => void;
  onCancel: () => void;
}

export function CondolenceForm({ memorialId, onSubmit, onCancel }: CondolenceFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const {
    data,
    errors,
    touched,
    isValid,
    handleChange,
    handleBlur,
    handleSubmit,
    reset
  } = useForm<CondolenceFormData>({
    initialData: {
      name: '',
      message: '',
      relationship: ''
    },
    validationSchema: condolenceFormSchema
  });

  const onFormSubmit = async (formData: CondolenceFormData) => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call - replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      console.log('Submitting condolence:', {
        memorialId,
        ...formData,
        date: new Date().toISOString(),
        approved: false // Requires moderation
      });
      
      setSubmitSuccess(true);
      reset();
      
      // Show success message briefly then close
      setTimeout(() => {
        setSubmitSuccess(false);
        onSubmit();
      }, 2000);
      
    } catch (error) {
      console.error('Error submitting condolence:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-green-50 border border-green-200 rounded-lg p-6 text-center"
      >
        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-green-800 mb-2">Thank You</h3>
        <p className="text-green-700">
          Your condolence message has been submitted and will be reviewed before being published.
        </p>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-gray-50 border border-gray-200 rounded-lg p-6"
    >
      <div className="mb-4">
        <h4 className="text-lg font-medium text-gray-900 mb-2">Leave a Condolence Message</h4>
        <p className="text-sm text-gray-600">
          Share your memories and condolences. All messages are reviewed before being published.
        </p>
      </div>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
        {/* Name Field */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            Your Name *
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={data.name}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 ${
              touched.name && errors.name?.length
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-maroon-500 focus:ring-maroon-500'
            }`}
            placeholder="Enter your full name"
          />
          {touched.name && errors.name?.length > 0 && (
            <p className="mt-1 text-sm text-red-600">{errors.name[0]}</p>
          )}
        </div>

        {/* Relationship Field */}
        <div>
          <label htmlFor="relationship" className="block text-sm font-medium text-gray-700 mb-1">
            Relationship (Optional)
          </label>
          <input
            type="text"
            id="relationship"
            name="relationship"
            value={data.relationship}
            onChange={handleChange}
            onBlur={handleBlur}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:border-maroon-500 focus:ring-maroon-500"
            placeholder="e.g., Classmate, Friend, Colleague"
          />
        </div>

        {/* Message Field */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
            Condolence Message *
          </label>
          <textarea
            id="message"
            name="message"
            rows={4}
            value={data.message}
            onChange={handleChange}
            onBlur={handleBlur}
            className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 resize-none ${
              touched.message && errors.message?.length
                ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-maroon-500 focus:ring-maroon-500'
            }`}
            placeholder="Share your memories, condolences, or thoughts about the departed..."
          />
          {touched.message && errors.message?.length > 0 && (
            <p className="mt-1 text-sm text-red-600">{errors.message[0]}</p>
          )}
          <p className="mt-1 text-xs text-gray-500">
            Please be respectful and appropriate in your message.
          </p>
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onCancel}
            disabled={isSubmitting}
            className="text-gray-600 hover:text-gray-700"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            size="sm"
            disabled={!isValid || isSubmitting}
            loading={isSubmitting}
            className="bg-maroon-600 hover:bg-maroon-700 text-white"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Condolence'}
          </Button>
        </div>
      </form>
    </motion.div>
  );
}