import { render, screen, fireEvent } from '@/lib/test-utils';
import { FocusTrap } from '../FocusTrap';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('FocusTrap Component', () => {
  it('renders children correctly', () => {
    render(
      <FocusTrap>
        <div>Trapped content</div>
      </FocusTrap>
    );
    
    expect(screen.getByText('Trapped content')).toBeInTheDocument();
  });

  it('traps focus within the container when active', () => {
    render(
      <FocusTrap active>
        <button>First button</button>
        <button>Second button</button>
        <button>Third button</button>
      </FocusTrap>
    );
    
    const buttons = screen.getAllByRole('button');
    const [first, second, third] = buttons;
    
    // Focus should start on first element
    expect(document.activeElement).toBe(first);
    
    // Tab should move to second button
    fireEvent.keyDown(first, { key: 'Tab' });
    expect(document.activeElement).toBe(second);
    
    // Tab should move to third button
    fireEvent.keyDown(second, { key: 'Tab' });
    expect(document.activeElement).toBe(third);
    
    // Tab from last should cycle back to first
    fireEvent.keyDown(third, { key: 'Tab' });
    expect(document.activeElement).toBe(first);
  });

  it('handles reverse tab navigation', () => {
    render(
      <FocusTrap active>
        <button>First button</button>
        <button>Second button</button>
        <button>Third button</button>
      </FocusTrap>
    );
    
    const buttons = screen.getAllByRole('button');
    const [first, second, third] = buttons;
    
    // Start at first button
    first.focus();
    
    // Shift+Tab should move to last button
    fireEvent.keyDown(first, { key: 'Tab', shiftKey: true });
    expect(document.activeElement).toBe(third);
    
    // Shift+Tab should move to second button
    fireEvent.keyDown(third, { key: 'Tab', shiftKey: true });
    expect(document.activeElement).toBe(second);
  });

  it('does not trap focus when inactive', () => {
    const outsideButton = document.createElement('button');
    outsideButton.textContent = 'Outside button';
    document.body.appendChild(outsideButton);
    
    render(
      <FocusTrap active={false}>
        <button>Inside button</button>
      </FocusTrap>
    );
    
    const insideButton = screen.getByText('Inside button');
    
    // Focus should be able to move outside
    insideButton.focus();
    fireEvent.keyDown(insideButton, { key: 'Tab' });
    
    // Focus should not be trapped
    expect(document.activeElement).not.toBe(insideButton);
    
    document.body.removeChild(outsideButton);
  });

  it('restores focus to previous element when deactivated', () => {
    const triggerButton = document.createElement('button');
    triggerButton.textContent = 'Trigger';
    document.body.appendChild(triggerButton);
    triggerButton.focus();
    
    const { rerender } = render(
      <FocusTrap active>
        <button>Trapped button</button>
      </FocusTrap>
    );
    
    // Deactivate focus trap
    rerender(
      <FocusTrap active={false}>
        <button>Trapped button</button>
      </FocusTrap>
    );
    
    // Focus should return to trigger button
    expect(document.activeElement).toBe(triggerButton);
    
    document.body.removeChild(triggerButton);
  });

  it('handles elements with different focusable types', () => {
    render(
      <FocusTrap active>
        <button>Button</button>
        <input type="text" placeholder="Input" />
        <select>
          <option>Option</option>
        </select>
        <textarea placeholder="Textarea" />
        <a href="#test">Link</a>
      </FocusTrap>
    );
    
    const button = screen.getByRole('button');
    const input = screen.getByPlaceholderText('Input');
    const select = screen.getByRole('combobox');
    const textarea = screen.getByPlaceholderText('Textarea');
    const link = screen.getByRole('link');
    
    // All elements should be included in focus cycle
    expect(document.activeElement).toBe(button);
    
    fireEvent.keyDown(button, { key: 'Tab' });
    expect(document.activeElement).toBe(input);
    
    fireEvent.keyDown(input, { key: 'Tab' });
    expect(document.activeElement).toBe(select);
    
    fireEvent.keyDown(select, { key: 'Tab' });
    expect(document.activeElement).toBe(textarea);
    
    fireEvent.keyDown(textarea, { key: 'Tab' });
    expect(document.activeElement).toBe(link);
  });

  it('ignores disabled elements', () => {
    render(
      <FocusTrap active>
        <button>Enabled button</button>
        <button disabled>Disabled button</button>
        <input type="text" placeholder="Enabled input" />
        <input type="text" disabled placeholder="Disabled input" />
      </FocusTrap>
    );
    
    const enabledButton = screen.getByText('Enabled button');
    const enabledInput = screen.getByPlaceholderText('Enabled input');
    
    // Should skip disabled elements
    expect(document.activeElement).toBe(enabledButton);
    
    fireEvent.keyDown(enabledButton, { key: 'Tab' });
    expect(document.activeElement).toBe(enabledInput);
    
    fireEvent.keyDown(enabledInput, { key: 'Tab' });
    expect(document.activeElement).toBe(enabledButton); // Should cycle back
  });

  it('handles elements with tabindex', () => {
    render(
      <FocusTrap active>
        <button tabIndex={1}>Button 1</button>
        <button tabIndex={3}>Button 3</button>
        <button tabIndex={2}>Button 2</button>
        <button tabIndex={0}>Button 0</button>
        <button tabIndex={-1}>Button -1</button>
      </FocusTrap>
    );
    
    // Should respect tabindex order (positive first, then 0, skip -1)
    const button1 = screen.getByText('Button 1');
    const button2 = screen.getByText('Button 2');
    const button3 = screen.getByText('Button 3');
    const button0 = screen.getByText('Button 0');
    
    expect(document.activeElement).toBe(button1);
    
    fireEvent.keyDown(button1, { key: 'Tab' });
    expect(document.activeElement).toBe(button2);
    
    fireEvent.keyDown(button2, { key: 'Tab' });
    expect(document.activeElement).toBe(button3);
    
    fireEvent.keyDown(button3, { key: 'Tab' });
    expect(document.activeElement).toBe(button0);
  });

  it('handles nested focusable elements', () => {
    render(
      <FocusTrap active>
        <div>
          <button>Outer button</button>
          <div>
            <button>Inner button</button>
            <div>
              <input type="text" placeholder="Deep input" />
            </div>
          </div>
        </div>
      </FocusTrap>
    );
    
    const outerButton = screen.getByText('Outer button');
    const innerButton = screen.getByText('Inner button');
    const deepInput = screen.getByPlaceholderText('Deep input');
    
    // Should find all focusable elements regardless of nesting
    expect(document.activeElement).toBe(outerButton);
    
    fireEvent.keyDown(outerButton, { key: 'Tab' });
    expect(document.activeElement).toBe(innerButton);
    
    fireEvent.keyDown(innerButton, { key: 'Tab' });
    expect(document.activeElement).toBe(deepInput);
  });

  it('handles dynamic content changes', () => {
    const { rerender } = render(
      <FocusTrap active>
        <button>Button 1</button>
      </FocusTrap>
    );
    
    // Add more buttons
    rerender(
      <FocusTrap active>
        <button>Button 1</button>
        <button>Button 2</button>
        <button>Button 3</button>
      </FocusTrap>
    );
    
    const button1 = screen.getByText('Button 1');
    const button2 = screen.getByText('Button 2');
    const button3 = screen.getByText('Button 3');
    
    // Should include new buttons in focus cycle
    fireEvent.keyDown(button1, { key: 'Tab' });
    expect(document.activeElement).toBe(button2);
    
    fireEvent.keyDown(button2, { key: 'Tab' });
    expect(document.activeElement).toBe(button3);
  });

  it('calls onActivate and onDeactivate callbacks', () => {
    const onActivate = jest.fn();
    const onDeactivate = jest.fn();
    
    const { rerender } = render(
      <FocusTrap
        active={false}
        onActivate={onActivate}
        onDeactivate={onDeactivate}
      >
        <button>Button</button>
      </FocusTrap>
    );
    
    // Activate
    rerender(
      <FocusTrap
        active={true}
        onActivate={onActivate}
        onDeactivate={onDeactivate}
      >
        <button>Button</button>
      </FocusTrap>
    );
    
    expect(onActivate).toHaveBeenCalledTimes(1);
    
    // Deactivate
    rerender(
      <FocusTrap
        active={false}
        onActivate={onActivate}
        onDeactivate={onDeactivate}
      >
        <button>Button</button>
      </FocusTrap>
    );
    
    expect(onDeactivate).toHaveBeenCalledTimes(1);
  });

  it('handles escape key to deactivate', () => {
    const onEscape = jest.fn();
    
    render(
      <FocusTrap active onEscape={onEscape}>
        <button>Button</button>
      </FocusTrap>
    );
    
    fireEvent.keyDown(document, { key: 'Escape' });
    expect(onEscape).toHaveBeenCalledTimes(1);
  });

  it('has no accessibility violations', async () => {
    const { container } = render(
      <FocusTrap active>
        <button>Button 1</button>
        <button>Button 2</button>
      </FocusTrap>
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('works with custom focus selector', () => {
    render(
      <FocusTrap active focusSelector="[data-focusable]">
        <button>Regular button</button>
        <div data-focusable tabIndex={0}>Custom focusable</div>
        <button data-focusable>Focusable button</button>
      </FocusTrap>
    );
    
    const customFocusable = screen.getByText('Custom focusable');
    const focusableButton = screen.getByText('Focusable button');
    
    // Should only focus elements matching custom selector
    expect(document.activeElement).toBe(customFocusable);
    
    fireEvent.keyDown(customFocusable, { key: 'Tab' });
    expect(document.activeElement).toBe(focusableButton);
    
    fireEvent.keyDown(focusableButton, { key: 'Tab' });
    expect(document.activeElement).toBe(customFocusable); // Should cycle back
  });

  it('handles empty containers gracefully', () => {
    render(
      <FocusTrap active>
        <div>No focusable elements</div>
      </FocusTrap>
    );
    
    // Should not throw error with no focusable elements
    expect(() => {
      fireEvent.keyDown(document, { key: 'Tab' });
    }).not.toThrow();
  });
});