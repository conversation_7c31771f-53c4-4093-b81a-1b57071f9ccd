/**
 * Core Web Vitals optimization utilities
 */

import { onCLS, onFCP, onINP, onLCP, onTTFB, Metric } from 'web-vitals';

export interface WebVitalsMetrics {
  cls: number;
  fcp: number;
  inp: number;
  lcp: number;
  ttfb: number;
}

export interface WebVitalsThresholds {
  cls: { good: number; poor: number };
  fcp: { good: number; poor: number };
  inp: { good: number; poor: number };
  lcp: { good: number; poor: number };
  ttfb: { good: number; poor: number };
}

// Core Web Vitals thresholds (Google's recommendations)
export const WEB_VITALS_THRESHOLDS: WebVitalsThresholds = {
  cls: { good: 0.1, poor: 0.25 },
  fcp: { good: 1800, poor: 3000 },
  inp: { good: 200, poor: 500 },
  lcp: { good: 2500, poor: 4000 },
  ttfb: { good: 800, poor: 1800 },
};

/**
 * Get performance rating based on thresholds
 */
export function getPerformanceRating(
  value: number,
  thresholds: { good: number; poor: number }
): 'good' | 'needs-improvement' | 'poor' {
  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
}

/**
 * Calculate overall performance score
 */
export function calculateWebVitalsScore(metrics: Partial<WebVitalsMetrics>): number {
  let score = 0;
  let count = 0;

  Object.entries(metrics).forEach(([key, value]) => {
    if (value !== undefined) {
      const threshold = WEB_VITALS_THRESHOLDS[key as keyof WebVitalsThresholds];
      const rating = getPerformanceRating(value, threshold);
      
      switch (rating) {
        case 'good':
          score += 100;
          break;
        case 'needs-improvement':
          score += 50;
          break;
        case 'poor':
          score += 0;
          break;
      }
      count++;
    }
  });

  return count > 0 ? Math.round(score / count) : 0;
}

/**
 * Web Vitals monitoring class
 */
export class WebVitalsMonitor {
  private metrics: Partial<WebVitalsMetrics> = {};
  private callbacks: Array<(metrics: Partial<WebVitalsMetrics>) => void> = [];
  private isInitialized = false;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initialize();
    }
  }

  private initialize(): void {
    if (this.isInitialized) return;
    this.isInitialized = true;

    // Measure Core Web Vitals
    onCLS(this.handleMetric.bind(this, 'cls'));
    onFCP(this.handleMetric.bind(this, 'fcp'));
    onINP(this.handleMetric.bind(this, 'inp'));
    onLCP(this.handleMetric.bind(this, 'lcp'));
    onTTFB(this.handleMetric.bind(this, 'ttfb'));
  }

  private handleMetric(name: keyof WebVitalsMetrics, metric: Metric): void {
    this.metrics[name] = metric.value;
    
    // Notify callbacks
    this.callbacks.forEach(callback => callback(this.metrics));
    
    // Log in development
    if (process.env.NODE_ENV === 'development') {
      const rating = getPerformanceRating(
        metric.value,
        WEB_VITALS_THRESHOLDS[name]
      );
      
      console.log(`${name.toUpperCase()}: ${metric.value} (${rating})`);
    }
  }

  public onMetricsUpdate(callback: (metrics: Partial<WebVitalsMetrics>) => void): void {
    this.callbacks.push(callback);
  }

  public getMetrics(): Partial<WebVitalsMetrics> {
    return { ...this.metrics };
  }

  public getScore(): number {
    return calculateWebVitalsScore(this.metrics);
  }
}

/**
 * Layout Shift prevention utilities
 */
export class LayoutShiftPrevention {
  private static resizeObserver: ResizeObserver | null = null;
  private static observedElements = new WeakSet();

  /**
   * Observe element for size changes and prevent layout shifts
   */
  static observeElement(element: HTMLElement, callback?: () => void): void {
    if (this.observedElements.has(element)) return;

    if (!this.resizeObserver) {
      this.resizeObserver = new ResizeObserver((entries) => {
        entries.forEach((entry) => {
          const element = entry.target as HTMLElement;
          
          // Set explicit dimensions to prevent layout shift
          if (entry.contentRect.width > 0 && entry.contentRect.height > 0) {
            element.style.minWidth = `${entry.contentRect.width}px`;
            element.style.minHeight = `${entry.contentRect.height}px`;
          }
          
          callback?.();
        });
      });
    }

    this.resizeObserver.observe(element);
    this.observedElements.add(element);
  }

  /**
   * Set aspect ratio to prevent layout shift
   */
  static setAspectRatio(element: HTMLElement, ratio: string): void {
    element.style.aspectRatio = ratio;
    element.style.width = '100%';
  }

  /**
   * Reserve space for dynamic content
   */
  static reserveSpace(element: HTMLElement, width: number, height: number): void {
    element.style.minWidth = `${width}px`;
    element.style.minHeight = `${height}px`;
  }
}

/**
 * Critical resource preloading
 */
export class ResourcePreloader {
  private static preloadedResources = new Set<string>();

  /**
   * Preload critical CSS
   */
  static preloadCSS(href: string, media: string = 'all'): void {
    if (this.preloadedResources.has(href)) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    link.media = media;
    
    // Convert to stylesheet after load
    link.onload = () => {
      link.rel = 'stylesheet';
    };

    document.head.appendChild(link);
    this.preloadedResources.add(href);
  }

  /**
   * Preload critical JavaScript
   */
  static preloadJS(src: string): void {
    if (this.preloadedResources.has(src)) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'script';
    link.href = src;

    document.head.appendChild(link);
    this.preloadedResources.add(src);
  }

  /**
   * Preload critical images
   */
  static preloadImage(src: string, fetchPriority: 'high' | 'low' = 'high'): void {
    if (this.preloadedResources.has(src)) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    link.fetchPriority = fetchPriority;

    document.head.appendChild(link);
    this.preloadedResources.add(src);
  }

  /**
   * Preload critical fonts
   */
  static preloadFont(href: string, type: string = 'font/woff2'): void {
    if (this.preloadedResources.has(href)) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'font';
    link.type = type;
    link.href = href;
    link.crossOrigin = 'anonymous';

    document.head.appendChild(link);
    this.preloadedResources.add(href);
  }
}

/**
 * Performance optimization hooks
 */
export function useWebVitals(
  onMetricsUpdate?: (metrics: Partial<WebVitalsMetrics>) => void
): {
  metrics: Partial<WebVitalsMetrics>;
  score: number;
  monitor: WebVitalsMonitor;
} {
  const monitor = new WebVitalsMonitor();
  
  if (onMetricsUpdate) {
    monitor.onMetricsUpdate(onMetricsUpdate);
  }

  return {
    metrics: monitor.getMetrics(),
    score: monitor.getScore(),
    monitor,
  };
}

/**
 * Optimize images for Core Web Vitals
 */
export function optimizeImageForCWV(
  img: HTMLImageElement,
  options: {
    priority?: boolean;
    aspectRatio?: string;
    sizes?: string;
  } = {}
): void {
  const { priority = false, aspectRatio, sizes } = options;

  // Set loading priority
  img.loading = priority ? 'eager' : 'lazy';
  img.fetchPriority = priority ? 'high' : 'auto';
  img.decoding = 'async';

  // Set sizes for responsive images
  if (sizes) {
    img.sizes = sizes;
  }

  // Prevent layout shift with aspect ratio
  if (aspectRatio) {
    img.style.aspectRatio = aspectRatio;
    img.style.width = '100%';
    img.style.height = 'auto';
  }

  // Add intersection observer for lazy loading
  if (!priority && 'IntersectionObserver' in window) {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            observer.unobserve(img);
          }
        });
      },
      { rootMargin: '200px 0px' }
    );

    observer.observe(img);
  }
}

/**
 * Global Web Vitals monitoring setup
 */
export function setupWebVitalsMonitoring(): void {
  if (typeof window === 'undefined') return;

  const monitor = new WebVitalsMonitor();

  // Send metrics to analytics (replace with your analytics service)
  monitor.onMetricsUpdate((metrics) => {
    // Example: Google Analytics 4
    if ('gtag' in window) {
      Object.entries(metrics).forEach(([name, value]) => {
        if (value !== undefined) {
          (window as any).gtag('event', 'web_vitals', {
            event_category: 'Performance',
            event_label: name.toUpperCase(),
            value: Math.round(name === 'cls' ? value * 1000 : value),
            custom_map: { metric_value: 'value' },
          });
        }
      });
    }

    // Log performance issues in development
    if (process.env.NODE_ENV === 'development') {
      const score = calculateWebVitalsScore(metrics);
      if (score < 75) {
        console.warn('Performance issues detected:', metrics);
      }
    }
  });
}

/**
 * Optimize third-party scripts
 */
export function optimizeThirdPartyScript(
  src: string,
  options: {
    strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive';
    onLoad?: () => void;
    onError?: () => void;
  } = {}
): void {
  const { strategy = 'afterInteractive', onLoad, onError } = options;

  const script = document.createElement('script');
  script.src = src;
  script.async = true;

  if (onLoad) script.onload = onLoad;
  if (onError) script.onerror = onError;

  // Load strategy
  switch (strategy) {
    case 'beforeInteractive':
      document.head.appendChild(script);
      break;
    case 'afterInteractive':
      document.addEventListener('DOMContentLoaded', () => {
        document.head.appendChild(script);
      });
      break;
    case 'lazyOnload':
      window.addEventListener('load', () => {
        setTimeout(() => {
          document.head.appendChild(script);
        }, 1000);
      });
      break;
  }
}