'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent } from '@/components/ui';
import { GalleryGrid } from '@/components/gallery';
import { 
  PhotoIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { mockChapterGalleryImages } from '@/mocks/data/chapterGallery';
import type { GalleryImage } from '@/lib/types';

interface ChapterGalleryProps {
  chapterSlug: string;
}

export function ChapterGallery({ chapterSlug }: ChapterGalleryProps) {
  const [selectedAlbum, setSelectedAlbum] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Get gallery images for this chapter
  const allImages = mockChapterGalleryImages[chapterSlug] || [];

  // Get unique albums
  const albums = useMemo(() => {
    const albumSet = new Set(allImages.map(img => img.album).filter(Boolean));
    return ['all', ...Array.from(albumSet)];
  }, [allImages]);

  // Filter images based on selected album and search
  const filteredImages = useMemo(() => {
    let filtered = allImages;

    // Filter by album
    if (selectedAlbum !== 'all') {
      filtered = filtered.filter(img => img.album === selectedAlbum);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(img =>
        img.caption?.toLowerCase().includes(query) ||
        img.photographer?.toLowerCase().includes(query) ||
        img.album?.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [allImages, selectedAlbum, searchQuery]);

  const handleClearFilters = () => {
    setSelectedAlbum('all');
    setSearchQuery('');
  };

  const hasActiveFilters = selectedAlbum !== 'all' || searchQuery.trim();

  if (allImages.length === 0) {
    return (
      <div>
        <div className="mb-8">
          <h2 className="heading-2 text-2xl mb-4">Chapter Gallery</h2>
          <p className="text-gray-600">
            Browse through photos and memories from chapter events and activities.
          </p>
        </div>

        <Card variant="elevated" className="text-center py-12">
          <CardContent>
            <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Photos Yet</h3>
            <p className="text-gray-500">This chapter hasn&apos;t uploaded any photos yet.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="heading-2 text-2xl mb-4">Chapter Gallery</h2>
        <p className="text-gray-600">
          Browse through photos and memories from chapter events and activities.
        </p>
      </div>

      {/* Filters */}
      <div className="mb-8 space-y-4">
        {/* Search */}
        <div className="relative max-w-md">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search photos..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className={cn(
              "w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg",
              "focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500",
              "placeholder:text-gray-400 text-sm",
              "transition-colors duration-200"
            )}
          />
        </div>

        {/* Album filters */}
        <div className="flex flex-wrap items-center gap-3">
          <div className="flex items-center gap-2">
            <FunnelIcon className="w-5 h-5 text-gray-400" />
            <span className="text-sm text-gray-600 font-medium">Albums:</span>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {albums.map((album) => (
              <button
                key={album}
                onClick={() => setSelectedAlbum(album)}
                className={cn(
                  'px-3 py-1.5 text-sm rounded-full border transition-all duration-200',
                  'hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-maroon-500 focus:ring-offset-1',
                  selectedAlbum === album
                    ? 'bg-maroon-700 text-white border-maroon-700 shadow-sm'
                    : 'bg-white text-gray-600 border-gray-300 hover:border-maroon-300 hover:text-maroon-700'
                )}
              >
                {album === 'all' ? 'All Photos' : album}
              </button>
            ))}
          </div>

          {hasActiveFilters && (
            <button
              onClick={handleClearFilters}
              className={cn(
                "flex items-center gap-1.5 px-3 py-1.5 text-sm",
                "text-gray-500 hover:text-gray-700",
                "border border-gray-300 rounded-full",
                "hover:border-gray-400 transition-colors duration-200",
                "focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
              )}
            >
              Clear
            </button>
          )}
        </div>

        {/* Results count */}
        <div className="text-sm text-gray-600">
          {filteredImages.length === allImages.length 
            ? `${allImages.length} photos`
            : `${filteredImages.length} of ${allImages.length} photos`
          }
        </div>
      </div>

      {/* Gallery Grid */}
      {filteredImages.length === 0 ? (
        <Card variant="elevated" className="text-center py-12">
          <CardContent>
            <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No Photos Found</h3>
            <p className="text-gray-500 mb-4">
              {hasActiveFilters
                ? 'Try adjusting your search or filter criteria.'
                : 'No photos match your current filters.'}
            </p>
            {hasActiveFilters && (
              <button
                onClick={handleClearFilters}
                className="text-maroon-700 hover:text-maroon-800 font-medium"
              >
                Clear all filters
              </button>
            )}
          </CardContent>
        </Card>
      ) : (
        <GalleryGrid
          images={filteredImages}
          columns={3}
          showLightbox={true}
        />
      )}
    </div>
  );
}