import { test, expect } from '@playwright/test';

test.describe('Homepage', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should load and display main elements', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/FGC Ikom Alumni Association/);
    
    // Check main heading
    await expect(page.getByRole('heading', { name: /welcome to fgc ikom alumni/i })).toBeVisible();
    
    // Check navigation
    await expect(page.getByRole('navigation')).toBeVisible();
    await expect(page.getByRole('link', { name: /chapters/i })).toBeVisible();
    await expect(page.getByRole('link', { name: /sets/i })).toBeVisible();
    await expect(page.getByRole('link', { name: /events/i })).toBeVisible();
    
    // Check hero section
    await expect(page.locator('[data-testid="hero-section"]')).toBeVisible();
  });

  test('should have working navigation links', async ({ page }) => {
    // Test chapters link
    await page.getByRole('link', { name: /chapters/i }).click();
    await expect(page).toHaveURL(/\/chapters/);
    await expect(page.getByRole('heading', { name: /chapters/i })).toBeVisible();
    
    // Go back to homepage
    await page.goto('/');
    
    // Test sets link
    await page.getByRole('link', { name: /sets/i }).click();
    await expect(page).toHaveURL(/\/sets/);
    await expect(page.getByRole('heading', { name: /sets/i })).toBeVisible();
    
    // Go back to homepage
    await page.goto('/');
    
    // Test events link
    await page.getByRole('link', { name: /events/i }).click();
    await expect(page).toHaveURL(/\/events/);
    await expect(page.getByRole('heading', { name: /events/i })).toBeVisible();
  });

  test('should be responsive', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1200, height: 800 });
    await expect(page.locator('nav')).toBeVisible();
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await expect(page.locator('nav')).toBeVisible();
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    // Mobile menu might be collapsed, so we check for the mobile menu button
    const mobileMenuButton = page.locator('[aria-label*="menu"]').first();
    if (await mobileMenuButton.isVisible()) {
      await mobileMenuButton.click();
    }
    await expect(page.getByRole('link', { name: /chapters/i })).toBeVisible();
  });

  test('should have accessible skip link', async ({ page }) => {
    // Focus the page and press Tab to reveal skip link
    await page.keyboard.press('Tab');
    
    // Check if skip link is visible when focused
    const skipLink = page.getByRole('link', { name: /skip to main content/i });
    await expect(skipLink).toBeFocused();
    
    // Click skip link and verify main content is focused
    await skipLink.click();
    await expect(page.locator('#main-content')).toBeFocused();
  });

  test('should load hero carousel', async ({ page }) => {
    // Wait for carousel to load
    await expect(page.locator('[data-testid="hero-carousel"]')).toBeVisible();
    
    // Check for carousel slides
    const slides = page.locator('[data-testid="carousel-slide"]');
    await expect(slides.first()).toBeVisible();
    
    // Test carousel navigation if present
    const nextButton = page.locator('[aria-label*="next"]').first();
    if (await nextButton.isVisible()) {
      await nextButton.click();
      // Wait for transition
      await page.waitForTimeout(500);
    }
  });

  test('should display highlight cards', async ({ page }) => {
    // Check for highlight cards section
    await expect(page.locator('[data-testid="highlight-cards"]')).toBeVisible();
    
    // Check for individual cards
    const cards = page.locator('[data-testid="highlight-card"]');
    await expect(cards.first()).toBeVisible();
    
    // Test card interactions
    await cards.first().hover();
    // Cards should have hover effects (tested via CSS)
  });

  test('should have working search functionality', async ({ page }) => {
    // Look for search input
    const searchInput = page.getByPlaceholder(/search/i);
    if (await searchInput.isVisible()) {
      await searchInput.fill('test search');
      await searchInput.press('Enter');
      
      // Verify search was performed (this depends on implementation)
      // For now, just check that the input has the value
      await expect(searchInput).toHaveValue('test search');
    }
  });

  test('should have proper meta tags', async ({ page }) => {
    // Check meta description
    const metaDescription = page.locator('meta[name="description"]');
    await expect(metaDescription).toHaveAttribute('content', /FGC Ikom Alumni/);
    
    // Check Open Graph tags
    const ogTitle = page.locator('meta[property="og:title"]');
    await expect(ogTitle).toHaveAttribute('content', /FGC Ikom Alumni/);
    
    const ogDescription = page.locator('meta[property="og:description"]');
    await expect(ogDescription).toHaveAttribute('content', /.+/);
  });

  test('should load without JavaScript errors', async ({ page }) => {
    const errors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    page.on('pageerror', error => {
      errors.push(error.message);
    });
    
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Filter out known acceptable errors (like network errors in development)
    const criticalErrors = errors.filter(error => 
      !error.includes('Failed to load resource') &&
      !error.includes('net::ERR_') &&
      !error.includes('favicon.ico')
    );
    
    expect(criticalErrors).toHaveLength(0);
  });
});
