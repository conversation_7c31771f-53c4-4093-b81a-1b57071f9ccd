/**
 * Animation utilities that respect user preferences for reduced motion
 */

export interface AnimationConfig {
  duration: number;
  easing: string;
  delay?: number;
}

export interface ReducedMotionConfig {
  duration: number;
  easing: string;
  delay?: number;
}

/**
 * Get animation configuration based on user's motion preference
 */
export function getAnimationConfig(
  normalConfig: AnimationConfig,
  reducedConfig?: Partial<ReducedMotionConfig>
): AnimationConfig {
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  if (prefersReducedMotion) {
    return {
      duration: reducedConfig?.duration ?? 0.1,
      easing: reducedConfig?.easing ?? 'linear',
      delay: reducedConfig?.delay ?? 0,
    };
  }

  return normalConfig;
}

/**
 * CSS class names for reduced motion alternatives
 */
export const reducedMotionClasses = {
  // Instead of animations, use instant state changes with visual feedback
  focusRing: 'ring-2 ring-gold-500 ring-offset-2',
  activeState: 'bg-gold-100 border-gold-300',
  hoverState: 'bg-gray-50 border-gray-300',
  selectedState: 'bg-maroon-50 border-maroon-300',
  
  // Visual indicators for state changes
  success: 'border-l-4 border-green-500 bg-green-50',
  error: 'border-l-4 border-red-500 bg-red-50',
  warning: 'border-l-4 border-yellow-500 bg-yellow-50',
  info: 'border-l-4 border-blue-500 bg-blue-50',
  
  // Pattern-based indicators
  loading: 'bg-gray-100 border-2 border-dashed border-gray-300',
  processing: 'bg-blue-50 border-2 border-blue-200',
  complete: 'bg-green-50 border-2 border-green-200',
};

/**
 * Alternative feedback methods for users who prefer reduced motion
 */
export const alternativeFeedback = {
  /**
   * Provide haptic feedback on supported devices
   */
  haptic: (type: 'light' | 'medium' | 'heavy' = 'light') => {
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30],
      };
      navigator.vibrate(patterns[type]);
    }
  },

  /**
   * Provide audio feedback
   */
  audio: (type: 'success' | 'error' | 'click' = 'click') => {
    // Only play if user hasn't disabled audio
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    
    const frequencies = {
      success: 800,
      error: 300,
      click: 600,
    };

    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = frequencies[type];
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.1);
  },

  /**
   * Provide visual feedback through color changes
   */
  visual: (element: HTMLElement, type: 'success' | 'error' | 'active' | 'focus') => {
    const originalClasses = element.className;
    
    const feedbackClasses = {
      success: 'bg-green-100 border-green-300',
      error: 'bg-red-100 border-red-300',
      active: 'bg-gold-100 border-gold-300',
      focus: 'ring-2 ring-gold-500 ring-offset-2',
    };

    element.className += ` ${feedbackClasses[type]}`;
    
    // Remove feedback after a short delay
    setTimeout(() => {
      element.className = originalClasses;
    }, 200);
  },

  /**
   * Provide text-based feedback for screen readers
   */
  announce: (message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    // Remove after announcement
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },
};

/**
 * Framer Motion variants that respect reduced motion
 */
export const motionVariants = {
  fadeIn: {
    normal: {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 },
      transition: { duration: 0.3, ease: 'easeOut' },
    },
    reduced: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.1 },
    },
  },

  slideIn: {
    normal: {
      initial: { x: -100, opacity: 0 },
      animate: { x: 0, opacity: 1 },
      exit: { x: 100, opacity: 0 },
      transition: { type: 'spring', stiffness: 300, damping: 30 },
    },
    reduced: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.1 },
    },
  },

  scaleIn: {
    normal: {
      initial: { scale: 0.9, opacity: 0 },
      animate: { scale: 1, opacity: 1 },
      exit: { scale: 0.95, opacity: 0 },
      transition: { type: 'spring', stiffness: 300, damping: 30 },
    },
    reduced: {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 },
      transition: { duration: 0.1 },
    },
  },

  hover: {
    normal: {
      scale: 1.02,
      y: -2,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      transition: { type: 'spring', stiffness: 400, damping: 17 },
    },
    reduced: {
      // No animation, just class changes handled by CSS
    },
  },
};

/**
 * Hook to get motion-safe variants
 */
export function getMotionVariants(
  variantName: keyof typeof motionVariants,
  prefersReducedMotion: boolean
) {
  return prefersReducedMotion 
    ? motionVariants[variantName].reduced 
    : motionVariants[variantName].normal;
}

/**
 * CSS-in-JS styles for reduced motion alternatives
 */
export const reducedMotionStyles = {
  button: {
    normal: {
      transition: 'all 0.2s ease-in-out',
      transform: 'translateY(0)',
    },
    hover: {
      transform: 'translateY(-2px)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    },
    active: {
      transform: 'translateY(0)',
      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    },
    reducedMotion: {
      transition: 'background-color 0.1s ease',
      transform: 'none',
      boxShadow: 'none',
    },
  },

  card: {
    normal: {
      transition: 'all 0.3s ease-out',
      transform: 'translateY(0)',
    },
    hover: {
      transform: 'translateY(-6px)',
      boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
    },
    reducedMotion: {
      transition: 'border-color 0.1s ease, background-color 0.1s ease',
      transform: 'none',
      boxShadow: 'none',
    },
  },
};

/**
 * Utility to check if animations should be disabled
 */
export function shouldDisableAnimations(): boolean {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Utility to create a media query listener for reduced motion
 */
export function createReducedMotionListener(callback: (matches: boolean) => void): () => void {
  if (typeof window === 'undefined') return () => {};
  
  const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
  
  const handleChange = (event: MediaQueryListEvent) => {
    callback(event.matches);
  };
  
  mediaQuery.addEventListener('change', handleChange);
  
  // Call immediately with current value
  callback(mediaQuery.matches);
  
  // Return cleanup function
  return () => {
    mediaQuery.removeEventListener('change', handleChange);
  };
}