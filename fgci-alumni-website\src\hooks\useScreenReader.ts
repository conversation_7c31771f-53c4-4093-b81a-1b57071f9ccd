'use client';

import { useEffect, useRef, useCallback } from 'react';

/**
 * Hook for managing screen reader announcements and live regions
 */
export function useScreenReader() {
  const liveRegionRef = useRef<HTMLDivElement | null>(null);

  // Create live region if it doesn't exist
  useEffect(() => {
    if (!liveRegionRef.current) {
      const liveRegion = document.createElement('div');
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      liveRegion.className = 'sr-only';
      liveRegion.id = 'screen-reader-announcements';
      document.body.appendChild(liveRegion);
      liveRegionRef.current = liveRegion;
    }

    return () => {
      if (liveRegionRef.current && document.body.contains(liveRegionRef.current)) {
        document.body.removeChild(liveRegionRef.current);
      }
    };
  }, []);

  /**
   * Announce a message to screen readers
   * @param message - The message to announce
   * @param priority - 'polite' (default) or 'assertive'
   */
  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (!liveRegionRef.current) return;

    // Clear previous message
    liveRegionRef.current.textContent = '';
    
    // Set new priority if different
    if (liveRegionRef.current.getAttribute('aria-live') !== priority) {
      liveRegionRef.current.setAttribute('aria-live', priority);
    }

    // Announce new message after a brief delay to ensure it's read
    setTimeout(() => {
      if (liveRegionRef.current) {
        liveRegionRef.current.textContent = message;
      }
    }, 100);
  }, []);

  /**
   * Announce loading state
   */
  const announceLoading = useCallback((message: string = 'Loading content') => {
    announce(message, 'polite');
  }, [announce]);

  /**
   * Announce error state
   */
  const announceError = useCallback((message: string = 'An error occurred') => {
    announce(message, 'assertive');
  }, [announce]);

  /**
   * Announce success state
   */
  const announceSuccess = useCallback((message: string) => {
    announce(message, 'polite');
  }, [announce]);

  /**
   * Announce navigation change
   */
  const announceNavigation = useCallback((pageName: string) => {
    announce(`Navigated to ${pageName}`, 'polite');
  }, [announce]);

  /**
   * Announce form validation errors
   */
  const announceFormErrors = useCallback((errors: string[]) => {
    const errorMessage = errors.length === 1 
      ? `Form error: ${errors[0]}`
      : `Form has ${errors.length} errors: ${errors.join(', ')}`;
    announce(errorMessage, 'assertive');
  }, [announce]);

  return {
    announce,
    announceLoading,
    announceError,
    announceSuccess,
    announceNavigation,
    announceFormErrors
  };
}

/**
 * Hook for managing ARIA attributes dynamically
 */
export function useAriaAttributes() {
  const setAriaLabel = useCallback((element: HTMLElement | null, label: string) => {
    if (element) {
      element.setAttribute('aria-label', label);
    }
  }, []);

  const setAriaDescribedBy = useCallback((element: HTMLElement | null, describedById: string) => {
    if (element) {
      element.setAttribute('aria-describedby', describedById);
    }
  }, []);

  const setAriaExpanded = useCallback((element: HTMLElement | null, expanded: boolean) => {
    if (element) {
      element.setAttribute('aria-expanded', expanded.toString());
    }
  }, []);

  const setAriaSelected = useCallback((element: HTMLElement | null, selected: boolean) => {
    if (element) {
      element.setAttribute('aria-selected', selected.toString());
    }
  }, []);

  const setAriaPressed = useCallback((element: HTMLElement | null, pressed: boolean) => {
    if (element) {
      element.setAttribute('aria-pressed', pressed.toString());
    }
  }, []);

  const setAriaHidden = useCallback((element: HTMLElement | null, hidden: boolean) => {
    if (element) {
      element.setAttribute('aria-hidden', hidden.toString());
    }
  }, []);

  const setAriaLive = useCallback((element: HTMLElement | null, live: 'off' | 'polite' | 'assertive') => {
    if (element) {
      element.setAttribute('aria-live', live);
    }
  }, []);

  return {
    setAriaLabel,
    setAriaDescribedBy,
    setAriaExpanded,
    setAriaSelected,
    setAriaPressed,
    setAriaHidden,
    setAriaLive
  };
}

/**
 * Hook for managing semantic landmarks
 */
export function useLandmarks() {
  const createLandmark = useCallback((
    element: HTMLElement | null, 
    role: string, 
    label?: string
  ) => {
    if (element) {
      element.setAttribute('role', role);
      if (label) {
        element.setAttribute('aria-label', label);
      }
    }
  }, []);

  const setMainLandmark = useCallback((element: HTMLElement | null, label?: string) => {
    createLandmark(element, 'main', label);
  }, [createLandmark]);

  const setNavigationLandmark = useCallback((element: HTMLElement | null, label?: string) => {
    createLandmark(element, 'navigation', label);
  }, [createLandmark]);

  const setBannerLandmark = useCallback((element: HTMLElement | null, label?: string) => {
    createLandmark(element, 'banner', label);
  }, [createLandmark]);

  const setContentInfoLandmark = useCallback((element: HTMLElement | null, label?: string) => {
    createLandmark(element, 'contentinfo', label);
  }, [createLandmark]);

  const setComplementaryLandmark = useCallback((element: HTMLElement | null, label?: string) => {
    createLandmark(element, 'complementary', label);
  }, [createLandmark]);

  const setSearchLandmark = useCallback((element: HTMLElement | null, label?: string) => {
    createLandmark(element, 'search', label);
  }, [createLandmark]);

  return {
    createLandmark,
    setMainLandmark,
    setNavigationLandmark,
    setBannerLandmark,
    setContentInfoLandmark,
    setComplementaryLandmark,
    setSearchLandmark
  };
}