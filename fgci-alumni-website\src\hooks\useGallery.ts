import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import type { GalleryImage, GalleryAlbum, PaginatedResponse } from '@/lib/types';

interface GalleryQueryParams {
  page?: number;
  limit?: number;
  album?: string;
  tag?: string;
  search?: string;
}

export function useGalleryImages(params: GalleryQueryParams = {}) {
  return useQuery({
    queryKey: ['gallery', 'images', params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });
      
      try {
        const response = await apiClient.getPaginated<GalleryImage>(`/gallery?${queryParams.toString()}`);
        return response;
      } catch (error) {
        console.error('Failed to fetch gallery images:', error);
        throw new Error('Failed to load gallery images. Please try again.');
      }
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - images don't change frequently
    gcTime: 60 * 60 * 1000, // 1 hour for better caching
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false, // Prevent unnecessary refetches
    refetchOnReconnect: false, // Don't refetch on reconnect
    networkMode: 'offlineFirst', // Better offline experience
  });
}

export function useGalleryAlbums(params: Omit<GalleryQueryParams, 'album' | 'tag'> = {}) {
  return useQuery({
    queryKey: ['gallery', 'albums', params],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });
      
      try {
        const response = await apiClient.getPaginated<GalleryAlbum>(`/gallery/albums?${queryParams.toString()}`);
        return response;
      } catch (error) {
        console.error('Failed to fetch gallery albums:', error);
        throw new Error('Failed to load gallery albums. Please try again.');
      }
    },
    staleTime: 15 * 60 * 1000, // 15 minutes (albums change less frequently)
    gcTime: 2 * 60 * 60 * 1000, // 2 hours
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    networkMode: 'offlineFirst',
  });
}

export function useGalleryAlbum(albumId: string) {
  return useQuery({
    queryKey: ['gallery', 'albums', albumId],
    queryFn: async () => {
      try {
        const response = await apiClient.get<GalleryAlbum>(`/gallery/albums/${albumId}`);
        return response.data;
      } catch (error) {
        console.error('Failed to fetch gallery album:', error);
        throw new Error('Failed to load gallery album. Please try again.');
      }
    },
    enabled: !!albumId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}