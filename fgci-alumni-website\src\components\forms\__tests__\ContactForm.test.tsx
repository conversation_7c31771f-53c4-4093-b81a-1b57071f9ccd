import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ContactForm } from '../ContactForm';
import { apiClient } from '@/lib/api-client';

// Mock the API client
jest.mock('@/lib/api-client', () => ({
  apiClient: {
    post: jest.fn(),
  },
}));

// Mock the MSW provider
jest.mock('@/components/providers/MSWProvider', () => ({
  MSWProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('ContactForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all form fields', () => {
    renderWithProviders(<ContactForm />);
    
    // Check for all required fields
    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/graduation year/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/inquiry type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/chapter affiliation/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/subject/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/message/i)).toBeInTheDocument();
    
    // Check for submit button
    expect(screen.getByRole('button', { name: /send message/i })).toBeInTheDocument();
  });

  it('shows validation errors for required fields', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactForm />);
    
    const submitButton = screen.getByRole('button', { name: /send message/i });
    await user.click(submitButton);
    
    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/subject is required/i)).toBeInTheDocument();
      expect(screen.getByText(/message is required/i)).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactForm />);
    
    const emailInput = screen.getByLabelText(/email address/i);
    await user.type(emailInput, 'invalid-email');
    await user.tab(); // Trigger blur
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates phone number format', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactForm />);
    
    const phoneInput = screen.getByLabelText(/phone number/i);
    await user.type(phoneInput, '123'); // Invalid phone
    await user.tab(); // Trigger blur
    
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument();
    });
  });

  it('validates graduation year range', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactForm />);
    
    const yearInput = screen.getByLabelText(/graduation year/i);
    await user.type(yearInput, '1950'); // Too early
    await user.tab(); // Trigger blur
    
    await waitFor(() => {
      expect(screen.getByText(/year must be between 1970/i)).toBeInTheDocument();
    });
  });

  it('validates message length', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactForm />);
    
    const messageInput = screen.getByLabelText(/message/i);
    await user.type(messageInput, 'short'); // Too short
    await user.tab(); // Trigger blur
    
    await waitFor(() => {
      expect(screen.getByText(/must be at least 10 characters/i)).toBeInTheDocument();
    });
  });

  it('shows character count for message field', () => {
    renderWithProviders(<ContactForm />);
    
    // Should show character count (0/2000 initially)
    expect(screen.getByText('0/2000')).toBeInTheDocument();
  });

  it('updates character count as user types', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactForm />);
    
    const messageInput = screen.getByLabelText(/message/i);
    await user.type(messageInput, 'Hello world');
    
    expect(screen.getByText('11/2000')).toBeInTheDocument();
  });

  it('clears errors when user starts typing', async () => {
    const user = userEvent.setup();
    renderWithProviders(<ContactForm />);
    
    // First trigger validation error
    const nameInput = screen.getByLabelText(/full name/i);
    await user.click(nameInput);
    await user.tab(); // Blur without typing
    
    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
    });
    
    // Then start typing to clear error
    await user.type(nameInput, 'John');
    
    await waitFor(() => {
      expect(screen.queryByText(/name is required/i)).not.toBeInTheDocument();
    });
  });

  it('has proper accessibility attributes', () => {
    renderWithProviders(<ContactForm />);
    
    // Check for proper labels
    const nameInput = screen.getByLabelText(/full name/i);
    expect(nameInput).toHaveAttribute('required');
    
    // Check for help text associations
    const phoneInput = screen.getByLabelText(/phone number/i);
    expect(phoneInput).toHaveAttribute('aria-describedby');
  });

  it('disables submit button while submitting', async () => {
    const user = userEvent.setup();
    
    // Mock successful API response with a short delay so the submitting state is observable
    apiClient.post.mockImplementation(async () => {
      await new Promise((r) => setTimeout(r, 30));
      return { success: true, data: { message: 'Success!' } };
    });

    renderWithProviders(<ContactForm />);
    
    // Fill out required fields
    await user.type(screen.getByLabelText(/full name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/subject/i), 'Test subject');
    await user.type(screen.getByLabelText(/message/i), 'This is a test message');
    await user.selectOptions(screen.getByLabelText(/inquiry type/i), 'general');
    
    const submitButton = screen.getByRole('button', { name: /send message/i });
    await user.click(submitButton);

    // Button should be disabled and show loading text
    await waitFor(() => expect(submitButton).toBeDisabled());
    expect(screen.getByText(/sending message/i)).toBeInTheDocument();
  });

  it('shows success message after successful submission', async () => {
    const user = userEvent.setup();
    
    // Mock successful API response
    apiClient.post.mockResolvedValue({
      success: true,
      data: { message: 'Thank you for your message!' }
    });

    renderWithProviders(<ContactForm />);
    
    // Fill out and submit form
    await user.type(screen.getByLabelText(/full name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/subject/i), 'Test subject');
    await user.type(screen.getByLabelText(/message/i), 'This is a test message');
    await user.selectOptions(screen.getByLabelText(/inquiry type/i), 'general');
    
    await user.click(screen.getByRole('button', { name: /send message/i }));
    
    await waitFor(() => {
      expect(screen.getByText(/message sent successfully/i)).toBeInTheDocument();
      expect(screen.getByText(/thank you for your message/i)).toBeInTheDocument();
    });
  });

  it('shows error message after failed submission', async () => {
    const user = userEvent.setup();
    
    // Mock API error
    apiClient.post.mockRejectedValue({
      message: 'Server error'
    });

    renderWithProviders(<ContactForm />);
    
    // Fill out and submit form
    await user.type(screen.getByLabelText(/full name/i), 'John Doe');
    await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/subject/i), 'Test subject');
    await user.type(screen.getByLabelText(/message/i), 'This is a test message');
    await user.selectOptions(screen.getByLabelText(/inquiry type/i), 'general');
    
    await user.click(screen.getByRole('button', { name: /send message/i }));
    
    await waitFor(() => {
      expect(screen.getByText(/error sending message/i)).toBeInTheDocument();
    });
  });
});