import { <PERSON>ada<PERSON> } from 'next';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui';
import { Breadcrumbs } from '@/components/seo';
import { 
  MagnifyingGlassIcon,
  GlobeAltIcon,
  DocumentTextIcon,
  TagIcon,
  LinkIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { 
  getPageSEOConfig,
  generateEventStructuredData,
  generatePersonStructuredData,
  SITE_CONFIG 
} from '@/lib/seo';

// Generate metadata for this page
export async function generateMetadata(): Promise<Metadata> {
  const seoConfig = getPageSEOConfig('/seo-test');
  
  return {
    title: 'SEO Testing | FGC Ikom Alumni Association',
    description: 'Test and validate SEO implementation including meta tags, structured data, and search engine optimization features.',
    keywords: ['SEO', 'testing', 'meta tags', 'structured data', 'search optimization'],
    openGraph: {
      title: 'SEO Testing | FGC Ikom Alumni Association',
      description: 'Test and validate SEO implementation including meta tags, structured data, and search engine optimization features.',
      url: `${SITE_CONFIG.url}/seo-test`,
      siteName: SITE_CONFIG.name,
      images: [
        {
          url: `${SITE_CONFIG.url}/images/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: 'SEO Testing',
        },
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: 'SEO Testing | FGC Ikom Alumni Association',
      description: 'Test and validate SEO implementation including meta tags, structured data, and search engine optimization features.',
      images: [`${SITE_CONFIG.url}/images/og-image.jpg`],
    },
    robots: {
      index: false,
      follow: false,
    },
  };
}

export default function SEOTestPage() {
  // Example structured data
  const eventData = generateEventStructuredData({
    name: 'FGC Ikom Alumni Reunion 2024',
    description: 'Annual reunion bringing together FGC Ikom alumni from around the world.',
    startDate: '2024-12-15T18:00:00Z',
    endDate: '2024-12-15T23:00:00Z',
    location: 'Lagos, Nigeria',
    organizer: 'FGC Ikom Alumni Association',
    image: '/images/reunion-2024.jpg'
  });

  const personData = generatePersonStructuredData({
    name: 'Dr. Amina Okafor',
    jobTitle: 'Medical Doctor',
    worksFor: 'Lagos University Teaching Hospital',
    alumniOf: 'Federal Government College Ikom',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
    email: '<EMAIL>'
  });

  const breadcrumbItems = [
    { name: 'Home', url: '/' },
    { name: 'Testing', url: '/testing' },
    { name: 'SEO Test', url: '/seo-test' }
  ];

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(eventData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(personData)
        }}
      />

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="container-custom py-12">
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
                SEO Testing & Validation
              </h1>
              <p className="body-large text-gray-600 mb-6">
                This page demonstrates and tests the SEO implementation including 
                meta tags, structured data, breadcrumbs, and search engine optimization features.
              </p>
              
              {/* Breadcrumbs */}
              <div className="flex justify-center mb-6">
                <Breadcrumbs items={breadcrumbItems} />
              </div>
            </div>
          </div>
        </div>

        <div className="container-custom py-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* SEO Features */}
            <div className="space-y-6">
              <Card variant="elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MagnifyingGlassIcon className="w-5 h-5" />
                    Meta Tags Implementation
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Title Tag</h3>
                      <p className="text-sm text-gray-600">
                        Dynamic page titles with site name
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Meta Description</h3>
                      <p className="text-sm text-gray-600">
                        Unique descriptions for each page
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Keywords</h3>
                      <p className="text-sm text-gray-600">
                        Relevant keywords for each page
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Canonical URLs</h3>
                      <p className="text-sm text-gray-600">
                        Prevent duplicate content issues
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card variant="elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <GlobeAltIcon className="w-5 h-5" />
                    Open Graph & Twitter Cards
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Open Graph Tags</h3>
                      <p className="text-sm text-gray-600">
                        Rich social media previews
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Twitter Cards</h3>
                      <p className="text-sm text-gray-600">
                        Optimized Twitter sharing
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Dynamic Images</h3>
                      <p className="text-sm text-gray-600">
                        Context-aware social images
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card variant="elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DocumentTextIcon className="w-5 h-5" />
                    Structured Data
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Organization Schema</h3>
                      <p className="text-sm text-gray-600">
                        Alumni association information
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Educational Organization</h3>
                      <p className="text-sm text-gray-600">
                        School and alumni data
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Event Schema</h3>
                      <p className="text-sm text-gray-600">
                        Alumni events and reunions
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Person Schema</h3>
                      <p className="text-sm text-gray-600">
                        Alumni profile information
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Breadcrumb Schema</h3>
                      <p className="text-sm text-gray-600">
                        Navigation structure data
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Technical SEO */}
            <div className="space-y-6">
              <Card variant="elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TagIcon className="w-5 h-5" />
                    Technical SEO
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Semantic HTML</h3>
                      <p className="text-sm text-gray-600">
                        Proper heading hierarchy and landmarks
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Mobile-First Design</h3>
                      <p className="text-sm text-gray-600">
                        Responsive and mobile-optimized
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Fast Loading</h3>
                      <p className="text-sm text-gray-600">
                        Optimized images and performance
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">HTTPS Security</h3>
                      <p className="text-sm text-gray-600">
                        Secure connection required
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card variant="elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <LinkIcon className="w-5 h-5" />
                    Site Architecture
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">XML Sitemap</h3>
                      <p className="text-sm text-gray-600">
                        <a href="/sitemap.xml" className="text-maroon-700 hover:underline">
                          /sitemap.xml
                        </a> - Auto-generated sitemap
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Robots.txt</h3>
                      <p className="text-sm text-gray-600">
                        <a href="/robots.txt" className="text-maroon-700 hover:underline">
                          /robots.txt
                        </a> - Crawler instructions
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">URL Structure</h3>
                      <p className="text-sm text-gray-600">
                        Clean, descriptive URLs
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                    <div>
                      <h3 className="font-medium text-gray-900">Internal Linking</h3>
                      <p className="text-sm text-gray-600">
                        Strategic internal link structure
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card variant="elevated">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ChartBarIcon className="w-5 h-5" />
                    SEO Testing Tools
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium text-gray-900 mb-2">Recommended Tools:</h3>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Google Search Console</li>
                      <li>• Google PageSpeed Insights</li>
                      <li>• Rich Results Test</li>
                      <li>• Mobile-Friendly Test</li>
                      <li>• Lighthouse SEO Audit</li>
                      <li>• Schema Markup Validator</li>
                    </ul>
                  </div>

                  <div className="pt-4 border-t">
                    <p className="text-sm text-gray-600">
                      Use browser dev tools to inspect meta tags and structured data 
                      implementation on this page.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
