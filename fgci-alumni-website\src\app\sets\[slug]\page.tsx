'use client';

import { use } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSet, useSetActivities } from '../../../hooks/useSets';
import { Timeline } from '../../../components/ui/Timeline';
import { SetGallery } from '../../../components/sets/SetGallery';
import { DocumentsSection } from '../../../components/sets/DocumentsSection';
import { Avatar } from '../../../components/ui/Avatar';
import { Button } from '../../../components/ui/Button';
import { Card } from '../../../components/ui/Card';
import { LoadingSpinner, ErrorState } from '../../../components/ui/LoadingStates';
import { 
  ArrowLeft, 
  Users, 
  Calendar, 
  MapPin, 
  Phone, 
  Mail, 
  ExternalLink,
  Crown,
  Star,
  Activity
} from 'lucide-react';

interface SetDetailPageProps {
  params: Promise<{ slug: string }>;
}

export default function SetDetailPage({ params }: SetDetailPageProps) {
  const { slug } = use(params);
  
  const { data: set, isLoading: setLoading, error: setError } = useSet(slug);
  const { data: activitiesData, isLoading: activitiesLoading } = useSetActivities(slug);

  if (setLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (setError || !set?.data) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <ErrorState
            title="Set not found"
            message="The graduation set you're looking for doesn't exist or has been removed."
            actionLabel="Back to Sets"
            onAction={() => window.history.back()}
          />
        </div>
      </div>
    );
  }

  const setData = set.data;
  const activities = activitiesData?.data || setData.activities || [];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative">
        {/* Hero Image */}
        {setData.heroImage && (
          <div className="relative h-96 md:h-[500px]">
            <Image
              src={setData.heroImage}
              alt={`${setData.name} hero image`}
              fill
              className="object-cover"
              priority
              sizes="100vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          </div>
        )}

        {/* Hero Content */}
        <div className={`${setData.heroImage ? 'absolute inset-0' : 'bg-gradient-to-r from-maroon to-maroon-800'} flex items-end`}>
          <div className="container mx-auto px-4 pb-8">
            <div className="max-w-4xl">
              {/* Back Button */}
              <div className="mb-6">
                <Link href="/sets">
                  <Button variant="ghost" size="sm" className="text-white hover:bg-white/10">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Sets
                  </Button>
                </Link>
              </div>

              {/* Set Info */}
              <div className="text-white">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="bg-gold px-4 py-2 rounded-full">
                    <span className="text-maroon font-bold text-lg">{setData.year}</span>
                  </div>
                  <div className="bg-white/20 px-3 py-1 rounded-full">
                    <span className="text-sm font-medium">Graduation Set</span>
                  </div>
                </div>
                
                <h1 className="text-4xl md:text-5xl font-bold mb-4">
                  {setData.name}
                </h1>
                
                {setData.motto && (
                  <p className="text-xl md:text-2xl text-gold font-medium italic mb-6">
                    "{setData.motto}"
                  </p>
                )}
                
                {setData.description && (
                  <p className="text-lg text-gray-100 max-w-2xl mb-6">
                    {setData.description}
                  </p>
                )}

                {/* Quick Stats */}
                <div className="flex flex-wrap gap-6 text-sm">
                  {setData.memberCount && (
                    <div className="flex items-center space-x-2">
                      <Users className="w-5 h-5" />
                      <span>{setData.memberCount} Members</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2">
                    <Calendar className="w-5 h-5" />
                    <span>Graduated {setData.year}</span>
                  </div>
                  {activities.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <Activity className="w-5 h-5" />
                      <span>{activities.length} Activities</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Executive Committee */}
              <Card variant="default" padding="lg">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="bg-maroon/10 p-2 rounded-lg">
                    <Crown className="w-5 h-5 text-maroon" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">
                      Executive Committee
                    </h2>
                    <p className="text-gray-600">
                      Leadership team for {setData.name}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {setData.exco.map((member) => (
                    <div
                      key={member.id}
                      className="flex items-start space-x-4 p-4 rounded-lg border border-gray-200 hover:border-maroon/20 hover:bg-maroon/5 transition-colors"
                    >
                      <Avatar
                        src={member.photo}
                        alt={member.name}
                        size="lg"
                        fallback={member.name}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-semibold text-gray-900">
                            {member.name}
                          </h3>
                          {member.role === 'Set Leader' && (
                            <Star className="w-4 h-4 text-gold" />
                          )}
                        </div>
                        <p className="text-sm text-maroon font-medium mb-2">
                          {member.role}
                        </p>
                        {member.currentLocation && (
                          <div className="flex items-center text-sm text-gray-500 mb-2">
                            <MapPin className="w-3 h-3 mr-1" />
                            {member.currentLocation}
                          </div>
                        )}
                        {member.bio && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {member.bio}
                          </p>
                        )}
                        <div className="flex space-x-2">
                          {member.phone && (
                            <a
                              href={`tel:${member.phone}`}
                              className="inline-flex items-center text-xs text-maroon hover:text-maroon-700"
                            >
                              <Phone className="w-3 h-3 mr-1" />
                              Call
                            </a>
                          )}
                          {member.email && (
                            <a
                              href={`mailto:${member.email}`}
                              className="inline-flex items-center text-xs text-maroon hover:text-maroon-700"
                            >
                              <Mail className="w-3 h-3 mr-1" />
                              Email
                            </a>
                          )}
                          {member.socialLinks?.linkedin && (
                            <a
                              href={member.socialLinks.linkedin}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-xs text-maroon hover:text-maroon-700"
                            >
                              <ExternalLink className="w-3 h-3 mr-1" />
                              LinkedIn
                            </a>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* Activities Timeline */}
              <Card variant="default" padding="lg">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="bg-maroon/10 p-2 rounded-lg">
                    <Activity className="w-5 h-5 text-maroon" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">
                      Recent Activities
                    </h2>
                    <p className="text-gray-600">
                      Latest events and activities from {setData.name}
                    </p>
                  </div>
                </div>

                {activitiesLoading ? (
                  <div className="flex justify-center py-8">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <Timeline
                    activities={activities}
                    variant="vertical"
                    showDates={true}
                    interactive={false}
                  />
                )}
              </Card>

              {/* Gallery */}
              <Card variant="default" padding="lg">
                <SetGallery set={setData} />
              </Card>

              {/* Documents */}
              {setData.documents && setData.documents.length > 0 && (
                <Card variant="default" padding="lg">
                  <DocumentsSection documents={setData.documents} />
                </Card>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Set Leader Highlight */}
              {setData.leader && (
                <Card variant="elevated" padding="md">
                  <div className="text-center">
                    <div className="flex justify-center mb-4">
                      <div className="relative">
                        <Avatar
                          src={setData.leader.photo}
                          alt={setData.leader.name}
                          size="xl"
                          fallback={setData.leader.name}
                        />
                        <div className="absolute -top-1 -right-1 bg-gold p-1 rounded-full">
                          <Crown className="w-4 h-4 text-maroon" />
                        </div>
                      </div>
                    </div>
                    <h3 className="font-bold text-gray-900 mb-1">
                      {setData.leader.name}
                    </h3>
                    <p className="text-sm text-maroon font-medium mb-2">
                      Set Leader
                    </p>
                    {setData.leader.currentLocation && (
                      <div className="flex items-center justify-center text-sm text-gray-500 mb-3">
                        <MapPin className="w-3 h-3 mr-1" />
                        {setData.leader.currentLocation}
                      </div>
                    )}
                    {setData.leader.bio && (
                      <p className="text-sm text-gray-600 mb-4">
                        {setData.leader.bio}
                      </p>
                    )}
                    <div className="flex justify-center space-x-2">
                      {setData.leader.phone && (
                        <Button variant="outline" size="sm" asChild>
                          <a href={`tel:${setData.leader.phone}`}>
                            <Phone className="w-4 h-4 mr-1" />
                            Call
                          </a>
                        </Button>
                      )}
                      {setData.leader.email && (
                        <Button variant="outline" size="sm" asChild>
                          <a href={`mailto:${setData.leader.email}`}>
                            <Mail className="w-4 h-4 mr-1" />
                            Email
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              )}

              {/* Quick Stats */}
              <Card variant="default" padding="md">
                <h3 className="font-semibold text-gray-900 mb-4">Set Statistics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Graduation Year</span>
                    <span className="font-medium">{setData.year}</span>
                  </div>
                  {setData.memberCount && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Members</span>
                      <span className="font-medium">{setData.memberCount}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Executive Members</span>
                    <span className="font-medium">{setData.exco.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Recent Activities</span>
                    <span className="font-medium">{activities.length}</span>
                  </div>
                  {setData.gallery && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Gallery Photos</span>
                      <span className="font-medium">{setData.gallery.length}</span>
                    </div>
                  )}
                  {setData.documents && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Documents</span>
                      <span className="font-medium">{setData.documents.length}</span>
                    </div>
                  )}
                </div>
              </Card>

              {/* Navigation */}
              <Card variant="default" padding="md">
                <h3 className="font-semibold text-gray-900 mb-4">Explore More</h3>
                <div className="space-y-2">
                  <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                    <Link href="/sets">
                      <Users className="w-4 h-4 mr-2" />
                      All Graduation Sets
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                    <Link href="/chapters">
                      <MapPin className="w-4 h-4 mr-2" />
                      Alumni Chapters
                    </Link>
                  </Button>
                  <Button variant="outline" size="sm" className="w-full justify-start" asChild>
                    <Link href="/events">
                      <Calendar className="w-4 h-4 mr-2" />
                      Upcoming Events
                    </Link>
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}