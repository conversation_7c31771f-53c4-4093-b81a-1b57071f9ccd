'use client';

import { useState } from 'react';
import { MemorialGrid } from '@/components/memorial/MemorialGrid';
import { MemorialHero } from '@/components/memorial/MemorialHero';
import { MemorialFilters } from '@/components/memorial/MemorialFilters';

interface MemorialFilters {
  graduationYear?: string;
  chapter?: string;
  searchQuery?: string;
}

export default function RollOfHonourPage() {
  const [filters, setFilters] = useState<MemorialFilters>({});

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <MemorialHero />
      
      {/* Main Content */}
      <main className="container mx-auto px-4 py-12">
        <div className="max-w-7xl mx-auto">
          {/* Filters */}
          <div className="mb-8">
            <MemorialFilters onFiltersChange={setFilters} />
          </div>
          
          {/* Memorial Grid */}
          <MemorialGrid filters={filters} />
        </div>
      </main>
    </div>
  );
}