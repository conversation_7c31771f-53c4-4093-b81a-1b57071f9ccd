# Mock Service Worker (MSW) Setup

This directory contains the Mock Service Worker (MSW) configuration for the FGC Ikom Alumni Website. MSW allows us to intercept API requests and return mock data during development and testing.

## Directory Structure

```
src/mocks/
├── data/                 # Mock data files
│   ├── alumni.ts        # Alumni directory data
│   ├── chapters.ts      # Chapters data
│   ├── events.ts        # Events data
│   ├── gallery.ts       # Gallery images and albums
│   └── sets.ts          # Sets (graduation classes) data
├── __tests__/           # MSW handler tests
│   └── handlers.test.ts # Comprehensive API tests
├── browser.ts           # Browser MSW setup
├── handlers.ts          # API request handlers
├── server.ts            # Node.js MSW setup (for testing)
├── index.ts             # Main exports
└── README.md            # This file
```

## Usage

### Development Mode

To enable mocking in development, use the `dev:mock` script:

```bash
npm run dev:mock
```

This sets the `NEXT_PUBLIC_USE_MOCKS=true` environment variable and starts the development server with API mocking enabled.

### Regular Development

For regular development without mocking (using real APIs):

```bash
npm run dev
```

### Testing

MSW is automatically configured for testing environments. The mock server is set up in `server.ts` and can be imported in test files:

```typescript
import { setupMockServer } from '@/mocks/server';

// In your test setup
setupMockServer();
```

## API Endpoints

The following API endpoints are mocked:

### Chapters
- `GET /api/chapters` - List chapters with pagination and filtering
- `GET /api/chapters/:slug` - Get specific chapter details
- `GET /api/chapters/:slug/activities` - Get chapter activities

### Sets
- `GET /api/sets` - List graduation sets with pagination and filtering
- `GET /api/sets/:slug` - Get specific set details
- `GET /api/sets/:slug/activities` - Get set activities

### Events
- `GET /api/events` - List events with pagination and filtering
- `GET /api/events/:slug` - Get specific event details

### Gallery
- `GET /api/gallery` - List gallery images with pagination and filtering
- `GET /api/gallery/albums` - List gallery albums
- `GET /api/gallery/albums/:id` - Get specific album details

### ExcoBot (Alumni Directory)
- `GET /api/excobot/search` - Search alumni directory with advanced filtering

### Contact
- `POST /api/contact` - Submit contact form

### Health Check
- `GET /api/health` - API health status

## Query Parameters

Most endpoints support the following query parameters:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10-20 depending on endpoint)
- `search` - Search query string
- Additional filters specific to each endpoint

### Example Requests

```typescript
// Get first page of chapters
fetch('/api/chapters?page=1&limit=10')

// Search chapters by name
fetch('/api/chapters?search=Lagos')

// Filter chapters by type
fetch('/api/chapters?type=diaspora')

// Search alumni directory
fetch('/api/excobot/search?query=engineer&chapter=Lagos&graduationYear=2015')

// Get gallery images from specific album
fetch('/api/gallery?album=homecoming-2023')
```

## Response Format

All API responses follow a consistent format:

```typescript
interface ApiResponse<T> {
  data: T;
  success: boolean;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasNext?: boolean;
    timestamp?: string;
  };
  error?: string;
}
```

### Paginated Responses

```typescript
interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrevious: boolean;
    totalPages: number;
    timestamp: string;
  };
}
```

### Search Responses

```typescript
interface SearchResponse<T> extends PaginatedResponse<T> {
  meta: PaginatedResponse<T>['meta'] & {
    query?: string;
    filters?: Record<string, unknown>;
    searchTime?: number;
  };
}
```

## Mock Data

### Chapters
- 4 chapters: Lagos, Abuja, UK Diaspora, USA Diaspora
- Each chapter includes executive committee, activities, and metadata
- Supports filtering by type (regional, diaspora, school-based)

### Sets
- 5 graduation sets: Classes of 2011-2015
- Each set includes leaders, activities, gallery, and documents
- Sorted by graduation year (descending)

### Events
- 10 events with various statuses (upcoming, completed, cancelled)
- Includes homecoming, conferences, sports festivals, outreach programs
- Supports filtering by status and organizer

### Gallery
- 15+ images across 7 albums
- Images include metadata: photographer, date, tags, dimensions
- Albums include homecoming, chapter events, sports, medical outreach

### Alumni Directory
- 25+ alumni across different chapters and graduation years
- Includes contact information, roles, locations, and social links
- Supports advanced search and filtering

## Adding New Mock Data

To add new mock data:

1. **Add data to appropriate file** in `data/` directory
2. **Update handlers** in `handlers.ts` if new endpoints are needed
3. **Add tests** in `__tests__/handlers.test.ts`
4. **Update types** in `../lib/types.ts` if new interfaces are needed

### Example: Adding a New Chapter

```typescript
// In data/chapters.ts
export const mockChapters: Chapter[] = [
  // ... existing chapters
  {
    id: 'new-chapter',
    slug: 'new-chapter',
    name: 'New Chapter',
    region: 'New Region',
    type: 'regional',
    // ... other properties
  }
];
```

## Environment Variables

- `NEXT_PUBLIC_USE_MOCKS` - Set to `'true'` to enable mocking in browser
- `NODE_ENV` - MSW only runs in development mode

## Troubleshooting

### MSW Worker Not Starting

1. Ensure `mockServiceWorker.js` exists in `public/` directory
2. Run `npx msw init public/ --save` to regenerate the worker
3. Check browser console for MSW initialization messages

### API Requests Not Being Intercepted

1. Verify `NEXT_PUBLIC_USE_MOCKS=true` is set
2. Check that requests match the handler patterns exactly
3. Look for MSW console messages indicating successful interception

### Tests Failing

1. Ensure `setupMockServer()` is called in test setup
2. Check that test requests match the expected API format
3. Verify mock data exists for the test scenarios

## Performance Considerations

- Mock responses include realistic delays (100-500ms) to simulate network latency
- Large datasets are properly paginated
- Search operations include simulated search time metadata
- Images and heavy resources are referenced by URL only

## Security Notes

- Mock data contains no real personal information
- All contact details are fictional
- Email addresses use example domains
- Phone numbers follow standard formats but are not real

## Integration with Real APIs

When ready to integrate with real APIs:

1. Set `NEXT_PUBLIC_USE_MOCKS=false` or remove the environment variable
2. Update API client configuration in `src/lib/api-client.ts`
3. Ensure real API endpoints match the mock API contract
4. Update environment variables for production API URLs

The mock API contract serves as a specification for the real API implementation.