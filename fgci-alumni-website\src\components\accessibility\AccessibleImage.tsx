'use client';

import React, { useState } from 'react';
import Image, { ImageProps } from 'next/image';
import { cn } from '@/lib/utils';

interface AccessibleImageProps extends Omit<ImageProps, 'alt'> {
  alt: string; // Make alt required
  caption?: string;
  photographer?: string;
  longDescription?: string;
  decorative?: boolean;
  className?: string;
}

/**
 * Accessible image component with required alt text and enhanced screen reader support
 * Enforces accessibility best practices for images
 */
export function AccessibleImage({
  alt,
  caption,
  photographer,
  longDescription,
  decorative = false,
  className,
  onLoad,
  onError,
  ...props
}: AccessibleImageProps) {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleLoad = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setIsLoading(false);
    onLoad?.(event);
  };

  const handleError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    setHasError(true);
    setIsLoading(false);
    onError?.(event);
  };

  // For decorative images, use empty alt text
  const altText = decorative ? '' : alt;
  
  // Generate describedby ID if we have additional description
  const describedById = longDescription ? `${props.src}-description` : undefined;

  if (hasError) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center bg-gray-200 text-gray-500 rounded',
          className
        )}
        role="img"
        aria-label={decorative ? undefined : `Failed to load image: ${alt}`}
      >
        <div className="text-center p-4">
          <svg 
            className="w-12 h-12 mx-auto mb-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
            />
          </svg>
          <p className="text-sm">Image failed to load</p>
          {!decorative && (
            <p className="text-xs mt-1 text-gray-400">
              Alt text: {alt}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <figure className={cn('relative', className)}>
      {/* Loading state */}
      {isLoading && (
        <div 
          className="absolute inset-0 flex items-center justify-center bg-gray-200 animate-pulse rounded"
          aria-hidden="true"
        >
          <div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
        </div>
      )}

      {/* Main image */}
      <Image
        {...props}
        alt={altText}
        onLoad={handleLoad}
        onError={handleError}
        aria-describedby={describedById}
        className={cn(
          'transition-opacity duration-300',
          isLoading ? 'opacity-0' : 'opacity-100'
        )}
      />

      {/* Long description (hidden but accessible to screen readers) */}
      {longDescription && (
        <div id={describedById} className="sr-only">
          {longDescription}
        </div>
      )}

      {/* Caption and photographer info */}
      {(caption || photographer) && (
        <figcaption className="mt-2 text-sm text-gray-600">
          {caption && (
            <div className="mb-1">{caption}</div>
          )}
          {photographer && (
            <div className="text-xs text-gray-500">
              Photo by {photographer}
            </div>
          )}
        </figcaption>
      )}
    </figure>
  );
}

/**
 * Avatar image component with accessibility enhancements
 */
interface AccessibleAvatarProps {
  src?: string;
  alt: string;
  name: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function AccessibleAvatar({
  src,
  alt,
  name,
  size = 'md',
  className
}: AccessibleAvatarProps) {
  const [hasError, setHasError] = useState(false);

  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-12 h-12 text-sm',
    lg: 'w-16 h-16 text-base',
    xl: 'w-24 h-24 text-lg'
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (!src || hasError) {
    return (
      <div
        className={cn(
          'flex items-center justify-center rounded-full bg-maroon-100 text-maroon-700 font-medium',
          sizeClasses[size],
          className
        )}
        role="img"
        aria-label={alt}
      >
        {getInitials(name)}
      </div>
    );
  }

  return (
    <div className={cn('relative', sizeClasses[size], className)}>
      <Image
        src={src}
        alt={alt}
        fill
        className="rounded-full object-cover"
        onError={() => setHasError(true)}
        sizes="(max-width: 768px) 100px, 200px"
      />
    </div>
  );
}

/**
 * Gallery image component with enhanced accessibility
 */
interface AccessibleGalleryImageProps {
  src: string;
  alt: string;
  caption?: string;
  photographer?: string;
  onClick?: () => void;
  className?: string;
}

export function AccessibleGalleryImage({
  src,
  alt,
  caption,
  photographer,
  onClick,
  className
}: AccessibleGalleryImageProps) {
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if ((event.key === 'Enter' || event.key === ' ') && onClick) {
      event.preventDefault();
      onClick();
    }
  };

  const ariaLabel = `${alt}${caption ? `. ${caption}` : ''}${photographer ? `. Photo by ${photographer}` : ''}. Click to view full size.`;

  return (
    <div
      className={cn(
        'relative group cursor-pointer overflow-hidden rounded-lg',
        'focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2',
        className
      )}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={onClick ? ariaLabel : alt}
    >
      <AccessibleImage
        src={src}
        alt={alt}
        caption={caption}
        photographer={photographer}
        fill
        className="object-cover transition-transform duration-300 group-hover:scale-105"
        sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
      />
      
      {/* Overlay for better contrast */}
      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
      
      {/* Screen reader instruction */}
      <span className="sr-only">
        Press Enter or Space to open image in lightbox
      </span>
    </div>
  );
}