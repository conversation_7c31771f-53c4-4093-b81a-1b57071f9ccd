'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './Card';
import { Button } from './Button';
import { 
  ExclamationTriangleIcon, 
  InformationCircleIcon, 
  MegaphoneIcon,
  CalendarDaysIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

export interface Announcement {
  id: string;
  title: string;
  content: string;
  priority: 'high' | 'medium' | 'low';
  type?: 'general' | 'event' | 'urgent' | 'update';
  startDate: string;
  endDate?: string;
  link?: string;
  linkText?: string;
  action?: () => void;
}

export interface AnnouncementsProps {
  announcements: Announcement[];
  maxItems?: number;
  showAll?: boolean;
  className?: string;
}

export function Announcements({
  announcements,
  maxItems = 3,
  showAll = false,
  className
}: AnnouncementsProps) {
  // Filter active announcements
  const now = new Date();
  const activeAnnouncements = announcements.filter(announcement => {
    const startDate = new Date(announcement.startDate);
    const endDate = announcement.endDate ? new Date(announcement.endDate) : null;
    
    return startDate <= now && (!endDate || endDate >= now);
  });

  // Sort by priority and date
  const sortedAnnouncements = activeAnnouncements.sort((a, b) => {
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    
    if (priorityDiff !== 0) return priorityDiff;
    
    return new Date(b.startDate).getTime() - new Date(a.startDate).getTime();
  });

  // Limit items if not showing all
  const displayedAnnouncements = showAll 
    ? sortedAnnouncements 
    : sortedAnnouncements.slice(0, maxItems);

  const getPriorityClasses = (priority: string) => {
    switch (priority) {
      case 'high':
        return {
          border: 'border-l-4 border-l-red-500',
          bg: 'bg-red-50',
          icon: 'text-red-600',
          title: 'text-red-900'
        };
      case 'medium':
        return {
          border: 'border-l-4 border-l-yellow-500',
          bg: 'bg-yellow-50',
          icon: 'text-yellow-600',
          title: 'text-yellow-900'
        };
      case 'low':
        return {
          border: 'border-l-4 border-l-blue-500',
          bg: 'bg-blue-50',
          icon: 'text-blue-600',
          title: 'text-blue-900'
        };
      default:
        return {
          border: 'border-l-4 border-l-gray-500',
          bg: 'bg-gray-50',
          icon: 'text-gray-600',
          title: 'text-gray-900'
        };
    }
  };

  const getTypeIcon = (type?: string, priority?: string) => {
    const iconClass = "w-5 h-5";
    
    switch (type) {
      case 'urgent':
        return <ExclamationTriangleIcon className={iconClass} />;
      case 'event':
        return <CalendarDaysIcon className={iconClass} />;
      case 'update':
        return <InformationCircleIcon className={iconClass} />;
      default:
        return <MegaphoneIcon className={iconClass} />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (!displayedAnnouncements.length) {
    return (
      <div className={cn('w-full', className)}>
        <Card variant="outlined" className="text-center py-8">
          <CardContent>
            <MegaphoneIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="heading-3 text-lg text-gray-600 mb-2">No Active Announcements</h3>
            <p className="body-base text-gray-500">
              Check back later for important updates and announcements.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn('w-full space-y-4', className)}>
      {displayedAnnouncements.map((announcement) => {
        const priorityClasses = getPriorityClasses(announcement.priority);
        
        return (
          <Card
            key={announcement.id}
            variant="outlined"
            className={cn(
              'transition-all duration-200 hover:shadow-md',
              priorityClasses.border,
              priorityClasses.bg
            )}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className={cn('flex-shrink-0', priorityClasses.icon)}>
                    {getTypeIcon(announcement.type, announcement.priority)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <CardTitle className={cn('text-lg', priorityClasses.title)}>
                      {announcement.title}
                    </CardTitle>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-500">
                        {formatDate(announcement.startDate)}
                      </span>
                      {announcement.endDate && (
                        <>
                          <span className="text-xs text-gray-400">•</span>
                          <span className="text-xs text-gray-500">
                            Until {formatDate(announcement.endDate)}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                
                {announcement.priority === 'high' && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Urgent
                  </span>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <p className="body-base text-gray-700 mb-4 leading-relaxed">
                {announcement.content}
              </p>
              
              {(announcement.link || announcement.action) && announcement.linkText && (
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn('p-0 h-auto font-medium', priorityClasses.title)}
                  icon={<ArrowRightIcon className="w-4 h-4" />}
                  onClick={announcement.action}
                >
                  {announcement.linkText}
                </Button>
              )}
            </CardContent>
          </Card>
        );
      })}
      
      {!showAll && sortedAnnouncements.length > maxItems && (
        <div className="text-center pt-4">
          <Button variant="outline" size="sm">
            View All Announcements ({sortedAnnouncements.length - maxItems} more)
          </Button>
        </div>
      )}
    </div>
  );
}

// Helper function to create sample announcements
export const createSampleAnnouncements = (): Announcement[] => [
  {
    id: 'ann-1',
    title: 'Annual Homecoming 2024 Registration Open',
    content: 'Registration is now open for our biggest alumni gathering of the year! Join us December 14-15, 2024, at FGC Ikom Campus for a weekend of networking, celebration, and giving back.',
    priority: 'high',
    type: 'event',
    startDate: '2024-09-01',
    endDate: '2024-12-10',
    linkText: 'Register Now',
    link: '/events/annual-homecoming-2024'
  },
  {
    id: 'ann-2',
    title: 'New Chapter Formation in Canada',
    content: 'We are excited to announce the formation of a new alumni chapter in Canada. Alumni residing in Canada are invited to join our inaugural meeting.',
    priority: 'medium',
    type: 'update',
    startDate: '2024-08-15',
    endDate: '2024-10-31',
    linkText: 'Learn More',
    link: '/chapters/canada'
  },
  {
    id: 'ann-3',
    title: 'Scholarship Fund Milestone Reached',
    content: 'Thanks to your generous contributions, we have reached our goal of ₦50 million for the FGC Ikom Scholarship Fund. This will support 100 indigent students this academic year.',
    priority: 'low',
    type: 'general',
    startDate: '2024-08-01',
    linkText: 'View Impact Report',
    link: '/scholarship-impact'
  }
];
