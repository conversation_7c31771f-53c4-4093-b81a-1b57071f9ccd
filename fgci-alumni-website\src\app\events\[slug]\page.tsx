'use client';

import { useState } from 'react';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { 
  CalendarDaysIcon, 
  MapPinIcon,
  ClockIcon,
  TagIcon,
  LinkIcon,
  ArrowLeftIcon,
  ShareIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { <PERSON><PERSON>, Card, CardContent } from '@/components/ui';
import { LoadingStates } from '@/components/ui/LoadingStates';
import { useEvent } from '@/hooks/useEvents';
import { cn } from '@/lib/utils';

interface EventDetailPageProps {
  params: {
    slug: string;
  };
}

export default function EventDetailPage({ params }: EventDetailPageProps) {
  const { data: event, isLoading, error } = useEvent(params.slug);
  const [showGallery, setShowGallery] = useState(false);

  if (isLoading) {
    return <LoadingStates.Page />;
  }

  if (error || !event) {
    notFound();
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'ongoing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEventStatusLabel = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'Upcoming Event';
      case 'ongoing':
        return 'Event in Progress';
      case 'completed':
        return 'Event Completed';
      case 'cancelled':
        return 'Event Cancelled';
      default:
        return status;
    }
  };

  const isRegistrationAvailable = () => {
    return event.status === 'upcoming' && event.registrationUrl;
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: event.title,
          text: event.description,
          url: window.location.href,
        });
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Back Navigation */}
      <div className="bg-white border-b">
        <div className="container-custom py-4">
          <Link 
            href="/events"
            className="inline-flex items-center text-sm text-gray-600 hover:text-maroon-700 transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Events
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative">
        {event.heroImage && (
          <div className="relative h-96 lg:h-[500px]">
            <Image
              src={event.heroImage}
              alt={event.title}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-black/40" />
          </div>
        )}
        
        <div className="absolute inset-0 flex items-end">
          <div className="container-custom pb-12">
            <div className="max-w-4xl">
              <div className="mb-4">
                <span className={cn(
                  'inline-flex px-3 py-1 text-sm font-medium rounded-full border',
                  getEventStatusColor(event.status)
                )}>
                  {getEventStatusLabel(event.status)}
                </span>
              </div>
              <h1 className="heading-1 text-4xl lg:text-5xl text-white mb-4">
                {event.title}
              </h1>
              <div className="flex flex-wrap items-center gap-6 text-white/90">
                <div className="flex items-center">
                  <CalendarDaysIcon className="w-5 h-5 mr-2" />
                  <span>{formatDate(event.date)}</span>
                </div>
                <div className="flex items-center">
                  <ClockIcon className="w-5 h-5 mr-2" />
                  <span>{formatTime(event.date)}</span>
                </div>
                {event.location && (
                  <div className="flex items-center">
                    <MapPinIcon className="w-5 h-5 mr-2" />
                    <span>{event.location}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="container-custom py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Description */}
            <Card variant="elevated" padding="lg">
              <h2 className="heading-2 text-2xl mb-4">About This Event</h2>
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed">
                  {event.description}
                </p>
              </div>
            </Card>

            {/* Event Gallery */}
            {event.gallery && event.gallery.length > 0 && (
              <Card variant="elevated" padding="lg">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="heading-2 text-2xl">Event Gallery</h2>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowGallery(true)}
                  >
                    <PhotoIcon className="w-4 h-4 mr-2" />
                    View All ({event.gallery.length})
                  </Button>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {event.gallery.slice(0, 6).map((image, index) => (
                    <div
                      key={index}
                      className="relative aspect-square rounded-lg overflow-hidden cursor-pointer group"
                      onClick={() => setShowGallery(true)}
                    >
                      <Image
                        src={image}
                        alt={`Event photo ${index + 1}`}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      {index === 5 && event.gallery.length > 6 && (
                        <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                          <span className="text-white font-semibold">
                            +{event.gallery.length - 6} more
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Registration Card */}
            {isRegistrationAvailable() && (
              <Card variant="elevated" padding="lg">
                <h3 className="heading-3 text-xl mb-4">Register for Event</h3>
                <p className="text-gray-600 mb-4">
                  Secure your spot at this exciting event. Registration is required.
                </p>
                <a
                  href={event.registrationUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block"
                >
                  <Button variant="primary" className="w-full">
                    <LinkIcon className="w-4 h-4 mr-2" />
                    Register Now
                  </Button>
                </a>
              </Card>
            )}

            {/* Event Details */}
            <Card variant="elevated" padding="lg">
              <h3 className="heading-3 text-xl mb-4">Event Details</h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CalendarDaysIcon className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                  <div>
                    <p className="font-medium text-gray-900">Date & Time</p>
                    <p className="text-gray-600">{formatDate(event.date)}</p>
                    <p className="text-gray-600">{formatTime(event.date)}</p>
                    {event.endDate && (
                      <p className="text-gray-600 text-sm">
                        Ends: {formatDate(event.endDate)} at {formatTime(event.endDate)}
                      </p>
                    )}
                  </div>
                </div>

                {event.location && (
                  <div className="flex items-start">
                    <MapPinIcon className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">Location</p>
                      <p className="text-gray-600">{event.location}</p>
                    </div>
                  </div>
                )}

                {event.organizer && (
                  <div className="flex items-start">
                    <TagIcon className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="font-medium text-gray-900">Organizer</p>
                      <p className="text-gray-600">{event.organizer}</p>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Share Event */}
            <Card variant="elevated" padding="lg">
              <h3 className="heading-3 text-xl mb-4">Share Event</h3>
              <Button
                variant="outline"
                onClick={handleShare}
                className="w-full"
              >
                <ShareIcon className="w-4 h-4 mr-2" />
                Share Event
              </Button>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}