'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle, Button } from '@/components/ui';
import { FocusTrap, ScreenReaderOnly } from '@/components/accessibility';
import { 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

export default function AccessibilityTestPage() {
  const [showModal, setShowModal] = useState(false);
  const [testResults, setTestResults] = useState<{
    skipToContent: boolean;
    keyboardNavigation: boolean;
    screenReader: boolean;
    colorContrast: boolean;
    focusManagement: boolean;
  }>({
    skipToContent: false,
    keyboardNavigation: false,
    screenReader: false,
    colorContrast: false,
    focusManagement: false
  });

  const runAccessibilityTests = () => {
    // Simulate running accessibility tests
    setTestResults({
      skipToContent: true,
      keyboardNavigation: true,
      screenReader: true,
      colorContrast: true,
      focusManagement: true
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container-custom py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
              Accessibility Testing
            </h1>
            <p className="body-large text-gray-600">
              This page demonstrates and tests the accessibility features implemented 
              throughout the FGC Ikom Alumni website.
            </p>
          </div>
        </div>
      </div>

      <div className="container-custom py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Accessibility Features */}
          <div className="space-y-6">
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Implemented Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900">Skip to Content</h3>
                    <p className="text-sm text-gray-600">
                      Press Tab when the page loads to see the skip link.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900">Keyboard Navigation</h3>
                    <p className="text-sm text-gray-600">
                      All interactive elements are accessible via keyboard.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900">Screen Reader Support</h3>
                    <p className="text-sm text-gray-600">
                      Proper ARIA labels, roles, and semantic HTML.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900">Color Contrast</h3>
                    <p className="text-sm text-gray-600">
                      WCAG AA compliant color contrast ratios.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900">Focus Management</h3>
                    <p className="text-sm text-gray-600">
                      Focus trapping in modals and proper focus indicators.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900">Reduced Motion</h3>
                    <p className="text-sm text-gray-600">
                      Respects user's motion preferences.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-green-600 mt-1 flex-shrink-0" />
                  <div>
                    <h3 className="font-medium text-gray-900">Touch Targets</h3>
                    <p className="text-sm text-gray-600">
                      Minimum 44px touch target size for mobile.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Test Results */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Accessibility Test Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 mb-4">
                  {Object.entries(testResults).map(([test, passed]) => (
                    <div key={test} className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 capitalize">
                        {test.replace(/([A-Z])/g, ' $1').trim()}
                      </span>
                      {passed ? (
                        <CheckCircleIcon className="w-5 h-5 text-green-600" />
                      ) : (
                        <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />
                      )}
                    </div>
                  ))}
                </div>
                <Button onClick={runAccessibilityTests} variant="outline" className="w-full">
                  Run Accessibility Tests
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Interactive Tests */}
          <div className="space-y-6">
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Interactive Tests</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Focus Trap Test</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Click the button below to test focus trapping in a modal.
                  </p>
                  <Button onClick={() => setShowModal(true)} variant="primary">
                    Open Modal
                  </Button>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Screen Reader Test</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    The following text is only visible to screen readers:
                  </p>
                  <ScreenReaderOnly>
                    This text is only announced to screen readers and demonstrates 
                    the screen reader only utility class.
                  </ScreenReaderOnly>
                  <p className="text-sm text-gray-500 italic">
                    (Screen reader users will hear additional content here)
                  </p>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Keyboard Navigation Test</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Use Tab, Shift+Tab, Enter, and Space to navigate these elements:
                  </p>
                  <div className="space-y-2">
                    <Button variant="outline" size="sm">Button 1</Button>
                    <Button variant="outline" size="sm">Button 2</Button>
                    <Button variant="outline" size="sm">Button 3</Button>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Color Contrast Examples</h3>
                  <div className="space-y-2">
                    <div className="p-3 bg-maroon-700 text-white rounded">
                      High contrast text (WCAG AA compliant)
                    </div>
                    <div className="p-3 bg-gold-500 text-gray-900 rounded">
                      High contrast text (WCAG AA compliant)
                    </div>
                    <div className="p-3 bg-gray-100 text-gray-900 rounded">
                      High contrast text (WCAG AA compliant)
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* WCAG Guidelines */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>WCAG 2.1 AA Compliance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <InformationCircleIcon className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-gray-900">Perceivable</h4>
                      <p className="text-sm text-gray-600">
                        Information and UI components must be presentable to users in ways they can perceive.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <InformationCircleIcon className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-gray-900">Operable</h4>
                      <p className="text-sm text-gray-600">
                        UI components and navigation must be operable by all users.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <InformationCircleIcon className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-gray-900">Understandable</h4>
                      <p className="text-sm text-gray-600">
                        Information and operation of UI must be understandable.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <InformationCircleIcon className="w-5 h-5 text-blue-600 mt-1 flex-shrink-0" />
                    <div>
                      <h4 className="font-medium text-gray-900">Robust</h4>
                      <p className="text-sm text-gray-600">
                        Content must be robust enough for interpretation by assistive technologies.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Modal for Focus Trap Test */}
      {showModal && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <FocusTrap isActive={showModal}>
            <Card variant="elevated" className="w-full max-w-md">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Focus Trap Test Modal</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowModal(false)}
                    className="p-2"
                    aria-label="Close modal"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">
                  This modal demonstrates focus trapping. Try using Tab and Shift+Tab 
                  to navigate. Focus should stay within this modal.
                </p>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full">
                    First Button
                  </Button>
                  <Button variant="outline" className="w-full">
                    Second Button
                  </Button>
                  <div className="flex gap-3">
                    <Button 
                      variant="primary" 
                      onClick={() => setShowModal(false)}
                      className="flex-1"
                    >
                      Close Modal
                    </Button>
                    <Button variant="outline" className="flex-1">
                      Cancel
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </FocusTrap>
        </div>
      )}
    </div>
  );
}
