'use client';

import { useEffect, useCallback, useRef } from 'react';

export interface KeyboardNavigationOptions {
  onEscape?: () => void;
  onArrowLeft?: () => void;
  onArrowRight?: () => void;
  onArrowUp?: () => void;
  onArrowDown?: () => void;
  onEnter?: () => void;
  onSpace?: () => void;
  onTab?: (event: KeyboardEvent) => void;
  onHome?: () => void;
  onEnd?: () => void;
  onPageUp?: () => void;
  onPageDown?: () => void;
  customKeys?: Record<string, () => void>;
  enabled?: boolean;
  preventDefault?: string[];
  stopPropagation?: string[];
}

/**
 * Hook for managing keyboard navigation and shortcuts
 * Provides consistent keyboard interaction patterns across components
 */
export function useKeyboardNavigation(options: KeyboardNavigationOptions = {}) {
  const {
    onEscape,
    onArrowLeft,
    onArrowRight,
    onArrowUp,
    onArrowDown,
    onEnter,
    onSpace,
    onTab,
    onHome,
    onEnd,
    onPageUp,
    onPageDown,
    customKeys = {},
    enabled = true,
    preventDefault = [],
    stopPropagation = []
  } = options;

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    const { key, code } = event;
    
    // Handle preventDefault
    if (preventDefault.includes(key) || preventDefault.includes(code)) {
      event.preventDefault();
    }
    
    // Handle stopPropagation
    if (stopPropagation.includes(key) || stopPropagation.includes(code)) {
      event.stopPropagation();
    }

    switch (key) {
      case 'Escape':
        onEscape?.();
        break;
      case 'ArrowLeft':
        onArrowLeft?.();
        break;
      case 'ArrowRight':
        onArrowRight?.();
        break;
      case 'ArrowUp':
        onArrowUp?.();
        break;
      case 'ArrowDown':
        onArrowDown?.();
        break;
      case 'Enter':
        onEnter?.();
        break;
      case ' ':
        onSpace?.();
        break;
      case 'Tab':
        onTab?.(event);
        break;
      case 'Home':
        onHome?.();
        break;
      case 'End':
        onEnd?.();
        break;
      case 'PageUp':
        onPageUp?.();
        break;
      case 'PageDown':
        onPageDown?.();
        break;
      default:
        // Handle custom keys
        if (customKeys[key]) {
          customKeys[key]();
        }
        break;
    }
  }, [
    enabled,
    onEscape,
    onArrowLeft,
    onArrowRight,
    onArrowUp,
    onArrowDown,
    onEnter,
    onSpace,
    onTab,
    onHome,
    onEnd,
    onPageUp,
    onPageDown,
    customKeys,
    preventDefault,
    stopPropagation
  ]);

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown, enabled]);

  return { handleKeyDown };
}

/**
 * Hook for managing focus trapping within a container
 * Useful for modals, dropdowns, and other overlay components
 */
export function useFocusTrap(isActive: boolean = true) {
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return;

      if (event.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          event.preventDefault();
          lastElement?.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          event.preventDefault();
          firstElement?.focus();
        }
      }
    };

    // Focus first element when trap becomes active
    firstElement?.focus();

    container.addEventListener('keydown', handleTabKey);
    return () => container.removeEventListener('keydown', handleTabKey);
  }, [isActive]);

  return containerRef;
}

/**
 * Hook for managing roving tabindex navigation
 * Useful for grid layouts, toolbars, and menu systems
 */
export function useRovingTabIndex<T extends HTMLElement>(
  items: T[],
  orientation: 'horizontal' | 'vertical' | 'both' = 'horizontal'
) {
  const currentIndex = useRef(0);

  const setActiveItem = useCallback((index: number) => {
    if (index < 0 || index >= items.length) return;
    
    // Remove tabindex from all items
    items.forEach((item, i) => {
      item.setAttribute('tabindex', i === index ? '0' : '-1');
    });
    
    currentIndex.current = index;
    items[index]?.focus();
  }, [items]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const { key } = event;
    let newIndex = currentIndex.current;

    switch (key) {
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = (currentIndex.current + 1) % items.length;
        }
        break;
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex.current === 0 ? items.length - 1 : currentIndex.current - 1;
        }
        break;
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = (currentIndex.current + 1) % items.length;
        }
        break;
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = currentIndex.current === 0 ? items.length - 1 : currentIndex.current - 1;
        }
        break;
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        newIndex = items.length - 1;
        break;
    }

    if (newIndex !== currentIndex.current) {
      setActiveItem(newIndex);
    }
  }, [items, orientation, setActiveItem]);

  // Initialize roving tabindex
  useEffect(() => {
    if (items.length === 0) return;

    items.forEach((item, index) => {
      item.setAttribute('tabindex', index === 0 ? '0' : '-1');
      item.addEventListener('keydown', handleKeyDown);
      item.addEventListener('focus', () => {
        currentIndex.current = index;
      });
    });

    return () => {
      items.forEach(item => {
        item.removeEventListener('keydown', handleKeyDown);
      });
    };
  }, [items, handleKeyDown]);

  return { setActiveItem, currentIndex: currentIndex.current };
}

/**
 * Hook for managing skip links navigation
 */
export function useSkipLinks() {
  const skipToContent = useCallback(() => {
    const mainContent = document.getElementById('main-content') || 
                       document.querySelector('main') ||
                       document.querySelector('[role="main"]');
    
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  const skipToNavigation = useCallback(() => {
    const navigation = document.getElementById('main-navigation') ||
                      document.querySelector('nav') ||
                      document.querySelector('[role="navigation"]');
    
    if (navigation) {
      navigation.focus();
      navigation.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  return { skipToContent, skipToNavigation };
}