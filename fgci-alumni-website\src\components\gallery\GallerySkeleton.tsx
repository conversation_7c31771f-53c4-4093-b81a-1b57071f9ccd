'use client';

import { cn } from '@/lib/utils';

interface GallerySkeletonProps {
  columns?: 2 | 3 | 4;
  count?: number;
  className?: string;
}

export function GallerySkeleton({ 
  columns = 3, 
  count = 12, 
  className 
}: GallerySkeletonProps) {
  const gridClasses = {
    2: 'grid-cols-2',
    3: 'grid-cols-2 md:grid-cols-3',
    4: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
  };

  return (
    <div className={cn(
      "grid gap-4",
      gridClasses[columns],
      className
    )}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="relative aspect-square rounded-lg bg-gray-200 animate-pulse overflow-hidden"
        >
          {/* Shimmer effect */}
          <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/60 to-transparent" />
        </div>
      ))}
    </div>
  );
}

interface AlbumSkeletonProps {
  count?: number;
  className?: string;
}

export function AlbumSkeleton({ count = 6, className }: AlbumSkeletonProps) {
  return (
    <div className={cn(
      "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",
      className
    )}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse"
        >
          {/* Album cover skeleton */}
          <div className="relative h-64 bg-gray-200">
            <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/60 to-transparent" />
          </div>
          
          {/* Album info skeleton */}
          <div className="p-6 space-y-3">
            <div className="h-6 bg-gray-200 rounded animate-pulse" />
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
            </div>
            <div className="flex justify-between">
              <div className="h-3 bg-gray-200 rounded w-20 animate-pulse" />
              <div className="h-3 bg-gray-200 rounded w-16 animate-pulse" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}