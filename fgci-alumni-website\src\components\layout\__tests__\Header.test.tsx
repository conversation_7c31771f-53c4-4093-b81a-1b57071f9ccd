import { render, screen, fireEvent, waitFor } from '@/lib/test-utils';
import { Header } from '../Header';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

// Mock next/navigation
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => '/',
}));

describe('Header Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders header with logo and navigation', () => {
    render(<Header />);
    
    expect(screen.getByRole('banner')).toBeInTheDocument();
    expect(screen.getByText('FGC Ikom Alumni')).toBeInTheDocument();
    expect(screen.getByRole('navigation')).toBeInTheDocument();
  });

  it('renders all navigation links', () => {
    render(<Header />);
    
    expect(screen.getByRole('link', { name: /home/<USER>
    expect(screen.getByRole('link', { name: /chapters/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /sets/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /events/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /gallery/i })).toBeInTheDocument();
    expect(screen.getByRole('link', { name: /excobot/i })).toBeInTheDocument();
  });

  it('renders skip to content link', () => {
    render(<Header />);
    
    const skipLink = screen.getByText('Skip to content');
    expect(skipLink).toBeInTheDocument();
    expect(skipLink).toHaveAttribute('href', '#main-content');
  });

  it('shows mobile menu button on small screens', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 640,
    });

    render(<Header />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    expect(menuButton).toBeInTheDocument();
    expect(menuButton).toHaveAttribute('aria-expanded', 'false');
  });

  it('toggles mobile menu when button is clicked', async () => {
    render(<Header />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    
    // Open menu
    fireEvent.click(menuButton);
    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'true');
    });
    
    // Close menu
    fireEvent.click(menuButton);
    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'false');
    });
  });

  it('closes mobile menu when escape key is pressed', async () => {
    render(<Header />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    
    // Open menu
    fireEvent.click(menuButton);
    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'true');
    });
    
    // Press escape
    fireEvent.keyDown(document, { key: 'Escape' });
    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'false');
    });
  });

  it('closes mobile menu when clicking outside', async () => {
    render(<Header />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    
    // Open menu
    fireEvent.click(menuButton);
    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'true');
    });
    
    // Click outside
    fireEvent.click(document.body);
    await waitFor(() => {
      expect(menuButton).toHaveAttribute('aria-expanded', 'false');
    });
  });

  it('highlights active navigation link', () => {
    // Mock current pathname
    jest.mock('next/navigation', () => ({
      useRouter: () => ({
        push: mockPush,
        replace: jest.fn(),
        prefetch: jest.fn(),
        back: jest.fn(),
        forward: jest.fn(),
        refresh: jest.fn(),
      }),
      usePathname: () => '/chapters',
    }));

    render(<Header />);
    
    const chaptersLink = screen.getByRole('link', { name: /chapters/i });
    expect(chaptersLink).toHaveClass('nav-link-active');
  });

  it('renders search functionality', () => {
    render(<Header />);
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    expect(searchButton).toBeInTheDocument();
  });

  it('opens search modal when search button is clicked', async () => {
    render(<Header />);
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    fireEvent.click(searchButton);
    
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument();
    });
  });

  it('handles search input and submission', async () => {
    render(<Header />);
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    fireEvent.click(searchButton);
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText(/search/i);
      fireEvent.change(searchInput, { target: { value: 'test query' } });
      fireEvent.submit(searchInput.closest('form')!);
      
      expect(mockPush).toHaveBeenCalledWith('/search?q=test+query');
    });
  });

  it('supports keyboard navigation', () => {
    render(<Header />);
    
    const navigation = screen.getByRole('navigation');
    const links = screen.getAllByRole('link');
    
    // First link should be focusable
    links[0].focus();
    expect(document.activeElement).toBe(links[0]);
    
    // Tab should move to next link
    fireEvent.keyDown(links[0], { key: 'Tab' });
    // Note: In a real test, you'd check focus management
  });

  it('has proper semantic structure', () => {
    render(<Header />);
    
    const header = screen.getByRole('banner');
    const navigation = screen.getByRole('navigation');
    const logo = screen.getByText('FGC Ikom Alumni');
    
    expect(header).toContainElement(navigation);
    expect(header).toContainElement(logo);
  });

  it('has proper ARIA attributes', () => {
    render(<Header />);
    
    const navigation = screen.getByRole('navigation');
    expect(navigation).toHaveAttribute('aria-label', 'Main navigation');
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    expect(menuButton).toHaveAttribute('aria-controls');
    expect(menuButton).toHaveAttribute('aria-expanded');
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<Header />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('has no accessibility violations with mobile menu open', async () => {
    const { container } = render(<Header />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    fireEvent.click(menuButton);
    
    await waitFor(async () => {
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  it('renders admin login button when not authenticated', () => {
    render(<Header />);
    
    const loginButton = screen.getByRole('button', { name: /admin login/i });
    expect(loginButton).toBeInTheDocument();
  });

  it('renders user menu when authenticated', () => {
    // Mock authenticated state
    render(<Header isAuthenticated />);
    
    const userMenu = screen.getByRole('button', { name: /user menu/i });
    expect(userMenu).toBeInTheDocument();
  });

  it('handles responsive behavior', () => {
    // Test desktop view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    const { rerender } = render(<Header />);
    
    expect(screen.queryByRole('button', { name: /menu/i })).not.toBeInTheDocument();
    
    // Test mobile view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 640,
    });

    rerender(<Header />);
    
    expect(screen.getByRole('button', { name: /menu/i })).toBeInTheDocument();
  });

  it('maintains focus management in mobile menu', async () => {
    render(<Header />);
    
    const menuButton = screen.getByRole('button', { name: /menu/i });
    
    // Open menu
    fireEvent.click(menuButton);
    
    await waitFor(() => {
      const firstLink = screen.getAllByRole('link')[0];
      expect(document.activeElement).toBe(firstLink);
    });
  });

  it('renders with custom className', () => {
    render(<Header className="custom-header" />);
    
    const header = screen.getByRole('banner');
    expect(header).toHaveClass('custom-header');
  });
});