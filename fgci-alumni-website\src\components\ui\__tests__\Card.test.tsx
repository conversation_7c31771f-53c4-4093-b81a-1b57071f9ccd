import { render, screen, fireEvent } from '@/lib/test-utils';
import { Card, CardHeader, CardTitle, CardContent } from '../Card';

describe('Card Components', () => {
  describe('Card', () => {
    it('renders with default props', () => {
      render(
        <Card>
          <div>Card content</div>
        </Card>
      );
      
      const card = screen.getByText('Card content').closest('div');
      expect(card).toHaveClass('card');
    });

    it('renders with different variants', () => {
      const { rerender } = render(<Card variant="elevated">Elevated</Card>);
      expect(screen.getByText('Elevated').closest('div')).toHaveClass('card-elevated');

      rerender(<Card variant="outlined">Outlined</Card>);
      expect(screen.getByText('Outlined').closest('div')).toHaveClass('card-outlined');
    });

    it('renders with hover effect', () => {
      render(<Card hover>Hoverable</Card>);
      const card = screen.getByText('Hoverable').closest('div');
      expect(card).toHaveClass('card-hover');
    });

    it('handles click events when clickable', () => {
      const handleClick = jest.fn();
      render(
        <Card onClick={handleClick}>
          Clickable card
        </Card>
      );
      
      const card = screen.getByText('Clickable card').closest('div');
      fireEvent.click(card!);
      
      expect(handleClick).toHaveBeenCalledTimes(1);
    });

    it('renders with custom className', () => {
      render(<Card className="custom-card">Custom</Card>);
      const card = screen.getByText('Custom').closest('div');
      expect(card).toHaveClass('custom-card');
    });
  });

  describe('CardHeader', () => {
    it('renders header content', () => {
      render(
        <Card>
          <CardHeader>
            <h2>Header Title</h2>
          </CardHeader>
        </Card>
      );
      
      const header = screen.getByText('Header Title').closest('div');
      expect(header).toHaveClass('card-header');
    });
  });

  describe('CardTitle', () => {
    it('renders title with default heading level', () => {
      render(
        <Card>
          <CardHeader>
            <CardTitle>Card Title</CardTitle>
          </CardHeader>
        </Card>
      );
      
      const title = screen.getByText('Card Title');
      expect(title).toHaveClass('card-title');
      expect(title.tagName).toBe('H3');
    });

    it('renders title with custom heading level', () => {
      render(
        <Card>
          <CardHeader>
            <CardTitle as="h2">Custom Level</CardTitle>
          </CardHeader>
        </Card>
      );
      
      const title = screen.getByText('Custom Level');
      expect(title.tagName).toBe('H2');
    });
  });

  describe('CardContent', () => {
    it('renders content area', () => {
      render(
        <Card>
          <CardContent>
            <p>Card content goes here</p>
          </CardContent>
        </Card>
      );
      
      const content = screen.getByText('Card content goes here').closest('div');
      expect(content).toHaveClass('card-content');
    });
  });

  describe('Complete Card', () => {
    it('renders a complete card structure', () => {
      render(
        <Card variant="elevated" hover>
          <CardHeader>
            <CardTitle>Complete Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This is a complete card with header and content.</p>
          </CardContent>
        </Card>
      );
      
      expect(screen.getByText('Complete Card')).toBeInTheDocument();
      expect(screen.getByText('This is a complete card with header and content.')).toBeInTheDocument();
      
      const card = screen.getByText('Complete Card').closest('div')?.closest('div');
      expect(card).toHaveClass('card-elevated', 'card-hover');
    });

    it('maintains proper semantic structure', () => {
      render(
        <Card>
          <CardHeader>
            <CardTitle>Semantic Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Content with proper semantics</p>
          </CardContent>
        </Card>
      );
      
      // Check that the title is properly nested within the header
      const title = screen.getByText('Semantic Card');
      const header = title.closest('.card-header');
      expect(header).toBeInTheDocument();
      
      // Check that content is separate from header
      const content = screen.getByText('Content with proper semantics');
      const contentContainer = content.closest('.card-content');
      expect(contentContainer).toBeInTheDocument();
      expect(contentContainer).not.toBe(header);
    });
  });
});
