import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const detailedHealth = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        memory: getMemoryUsage(),
        cpu: getCpuUsage(),
      },
      services: {
        database: await checkDatabase(),
        api: await checkExternalAPI(),
        storage: await checkStorage(),
      },
      performance: {
        responseTime: Date.now(),
        lastDeployment: process.env.VERCEL_GIT_COMMIT_SHA || 'unknown',
        buildTime: process.env.BUILD_TIME || 'unknown',
      }
    };

    // Calculate response time
    detailedHealth.performance.responseTime = Date.now() - detailedHealth.performance.responseTime;

    return NextResponse.json(detailedHealth, { status: 200 });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function getMemoryUsage() {
  const usage = process.memoryUsage();
  return {
    rss: usage.rss,
    heapTotal: usage.heapTotal,
    heapUsed: usage.heapUsed,
    external: usage.external,
    arrayBuffers: usage.arrayBuffers,
    rssMB: Math.round(usage.rss / 1024 / 1024),
    heapTotalMB: Math.round(usage.heapTotal / 1024 / 1024),
    heapUsedMB: Math.round(usage.heapUsed / 1024 / 1024),
    heapUsedPercentage: Math.round((usage.heapUsed / usage.heapTotal) * 100),
  };
}

function getCpuUsage() {
  const usage = process.cpuUsage();
  return {
    user: usage.user,
    system: usage.system,
    userMS: Math.round(usage.user / 1000),
    systemMS: Math.round(usage.system / 1000),
  };
}

async function checkDatabase(): Promise<{ status: string; responseTime?: number; error?: string }> {
  try {
    const start = Date.now();
    
    // In a real application, you would check your database connection here
    // For now, we'll simulate a database check
    if (process.env.DATABASE_URL) {
      // Simulate database ping
      await new Promise(resolve => setTimeout(resolve, 10));
      return {
        status: 'connected',
        responseTime: Date.now() - start,
      };
    } else {
      return {
        status: 'not_configured',
      };
    }
  } catch (error) {
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Database connection failed',
    };
  }
}

async function checkExternalAPI(): Promise<{ status: string; responseTime?: number; error?: string }> {
  try {
    const start = Date.now();
    
    // Check if external API is configured
    if (process.env.NEXT_PUBLIC_API_URL) {
      // In a real application, you would ping your external API here
      // For now, we'll simulate an API check
      await new Promise(resolve => setTimeout(resolve, 5));
      return {
        status: 'available',
        responseTime: Date.now() - start,
      };
    } else {
      return {
        status: 'not_configured',
      };
    }
  } catch (error) {
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'External API check failed',
    };
  }
}

async function checkStorage(): Promise<{ status: string; error?: string }> {
  try {
    // In a real application, you would check your storage service here
    // For now, we'll simulate a storage check
    return {
      status: 'available',
    };
  } catch (error) {
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Storage check failed',
    };
  }
}
