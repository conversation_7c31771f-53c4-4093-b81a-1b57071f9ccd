# Design Document

## Overview

The FGC Ikom Alumni Website is a modern, performance-optimized React application built with Next.js 14 using the App Router. The architecture follows a component-driven development approach with a focus on accessibility, performance, and maintainability. The design system is built around the maroon and gold brand colors with Tailwind CSS providing utility-first styling and design tokens.

The application uses a hybrid rendering strategy with Static Site Generation (SSG) for content-heavy pages and Incremental Static Regeneration (ISR) for dynamic content. A comprehensive mock service worker setup enables offline development, while React Query handles data fetching and caching for optimal performance.

## Architecture

### Frontend Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Next.js App Router                       │
├─────────────────────────────────────────────────────────────┤
│  Pages Layer                                                │
│  ├── Static Pages (SSG): Home, About, Contact, Privacy     │
│  ├── Dynamic Pages (ISR): Chapters, Sets, Events           │
│  └── Interactive Pages (CSR): ExcoBot, Search              │
├─────────────────────────────────────────────────────────────┤
│  Component Layer                                            │
│  ├── Layout Components (Header, Footer, Navigation)        │
│  ├── UI Components (Button, Card, Modal, Lightbox)         │
│  ├── Feature Components (ChapterCard, SetDetail, Timeline) │
│  └── Form Components (ContactForm, SearchForm)             │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                 │
│  ├── React Query (TanStack Query) - Caching & State        │
│  ├── API Client - HTTP requests with error handling        │
│  └── Mock Service Worker - Development & Testing           │
├─────────────────────────────────────────────────────────────┤
│  Styling & Animation                                        │
│  ├── Tailwind CSS - Utility classes & design tokens        │
│  ├── CSS Variables - Brand colors & spacing                │
│  └── Framer Motion - Animations & transitions              │
└─────────────────────────────────────────────────────────────┘
```

### Rendering Strategy

- **SSG (Static Site Generation)**: Home, About, Contact, Privacy pages
- **ISR (Incremental Static Regeneration)**: Chapters index, Sets index, individual chapter/set pages
- **CSR (Client-Side Rendering)**: ExcoBot search, Gallery lightbox, Interactive forms

### Performance Optimizations

- Next.js Image component with automatic WebP/AVIF conversion
- Font optimization with `font-display: swap` and variable fonts
- Code splitting at route and component level
- Lazy loading for images and non-critical components
- Service Worker for caching static assets
- CDN integration for media assets

## Components and Interfaces

### Core UI Components

#### Button Component
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: React.ReactNode;
}
```

#### Card Component
```typescript
interface CardProps {
  variant: 'default' | 'elevated' | 'outlined';
  padding: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  hover?: boolean;
  onClick?: () => void;
}
```

#### Modal/Lightbox Component
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size: 'sm' | 'md' | 'lg' | 'xl' | 'fullscreen';
  children: React.ReactNode;
  closeOnOverlayClick?: boolean;
}
```

### Layout Components

#### Header Component
- Responsive navigation with mobile hamburger menu
- Search functionality integration
- Admin login CTA (placeholder)
- Accessibility: Skip to content link, keyboard navigation

#### Footer Component
- Contact information and social links
- Quick navigation links
- Copyright and legal information
- Newsletter signup form

### Feature-Specific Components

#### Chapter Components
```typescript
interface ChapterCardProps {
  chapter: Chapter;
  showLastActivity?: boolean;
  compact?: boolean;
}

interface ChapterPageProps {
  chapter: Chapter;
  activities: Activity[];
  galleryPreview: string[];
}
```

#### Set Components
```typescript
interface SetCardProps {
  set: Set;
  showMemberCount?: boolean;
}

interface SetDetailProps {
  set: Set;
  activities: Activity[];
  gallery: GalleryAlbum[];
}
```

#### Gallery Components
```typescript
interface GalleryGridProps {
  images: GalleryImage[];
  columns: 2 | 3 | 4;
  onImageClick: (image: GalleryImage, index: number) => void;
}

interface LightboxProps {
  images: GalleryImage[];
  currentIndex: number;
  isOpen: boolean;
  onClose: () => void;
  onNext: () => void;
  onPrevious: () => void;
}
```

#### Timeline Component
```typescript
interface TimelineProps {
  activities: Activity[];
  variant: 'vertical' | 'horizontal';
  showDates?: boolean;
  interactive?: boolean;
}
```

### Search and Directory Components

#### ExcoBot Components
```typescript
interface ExcoBotSearchProps {
  onSearch: (query: string, filters: SearchFilters) => void;
  results: Person[];
  loading: boolean;
}

interface AlumniCardProps {
  person: Person;
  showContactInfo?: boolean;
  compact?: boolean;
}
```

## Data Models

### Core Data Types

```typescript
interface Person {
  id: string;
  name: string;
  role?: string;
  phone?: string;
  email?: string;
  photo?: string;
  graduationYear?: number;
  chapter?: string;
  currentLocation?: string;
  bio?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
}

interface Chapter {
  id: string;
  slug: string;
  name: string;
  region?: string;
  type: 'regional' | 'diaspora' | 'school-based';
  summary?: string;
  description?: string;
  heroImage?: string;
  exco: Person[];
  activities: Activity[];
  contactEmail?: string;
  establishedDate?: string;
  lastActivityDate?: string;
  memberCount?: number;
}

interface Set {
  id: string;
  slug: string;
  name: string;
  year: number;
  motto?: string;
  description?: string;
  heroImage?: string;
  leader?: Person;
  exco: Person[];
  activities: Activity[];
  gallery: string[];
  documents?: Document[];
  memberCount?: number;
}

interface Activity {
  id: string;
  date: string;
  title: string;
  description: string;
  photos?: string[];
  location?: string;
  type: 'meeting' | 'event' | 'fundraiser' | 'social' | 'memorial';
}

interface Event {
  id: string;
  slug: string;
  title: string;
  description: string;
  date: string;
  endDate?: string;
  location: string;
  heroImage?: string;
  gallery?: string[];
  organizer: string;
  registrationUrl?: string;
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
}

interface GalleryImage {
  id: string;
  url: string;
  thumbnailUrl: string;
  alt: string;
  caption?: string;
  photographer?: string;
  date?: string;
  album?: string;
  tags?: string[];
}

interface Memorial {
  id: string;
  person: Person;
  dateOfPassing: string;
  tribute: string;
  condolences?: Condolence[];
  photos?: string[];
}

interface Condolence {
  id: string;
  name: string;
  message: string;
  date: string;
  approved: boolean;
}
```

### API Response Types

```typescript
interface ApiResponse<T> {
  data: T;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasNext?: boolean;
  };
  error?: string;
}

interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}
```

## Error Handling

### Error Boundary Strategy

```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

// Global Error Boundary for unhandled errors
// Page-level Error Boundaries for route-specific errors
// Component-level Error Boundaries for critical components
```

### API Error Handling

```typescript
interface ApiError {
  status: number;
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Centralized error handling in API client
// User-friendly error messages
// Retry logic for transient failures
// Offline state handling
```

### Form Validation

```typescript
interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

interface FormErrors {
  [fieldName: string]: string[];
}
```

## Testing Strategy

### Unit Testing
- **Jest + React Testing Library** for component testing
- **Coverage target**: 80% for components, 90% for utilities
- **Test categories**:
  - Component rendering and props
  - User interactions (clicks, form submissions)
  - Accessibility (ARIA attributes, keyboard navigation)
  - Error states and edge cases

### Integration Testing
- **API integration** with Mock Service Worker
- **Form submissions** with validation
- **Search functionality** across components
- **Navigation flows** between pages

### End-to-End Testing
- **Playwright** for critical user journeys:
  - Home → Chapters → Chapter Detail → Gallery
  - Sets browsing and filtering
  - ExcoBot search functionality
  - Contact form submission
  - Mobile responsive behavior

### Accessibility Testing
- **axe-core** integration in unit tests
- **Manual keyboard navigation** testing
- **Screen reader** compatibility testing
- **Color contrast** validation
- **Focus management** testing

### Performance Testing
- **Lighthouse CI** integration
- **Core Web Vitals** monitoring
- **Bundle size** analysis
- **Image optimization** verification

## Design System Implementation

### Color Tokens
```css
:root {
  /* Primary Colors */
  --color-maroon: #7B0F17;
  --color-maroon-900: #5C0B12;
  --color-maroon-800: #6A0D15;
  --color-maroon-700: #7B0F17;
  --color-maroon-600: #8C1119;
  --color-maroon-500: #9D131B;
  
  /* Gold Colors */
  --color-gold: #D4A017;
  --color-gold-200: #F7E6C0;
  --color-gold-300: #F0D898;
  --color-gold-400: #E8CA70;
  --color-gold-500: #D4A017;
  --color-gold-600: #B8900F;
  
  /* Neutral Colors */
  --gray-900: #0f172a;
  --gray-800: #1e293b;
  --gray-700: #334155;
  --gray-600: #475569;
  --gray-500: #64748b;
  --gray-400: #94a3b8;
  --gray-300: #cbd5e1;
  --gray-200: #e2e8f0;
  --gray-100: #f1f5f9;
  --gray-50: #f8fafc;
  
  /* Focus States */
  --focus: 3px solid rgba(212,160,23,0.25);
  
  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
}
```

### Typography Scale
```css
:root {
  /* Font Families */
  --font-heading: 'Playfair Display Variable', 'Merriweather', serif;
  --font-body: 'Inter Variable', 'Open Sans', sans-serif;
  
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
}
```

### Spacing Scale
```css
:root {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
}
```

### Animation Tokens
```css
:root {
  /* Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  
  /* Easings */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Transforms */
  --transform-hover: translateY(-6px);
  --shadow-hover: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
```

## Accessibility Implementation

### Focus Management
- Visible focus indicators using `--focus` token
- Focus trapping in modals and lightboxes
- Skip links for keyboard navigation
- Logical tab order throughout the application

### Screen Reader Support
- Semantic HTML structure with proper landmarks
- ARIA labels and descriptions for interactive elements
- Live regions for dynamic content updates
- Alternative text for all images

### Color and Contrast
- WCAG AA compliant color combinations
- Alternative indicators beyond color (icons, patterns)
- High contrast mode support
- Reduced motion preferences respected

### Keyboard Navigation
- All interactive elements keyboard accessible
- Custom keyboard shortcuts for gallery navigation
- Escape key handling for modals
- Arrow key navigation for carousels and grids

## Performance Optimization

### Image Optimization
- Next.js Image component with automatic format selection
- Responsive images with appropriate srcset
- Lazy loading with intersection observer
- CDN integration for fast delivery

### Code Splitting
- Route-based code splitting with Next.js
- Component-level lazy loading for heavy components
- Dynamic imports for non-critical functionality
- Tree shaking for unused code elimination

### Caching Strategy
- Static assets cached with long TTL
- API responses cached with React Query
- ISR for dynamic content with appropriate revalidation
- Service Worker for offline functionality

### Bundle Optimization
- Webpack bundle analyzer integration
- Import optimization and tree shaking
- CSS purging with Tailwind
- Font subsetting for used characters only