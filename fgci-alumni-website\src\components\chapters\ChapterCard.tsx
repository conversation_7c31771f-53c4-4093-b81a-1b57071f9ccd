'use client';

import { useState } from 'react';
import Link from 'next/link';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui';
import { 
  MapPinIcon, 
  UsersIcon,
  CalendarDaysIcon,
  EnvelopeIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import type { Chapter } from '@/lib/types';

interface ChapterCardProps {
  chapter: Chapter;
  showLastActivity?: boolean;
  compact?: boolean;
}

function ChapterCard({ 
  chapter, 
  showLastActivity = true, 
  compact = false 
}: ChapterCardProps) {
  const [imageError, setImageError] = useState(false);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'regional':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'diaspora':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'school-based':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEstablishedYear = () => {
    if (!chapter.establishedDate) return 'N/A';
    return new Date(chapter.establishedDate).getFullYear();
  };

  const getLastActivityText = () => {
    if (!chapter.lastActivityDate) return 'No recent activity';
    const daysSince = Math.floor(
      (Date.now() - new Date(chapter.lastActivityDate).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSince === 0) return 'Active today';
    if (daysSince === 1) return 'Active yesterday';
    if (daysSince < 7) return `Active ${daysSince} days ago`;
    if (daysSince < 30) return `Active ${Math.floor(daysSince / 7)} weeks ago`;
    if (daysSince < 365) return `Active ${Math.floor(daysSince / 30)} months ago`;
    return `Active ${Math.floor(daysSince / 365)} years ago`;
  };

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <Card
      variant="elevated"
      hover
      className={cn(
        "group overflow-hidden transition-all duration-300",
        "hover:shadow-xl hover:-translate-y-2 hover:shadow-maroon-100/50",
        "transform-gpu will-change-transform",
        compact && "max-w-sm"
      )}
    >
      {/* Chapter Image */}
      <div className="relative h-48 overflow-hidden bg-gray-200">
        {!imageError && chapter.heroImage ? (
          <img
            src={chapter.heroImage}
            alt={`${chapter.name} chapter`}
            className="w-full h-full object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-maroon-100 to-maroon-200 flex items-center justify-center transition-all duration-300 group-hover:from-maroon-200 group-hover:to-maroon-300">
            <MapPinIcon className="w-16 h-16 text-maroon-400 transition-transform duration-300 group-hover:scale-110" />
          </div>
        )}
        
        {/* Type Badge */}
        <div className="absolute top-4 left-4">
          <span className={cn(
            'px-3 py-1 text-xs font-medium rounded-full border',
            getTypeColor(chapter.type)
          )}>
            {chapter.type.charAt(0).toUpperCase() + chapter.type.slice(1).replace('-', ' ')}
          </span>
        </div>
      </div>

      <CardHeader className="pb-3">
        <CardTitle className="flex items-start justify-between">
          <span className="text-xl font-bold text-gray-900 group-hover:text-maroon-700 transition-colors duration-300">
            {chapter.name}
          </span>
        </CardTitle>
        
        {chapter.region && (
          <div className="flex items-center text-sm text-gray-500 mt-1">
            <MapPinIcon className="w-4 h-4 mr-1 flex-shrink-0" />
            <span>{chapter.region}</span>
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0 space-y-4">
        {/* Summary */}
        {chapter.summary && (
          <p className="text-gray-600 text-sm line-clamp-3 leading-relaxed">
            {chapter.summary}
          </p>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          {chapter.memberCount && (
            <div className="flex items-center text-gray-600">
              <UsersIcon className="w-4 h-4 mr-2 flex-shrink-0 text-maroon-500" />
              <span>{chapter.memberCount.toLocaleString()} members</span>
            </div>
          )}
          
          <div className="flex items-center text-gray-600">
            <CalendarDaysIcon className="w-4 h-4 mr-2 flex-shrink-0 text-maroon-500" />
            <span>Est. {getEstablishedYear()}</span>
          </div>
        </div>

        {/* Last Activity */}
        {showLastActivity && (
          <div className="text-xs text-gray-500 bg-gray-50 px-3 py-2 rounded-md">
            {getLastActivityText()}
          </div>
        )}

        {/* Contact Info */}
        {chapter.contactEmail && (
          <div className="flex items-center text-sm text-gray-600">
            <EnvelopeIcon className="w-4 h-4 mr-2 flex-shrink-0 text-maroon-500" />
            <a 
              href={`mailto:${chapter.contactEmail}`}
              className="hover:text-maroon-700 transition-colors truncate"
              onClick={(e) => e.stopPropagation()}
            >
              {chapter.contactEmail}
            </a>
          </div>
        )}

        {/* Action Button */}
        <Link 
          href={`/chapters/${chapter.slug}`}
          className="block w-full"
        >
          <button className={cn(
            "w-full flex items-center justify-center gap-2 px-4 py-2.5 rounded-lg",
            "bg-maroon-700 text-white font-medium text-sm",
            "hover:bg-maroon-800 focus:bg-maroon-800",
            "focus:outline-none focus:ring-2 focus:ring-maroon-500 focus:ring-offset-2",
            "transition-all duration-300 transform",
            "group-hover:bg-maroon-800 group-hover:shadow-lg group-hover:scale-105",
            "active:scale-95"
          )}>
            <span>View Chapter Details</span>
            <ArrowRightIcon className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
          </button>
        </Link>
      </CardContent>
    </Card>
  );
}

export { ChapterCard };