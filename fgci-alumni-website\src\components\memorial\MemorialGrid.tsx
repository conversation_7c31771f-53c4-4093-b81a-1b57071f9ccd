'use client';

import { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { MemorialCard } from './MemorialCard';
import { useMemorials } from '@/hooks/useMemorials';
import { LoadingStates } from '@/components/ui/LoadingStates';

interface MemorialFilters {
  graduationYear?: string;
  chapter?: string;
  searchQuery?: string;
}

interface MemorialGridProps {
  filters?: MemorialFilters;
}

export function MemorialGrid({ filters = {} }: MemorialGridProps = {}) {
  const { data: memorials, isLoading, error } = useMemorials();
  const [localFilters, setLocalFilters] = useState<MemorialFilters>(filters);

  const filteredMemorials = useMemo(() => {
    if (!memorials) return [];

    const activeFilters = { ...localFilters, ...filters };

    return memorials.filter((memorial) => {
      const { person } = memorial;
      
      // Search query filter
      if (activeFilters.searchQuery) {
        const query = activeFilters.searchQuery.toLowerCase();
        const matchesName = person.name.toLowerCase().includes(query);
        const matchesRole = person.role?.toLowerCase().includes(query);
        const matchesChapter = person.chapter?.toLowerCase().includes(query);
        
        if (!matchesName && !matchesRole && !matchesChapter) {
          return false;
        }
      }

      // Graduation year filter (by decade)
      if (activeFilters.graduationYear && person.graduationYear) {
        const decade = Math.floor(person.graduationYear / 10) * 10;
        const expectedDecade = activeFilters.graduationYear.replace('s', '');
        if (decade.toString() !== expectedDecade) {
          return false;
        }
      }

      // Chapter filter
      if (activeFilters.chapter && person.chapter !== activeFilters.chapter) {
        return false;
      }

      return true;
    });
  }, [memorials, localFilters, filters]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <LoadingStates.MemorialGrid />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
          <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-medium text-red-800 mb-2">Unable to Load Memorial</h3>
          <p className="text-red-600">We're having trouble loading the memorial information. Please try again later.</p>
        </div>
      </div>
    );
  }

  if (!memorials || memorials.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Memorial Records</h3>
          <p className="text-gray-600">There are currently no memorial records to display.</p>
        </div>
      </div>
    );
  }

  if (filteredMemorials.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 max-w-md mx-auto">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Found</h3>
          <p className="text-gray-600">No memorial records match your current filters. Try adjusting your search criteria.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-gray-600">
          Showing {filteredMemorials.length} of {memorials.length} memorial{memorials.length !== 1 ? 's' : ''}
        </p>
        
        {/* Sort Options */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Sort by:</span>
          <select className="text-sm border border-gray-300 rounded-md px-2 py-1 bg-white focus:outline-none focus:ring-1 focus:ring-maroon-500 focus:border-maroon-500">
            <option value="name">Name</option>
            <option value="year">Graduation Year</option>
            <option value="date">Date of Passing</option>
            <option value="chapter">Chapter</option>
          </select>
        </div>
      </div>

      {/* Memorial Grid */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {filteredMemorials.map((memorial, index) => (
          <MemorialCard
            key={memorial.id}
            memorial={memorial}
            index={index}
          />
        ))}
      </motion.div>
    </div>
  );
}