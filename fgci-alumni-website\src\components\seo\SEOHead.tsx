import Head from 'next/head';
import { 
  generateTitle, 
  generateCanonicalUrl, 
  generateOGImageUrl,
  generateRobotsContent,
  generateKeywords,
  SITE_CONFIG,
  type SEOConfig 
} from '@/lib/seo';

interface SEOHeadProps extends Partial<SEOConfig> {
  pathname?: string;
}

export function SEOHead({
  title,
  description,
  keywords = [],
  canonical,
  ogImage,
  ogType = 'website',
  twitterCard = 'summary_large_image',
  noindex = false,
  nofollow = false,
  structuredData,
  pathname = '/'
}: SEOHeadProps) {
  const pageTitle = generateTitle(title);
  const canonicalUrl = canonical || generateCanonicalUrl(pathname);
  const ogImageUrl = ogImage || generateOGImageUrl(title, description);
  const keywordsString = generateKeywords(keywords);
  const robotsContent = generateRobotsContent(noindex, nofollow);

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{pageTitle}</title>
      <meta name="description" content={description} />
      {keywordsString && <meta name="keywords" content={keywordsString} />}
      <meta name="robots" content={robotsContent} />
      <link rel="canonical" href={canonicalUrl} />

      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={ogType} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:site_name" content={SITE_CONFIG.name} />
      <meta property="og:image" content={ogImageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={pageTitle} />
      <meta property="og:locale" content="en_US" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:site" content={SITE_CONFIG.social.twitter} />
      <meta name="twitter:creator" content={SITE_CONFIG.social.twitter} />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImageUrl} />
      <meta name="twitter:image:alt" content={pageTitle} />

      {/* Additional Meta Tags */}
      <meta name="author" content={SITE_CONFIG.name} />
      <meta name="publisher" content={SITE_CONFIG.name} />
      <meta name="theme-color" content="#8B4513" />
      <meta name="msapplication-TileColor" content="#8B4513" />

      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
      )}
    </Head>
  );
}

export default SEOHead;
