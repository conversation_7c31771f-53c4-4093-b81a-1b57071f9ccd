# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

Project: FGC Ikom Alumni Website (Next.js + TypeScript + Tailwind CSS)

Commands you will commonly use
- Install dependencies
  - npm install

- Develop
  - Start dev server (Next.js, Turbopack): npm run dev
  - Start dev server with API mocks (MSW): npm run dev:mock
  - Run Storybook (port 6006): npm run storybook

- Build and run
  - Production build (Next.js, Turbopack): npm run build
  - Start production server: npm run start
  - Build Storybook: npm run build-storybook

- Lint / Format / Types
  - Lint: npm run lint
  - Lint and fix: npm run lint:fix
  - Format with Prettier: npm run format
  - Check formatting: npm run format:check
  - Type-check: npm run type-check

- Tests (Vitest + RTL)
  - Unit tests (watch): npm run test
  - Test UI: npm run test:ui
  - Coverage: npm run test:coverage
  - CI mode: npm run test:ci
  - Run a single test file: npm run test -- path/to/file.test.ts
  - Run tests by name: npm run test -- -t "test name substring"

- E2E (Playwright)
  - Run E2E tests (headless): npm run test:e2e
  - Run E2E tests (headed): npm run test:e2e:headed
  - Open Playwright UI: npm run test:e2e:ui
  - Show last E2E report: npm run test:e2e:report

- Performance and bundle analysis
  - Lighthouse against local dev (writes lighthouse-report.html): npm run lighthouse
  - Next.js bundle analyzer build: npm run bundle-analyzer
  - Combined perf audit: npm run perf:audit

Environment and configuration
- Local env: copy .env.example to .env.local and adjust values.
  - NEXT_PUBLIC_API_URL, NEXT_PUBLIC_CDN_URL
  - Feature flags: NEXT_PUBLIC_USE_MOCKS, NEXT_PUBLIC_ENABLE_ANALYTICS
  - Optional analytics: NEXT_PUBLIC_GA_ID
- Mocks: When NEXT_PUBLIC_USE_MOCKS=true, Mock Service Worker (MSW) serves API responses during development (npm run dev:mock).
- Ports (defaults): Next.js on 3000, Storybook on 6006.

High-level architecture and structure
- Framework and runtime
  - Next.js App Router with React 19 and Turbopack.
  - TypeScript-first project; strict type-checking via npm run type-check.
  - Tailwind CSS v4 pipeline via @tailwindcss/postcss.

- Source layout (see README for details)
  - src/app: App Router routes and layout.
  - src/components: Reusable UI, layout, chapter/set, and gallery components.
  - src/hooks: Custom React hooks.
  - src/lib: Cross-cutting utilities (api.ts client, types.ts interfaces, utils.ts helpers).
  - src/styles: Global styles.
  - src/stories: Storybook stories for components.
  - src/tests: Test setup and utilities.

- Data and API integration
  - Client data fetching and caching: @tanstack/react-query.
  - API base URLs configured via NEXT_PUBLIC_API_URL/CDN env vars.
  - During development, MSW can intercept and mock requests.

- UI system and documentation
  - Tailwind utility classes; prettier-plugin-tailwindcss keeps class order tidy.
  - Storybook (builder: Vite via @storybook/nextjs-vite) for isolated component development and docs.

- Testing strategy
  - Unit tests with Vitest + React Testing Library; optional axe accessibility assertions available.
  - E2E tests with Playwright targeting critical user flows; supports headless/headed/UI modes and report viewing.

- Code quality and automation
  - ESLint (eslint-config-next + plugins) for linting; Prettier for formatting.
  - Husky + lint-staged run ESLint/Prettier on staged files before commit.

Operational notes distilled from README.md
- Prereqs: Node 18+, npm, Git.
- Typical workflow
  - npm install
  - cp .env.example .env.local and set required NEXT_PUBLIC_* variables
  - npm run dev (or npm run dev:mock)
  - For E2E: ensure server running, then npm run test:e2e
  - For production: npm run build && npm run start
- Deployment: README indicates Vercel deployment with env vars managed in the Vercel dashboard and main-branch auto-deploys.

Tips for future agents in this repo
- Prefer Vitest for unit tests (scripts use Vitest); Jest is present only for compatibility with some tooling (e.g., jest-axe assertions) but tests run via Vitest.
- To target a single unit test quickly, pass a file or name filter through Vitest, e.g. npm run test -- src/components/Button.test.tsx or npm run test -- -t "renders button".
- To switch between real API and mocks, toggle NEXT_PUBLIC_USE_MOCKS and use the dev vs dev:mock scripts accordingly.
