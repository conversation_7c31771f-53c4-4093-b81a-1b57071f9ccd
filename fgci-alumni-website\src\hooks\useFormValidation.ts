'use client';

import { useState, useCallback, useRef } from 'react';
import { ValidationSchema, FormErrors } from '@/lib/types';
import { validateField, validateForm } from '@/lib/validation';

interface UseFormValidationOptions {
  validationSchema: ValidationSchema;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  debounceMs?: number;
}

export function useFormValidation<T extends Record<string, unknown>>({
  validationSchema,
  validateOnChange = false,
  validateOnBlur = true,
  debounceMs = 300,
}: UseFormValidationOptions) {
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);
  const [isValidating, setIsValidating] = useState(false);
  
  const debounceTimeouts = useRef<Record<string, NodeJS.Timeout>>({});

  // Clear debounce timeout for a field
  const clearDebounceTimeout = useCallback((fieldName: string) => {
    if (debounceTimeouts.current[fieldName]) {
      clearTimeout(debounceTimeouts.current[fieldName]);
      delete debounceTimeouts.current[fieldName];
    }
  }, []);

  // Validate a single field with optional debouncing
  const validateSingleField = useCallback((
    fieldName: keyof T,
    value: unknown,
    immediate = false
  ): Promise<string[]> => {
    return new Promise((resolve) => {
      const fieldNameStr = fieldName as string;
      
      // Clear existing timeout
      clearDebounceTimeout(fieldNameStr);
      
      const performValidation = () => {
        const fieldRules = validationSchema[fieldNameStr];
        if (!fieldRules) {
          resolve([]);
          return;
        }
        
        setIsValidating(true);
        const fieldErrors = validateField(value, fieldRules);
        
        setErrors(prev => ({
          ...prev,
          [fieldNameStr]: fieldErrors
        }));
        
        setIsValidating(false);
        resolve(fieldErrors);
      };

      if (immediate || debounceMs === 0) {
        performValidation();
      } else {
        debounceTimeouts.current[fieldNameStr] = setTimeout(performValidation, debounceMs);
      }
    });
  }, [validationSchema, debounceMs, clearDebounceTimeout]);

  // Validate all fields
  const validateAllFields = useCallback((data: T): { isValid: boolean; errors: FormErrors } => {
    setIsValidating(true);
    const result = validateForm(data, validationSchema);
    setErrors(result.errors);
    setIsValidating(false);
    return result;
  }, [validationSchema]);

  // Handle field change validation
  const handleFieldChange = useCallback((
    fieldName: keyof T,
    value: unknown,
    data: T
  ) => {
    if (validateOnChange) {
      validateSingleField(fieldName, value);
    } else {
      // Clear error if field was previously invalid
      const fieldNameStr = fieldName as string;
      if (errors[fieldNameStr]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldNameStr];
          return newErrors;
        });
      }
    }
  }, [validateOnChange, validateSingleField, errors]);

  // Handle field blur validation
  const handleFieldBlur = useCallback((
    fieldName: keyof T,
    value: unknown
  ) => {
    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [fieldName]: true
    }));

    if (validateOnBlur) {
      validateSingleField(fieldName, value, true); // Immediate validation on blur
    }
  }, [validateOnBlur, validateSingleField]);

  // Set field error manually
  const setFieldError = useCallback((fieldName: keyof T, error: string | string[]) => {
    const errorArray = Array.isArray(error) ? error : [error];
    setErrors(prev => ({
      ...prev,
      [fieldName as string]: errorArray
    }));
  }, []);

  // Clear field error
  const clearFieldError = useCallback((fieldName: keyof T) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[fieldName as string];
      return newErrors;
    });
  }, []);

  // Clear all errors
  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Reset validation state
  const reset = useCallback(() => {
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
    setIsValidating(false);
    
    // Clear all debounce timeouts
    Object.values(debounceTimeouts.current).forEach(clearTimeout);
    debounceTimeouts.current = {};
  }, []);

  // Check if form has any errors
  const hasErrors = Object.keys(errors).length > 0;
  
  // Check if form is valid (no errors and at least one field touched)
  const isValid = !hasErrors && Object.keys(touched).length > 0;

  // Get field validation state
  const getFieldState = useCallback((fieldName: keyof T) => ({
    error: errors[fieldName as string] || [],
    hasError: Boolean(errors[fieldName as string]?.length),
    touched: Boolean(touched[fieldName]),
    isValidating: isValidating,
  }), [errors, touched, isValidating]);

  return {
    // State
    errors,
    touched,
    isValidating,
    hasErrors,
    isValid,
    
    // Methods
    validateSingleField,
    validateAllFields,
    handleFieldChange,
    handleFieldBlur,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    reset,
    getFieldState,
  };
}