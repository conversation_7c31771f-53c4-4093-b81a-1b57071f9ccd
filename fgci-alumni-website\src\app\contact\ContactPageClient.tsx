'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui';
import { ContactForm } from '@/components/forms/ContactForm';
import { Breadcrumbs, StructuredData } from '@/components/seo';
import { SITE_CONFIG } from '@/lib/seo';
import { 
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function ContactPageClient() {
  // Structured data for contact page
  const contactStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'ContactPage',
    name: 'Contact FGC Ikom Alumni Association',
    description: 'Get in touch with the FGC Ikom Alumni Association for membership, events, or general inquiries.',
    url: 'https://www.fgcikomalumni.org.ng/contact',
    mainEntity: {
      '@type': 'Organization',
      name: SITE_CONFIG.name,
      contactPoint: {
        '@type': 'ContactPoint',
        telephone: SITE_CONFIG.contact.phone,
        email: SITE_CONFIG.contact.email,
        contactType: 'customer service',
        availableLanguage: 'English'
      }
    }
  };

  return (
    <>
      <StructuredData data={contactStructuredData} id="contact-page" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <div className="bg-white border-b">
          <div className="container-custom py-12">
            <Breadcrumbs className="mb-6" />
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
                Contact Us
              </h1>
              <p className="body-large text-gray-600">
                We'd love to hear from you! Whether you have questions about membership, 
                events, or just want to reconnect with the FGC Ikom alumni community, 
                we're here to help.
              </p>
            </div>
          </div>
        </div>

        <div className="container-custom py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card variant="elevated">
                <CardHeader>
                  <CardTitle>Send us a Message</CardTitle>
                </CardHeader>
                <CardContent>
                  <ContactForm />
                </CardContent>
              </Card>
            </div>

            {/* Contact Information Sidebar */}
            <div className="space-y-6">
              {/* Contact Details */}
              <Card variant="elevated">
                <CardHeader>
                  <CardTitle>Get in Touch</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <EnvelopeIcon className="w-5 h-5 text-maroon-700 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-900">Email</p>
                      <a 
                        href={`mailto:${SITE_CONFIG.contact.email}`}
                        className="text-maroon-700 hover:text-maroon-800 transition-colors"
                      >
                        {SITE_CONFIG.contact.email}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <PhoneIcon className="w-5 h-5 text-maroon-700 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-900">Phone</p>
                      <a 
                        href={`tel:${SITE_CONFIG.contact.phone}`}
                        className="text-maroon-700 hover:text-maroon-800 transition-colors"
                      >
                        {SITE_CONFIG.contact.phone}
                      </a>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <MapPinIcon className="w-5 h-5 text-maroon-700 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-900">Address</p>
                      <p className="text-gray-600">
                        FGC Ikom Alumni Association<br />
                        Lagos Chapter Office<br />
                        Lagos, Nigeria
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <ClockIcon className="w-5 h-5 text-maroon-700 mt-1 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-900">Response Time</p>
                      <p className="text-gray-600">
                        We typically respond within<br />
                        24-48 hours
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Links */}
              <Card variant="elevated">
                <CardHeader>
                  <CardTitle>Quick Links</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <a 
                    href="/chapters" 
                    className="block text-maroon-700 hover:text-maroon-800 transition-colors"
                  >
                    Find Your Chapter
                  </a>
                  <a 
                    href="/events" 
                    className="block text-maroon-700 hover:text-maroon-800 transition-colors"
                  >
                    Upcoming Events
                  </a>
                  <a 
                    href="/excobot" 
                    className="block text-maroon-700 hover:text-maroon-800 transition-colors"
                  >
                    Alumni Directory
                  </a>
                  <a 
                    href="/chapter-excos" 
                    className="block text-maroon-700 hover:text-maroon-800 transition-colors"
                  >
                    Chapter Executives
                  </a>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}