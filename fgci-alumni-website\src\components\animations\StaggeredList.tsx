'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';
import { useReducedMotion } from '@/hooks/useReducedMotion';

interface StaggeredListProps {
  children: ReactNode[];
  className?: string;
  staggerDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

const reducedMotionContainer = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: {
      staggerChildren: 0.05,
      delayChildren: 0.05,
    },
  },
};

const getItemVariants = (direction: 'up' | 'down' | 'left' | 'right') => {
  const directions = {
    up: { y: 20 },
    down: { y: -20 },
    left: { x: 20 },
    right: { x: -20 },
  };

  return {
    hidden: {
      opacity: 0,
      ...directions[direction],
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 12,
      },
    },
  };
};

const reducedMotionItem = {
  hidden: { opacity: 0 },
  visible: { 
    opacity: 1,
    transition: { duration: 0.1 },
  },
};

export function StaggeredList({
  children,
  className,
  staggerDelay = 0.1,
  direction = 'up',
}: StaggeredListProps) {
  const prefersReducedMotion = useReducedMotion();

  const containerVars = prefersReducedMotion ? reducedMotionContainer : containerVariants;
  const itemVars = prefersReducedMotion ? reducedMotionItem : getItemVariants(direction);

  // Update stagger delay if provided
  if (staggerDelay !== 0.1) {
    containerVars.visible.transition.staggerChildren = staggerDelay;
  }

  return (
    <motion.div
      className={className}
      variants={containerVars}
      initial="hidden"
      animate="visible"
    >
      {children.map((child, index) => (
        <motion.div key={index} variants={itemVars}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  );
}

// Preset components for common list patterns
export function StaggeredGrid({
  children,
  className,
  columns = 3,
}: {
  children: ReactNode[];
  className?: string;
  columns?: number;
}) {
  const gridClass = `grid grid-cols-1 md:grid-cols-${columns} gap-6`;
  
  return (
    <StaggeredList className={`${gridClass} ${className || ''}`}>
      {children}
    </StaggeredList>
  );
}

export function StaggeredCards({
  children,
  className,
}: {
  children: ReactNode[];
  className?: string;
}) {
  return (
    <StaggeredList 
      className={`space-y-6 ${className || ''}`}
      direction="up"
      staggerDelay={0.15}
    >
      {children}
    </StaggeredList>
  );
}