'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'tel' | 'number' | 'password' | 'url';
  value: string | number;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  touched?: boolean;
  min?: number;
  max?: number;
  className?: string;
  helpText?: string;
  autoComplete?: string;
}

export function FormField({
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  errors = [],
  touched = false,
  min,
  max,
  className,
  helpText,
  autoComplete,
}: FormFieldProps) {
  const hasError = (errors && errors.length > 0) || !!touched;
  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;
  const helpId = `${fieldId}-help`;

  return (
    <div className={cn('space-y-2', className)}>
      <label 
        htmlFor={fieldId} 
        className="block text-sm font-medium text-gray-700"
      >
        {label}
        {required && (
          <>
            {' '}
            <span className="text-red-500 ml-1">*</span>
            <span className="sr-only" aria-label="required">required</span>
          </>
        )}
      </label>
      
      <input
        type={type}
        id={fieldId}
        name={name}
        value={value}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        min={min}
        max={max}
        autoComplete={autoComplete}
        aria-label={required ? `${label} *` : label}
        className={cn(
          'w-full px-3 py-2 border rounded-lg transition-colors',
          'focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500',
          'disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed',
          hasError
            ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
            : 'border-gray-300'
        )}
        aria-invalid={hasError}
        aria-describedby={cn(
          hasError && errorId,
          helpText && helpId
        )}
      />
      
      {helpText && !hasError && (
        <p id={helpId} className="text-sm text-gray-600">
          {helpText}
        </p>
      )}
      
      {hasError && (
        <div id={errorId} className="space-y-1" role="alert">
          {errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600">
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}