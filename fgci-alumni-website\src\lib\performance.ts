/**
 * Performance monitoring utilities for Core Web Vitals and performance budgets
 */

export interface PerformanceMetrics {
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  ttfb: number; // Time to First Byte
}

export interface PerformanceBudget {
  fcp: number;
  lcp: number;
  fid: number;
  cls: number;
  ttfb: number;
}

// Performance budgets based on Core Web Vitals thresholds
const DEFAULT_BUDGET: PerformanceBudget = {
  fcp: 1800, // ms
  lcp: 2500, // ms
  fid: 100,  // ms
  cls: 0.1,  // score
  ttfb: 600  // ms
};

/**
 * Measure Core Web Vitals using Performance Observer API
 */
export async function measureWebVitals(): Promise<PerformanceMetrics> {
  return new Promise((resolve) => {
    const metrics: Partial<PerformanceMetrics> = {};
    let metricsCollected = 0;
    const totalMetrics = 5;

    const checkComplete = () => {
      if (metricsCollected === totalMetrics) {
        resolve(metrics as PerformanceMetrics);
      }
    };

    // First Contentful Paint
    const fcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        metrics.fcp = fcpEntry.startTime;
        metricsCollected++;
        checkComplete();
        fcpObserver.disconnect();
      }
    });

    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        metrics.lcp = lastEntry.startTime;
        metricsCollected++;
        checkComplete();
      }
    });

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const fidEntry = entries[0];
      if (fidEntry) {
        metrics.fid = fidEntry.processingStart - fidEntry.startTime;
        metricsCollected++;
        checkComplete();
        fidObserver.disconnect();
      }
    });

    // Cumulative Layout Shift
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0;
      const entries = list.getEntries();
      
      for (const entry of entries) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      }
      
      metrics.cls = clsValue;
      metricsCollected++;
      checkComplete();
    });

    // Time to First Byte
    const navigationEntries = performance.getEntriesByType('navigation');
    if (navigationEntries.length > 0) {
      const navEntry = navigationEntries[0] as PerformanceNavigationTiming;
      metrics.ttfb = navEntry.responseStart - navEntry.requestStart;
      metricsCollected++;
      checkComplete();
    }

    try {
      fcpObserver.observe({ entryTypes: ['paint'] });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      fidObserver.observe({ entryTypes: ['first-input'] });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Performance Observer not supported:', error);
      // Fallback values
      resolve({
        fcp: 0,
        lcp: 0,
        fid: 0,
        cls: 0,
        ttfb: metrics.ttfb || 0
      });
    }

    // Timeout after 10 seconds
    setTimeout(() => {
      resolve({
        fcp: metrics.fcp || 0,
        lcp: metrics.lcp || 0,
        fid: metrics.fid || 0,
        cls: metrics.cls || 0,
        ttfb: metrics.ttfb || 0
      });
    }, 10000);
  });
}

/**
 * Check if performance metrics meet budget requirements
 */
export function checkPerformanceBudget(
  metrics: PerformanceMetrics,
  budget: PerformanceBudget = DEFAULT_BUDGET
): { passed: boolean; violations: string[] } {
  const violations: string[] = [];

  if (metrics.fcp > budget.fcp) {
    violations.push(`FCP: ${metrics.fcp.toFixed(0)}ms > ${budget.fcp}ms`);
  }

  if (metrics.lcp > budget.lcp) {
    violations.push(`LCP: ${metrics.lcp.toFixed(0)}ms > ${budget.lcp}ms`);
  }

  if (metrics.fid > budget.fid) {
    violations.push(`FID: ${metrics.fid.toFixed(0)}ms > ${budget.fid}ms`);
  }

  if (metrics.cls > budget.cls) {
    violations.push(`CLS: ${metrics.cls.toFixed(3)} > ${budget.cls}`);
  }

  if (metrics.ttfb > budget.ttfb) {
    violations.push(`TTFB: ${metrics.ttfb.toFixed(0)}ms > ${budget.ttfb}ms`);
  }

  return {
    passed: violations.length === 0,
    violations
  };
}

/**
 * Get memory usage information (Chrome only)
 */
export function getMemoryUsage(): MemoryInfo | null {
  if ('memory' in performance) {
    return (performance as any).memory;
  }
  return null;
}

/**
 * Monitor resource loading performance
 */
export function getResourceTimings(): PerformanceResourceTiming[] {
  return performance.getEntriesByType('resource') as PerformanceResourceTiming[];
}

/**
 * Calculate performance score based on Core Web Vitals
 */
export function calculatePerformanceScore(metrics: PerformanceMetrics): number {
  let score = 100;

  // FCP scoring (0-25 points)
  if (metrics.fcp > 3000) score -= 25;
  else if (metrics.fcp > 1800) score -= Math.round((metrics.fcp - 1800) / 1200 * 25);

  // LCP scoring (0-25 points)
  if (metrics.lcp > 4000) score -= 25;
  else if (metrics.lcp > 2500) score -= Math.round((metrics.lcp - 2500) / 1500 * 25);

  // FID scoring (0-25 points)
  if (metrics.fid > 300) score -= 25;
  else if (metrics.fid > 100) score -= Math.round((metrics.fid - 100) / 200 * 25);

  // CLS scoring (0-25 points)
  if (metrics.cls > 0.25) score -= 25;
  else if (metrics.cls > 0.1) score -= Math.round((metrics.cls - 0.1) / 0.15 * 25);

  return Math.max(0, score);
}

/**
 * Log performance metrics to analytics
 */
export function logPerformanceMetrics(metrics: PerformanceMetrics): void {
  // In a real application, you would send this to your analytics service
  if (typeof window !== 'undefined' && 'gtag' in window) {
    (window as any).gtag('event', 'web_vitals', {
      event_category: 'Performance',
      fcp: Math.round(metrics.fcp),
      lcp: Math.round(metrics.lcp),
      fid: Math.round(metrics.fid),
      cls: Math.round(metrics.cls * 1000) / 1000,
      ttfb: Math.round(metrics.ttfb)
    });
  }

  // Console logging for development
  if (process.env.NODE_ENV === 'development') {
    console.table({
      'First Contentful Paint': `${metrics.fcp.toFixed(0)}ms`,
      'Largest Contentful Paint': `${metrics.lcp.toFixed(0)}ms`,
      'First Input Delay': `${metrics.fid.toFixed(0)}ms`,
      'Cumulative Layout Shift': metrics.cls.toFixed(3),
      'Time to First Byte': `${metrics.ttfb.toFixed(0)}ms`,
      'Performance Score': `${calculatePerformanceScore(metrics)}/100`
    });
  }
}