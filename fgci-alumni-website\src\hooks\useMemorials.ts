'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Memorial, CondolenceFormData, ApiResponse } from '@/lib/types';
import { apiClient } from '@/lib/api-client';

// Query keys
export const memorialKeys = {
  all: ['memorials'] as const,
  lists: () => [...memorialKeys.all, 'list'] as const,
  list: (filters?: Record<string, unknown>) => [...memorialKeys.lists(), { filters }] as const,
  details: () => [...memorialKeys.all, 'detail'] as const,
  detail: (id: string) => [...memorialKeys.details(), id] as const,
  condolences: (id: string) => [...memorialKeys.detail(id), 'condolences'] as const,
};

// Fetch all memorials
export function useMemorials(filters?: Record<string, unknown>) {
  return useQuery({
    queryKey: memorialKeys.list(filters),
    queryFn: async (): Promise<Memorial[]> => {
      const response = await apiClient.get<ApiResponse<Memorial[]>>('/api/memorials', {
        params: filters,
      });
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Fetch single memorial
export function useMemorial(id: string) {
  return useQuery({
    queryKey: memorialKeys.detail(id),
    queryFn: async (): Promise<Memorial> => {
      const response = await apiClient.get<ApiResponse<Memorial>>(`/api/memorials/${id}`);
      return response.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

// Submit condolence message
export function useSubmitCondolence() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ memorialId, condolence }: { memorialId: string; condolence: CondolenceFormData }) => {
      const response = await apiClient.post<ApiResponse<{ id: string }>>(
        `/api/memorials/${memorialId}/condolences`,
        condolence
      );
      return response.data;
    },
    onSuccess: (_, { memorialId }) => {
      // Invalidate memorial queries to refetch updated data
      queryClient.invalidateQueries({ queryKey: memorialKeys.detail(memorialId) });
      queryClient.invalidateQueries({ queryKey: memorialKeys.condolences(memorialId) });
    },
    onError: (error) => {
      console.error('Failed to submit condolence:', error);
    },
  });
}

// Fetch condolences for a memorial
export function useCondolences(memorialId: string) {
  return useQuery({
    queryKey: memorialKeys.condolences(memorialId),
    queryFn: async () => {
      const response = await apiClient.get<ApiResponse<Memorial['condolences']>>(
        `/api/memorials/${memorialId}/condolences`
      );
      return response.data;
    },
    enabled: !!memorialId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Search memorials
export function useSearchMemorials(query: string, filters?: Record<string, unknown>) {
  return useQuery({
    queryKey: [...memorialKeys.lists(), 'search', { query, filters }],
    queryFn: async (): Promise<Memorial[]> => {
      const response = await apiClient.get<ApiResponse<Memorial[]>>('/api/memorials/search', {
        params: { q: query, ...filters },
      });
      return response.data;
    },
    enabled: query.length >= 2, // Only search when query is at least 2 characters
    staleTime: 30 * 1000, // 30 seconds for search results
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Memorial statistics
export function useMemorialStats() {
  return useQuery({
    queryKey: [...memorialKeys.all, 'stats'],
    queryFn: async () => {
      const response = await apiClient.get<ApiResponse<{
        total: number;
        byDecade: Record<string, number>;
        byChapter: Record<string, number>;
        recentCount: number;
      }>>('/api/memorials/stats');
      return response.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}