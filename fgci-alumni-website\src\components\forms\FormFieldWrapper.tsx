'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface FormFieldWrapperProps {
  children: React.ReactNode;
  label: string;
  name: string;
  required?: boolean;
  errors?: string[];
  touched?: boolean;
  helpText?: string;
  className?: string;
  labelClassName?: string;
  showOptionalText?: boolean;
  isLoading?: boolean;
}

export function FormFieldWrapper({
  children,
  label,
  name,
  required = false,
  errors = [],
  touched = false,
  helpText,
  className,
  labelClassName,
  showOptionalText = true,
  isLoading = false,
}: FormFieldWrapperProps) {
  const hasError = touched && errors.length > 0;
  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;
  const helpId = `${fieldId}-help`;

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between">
        <label 
          htmlFor={fieldId} 
          className={cn(
            'block text-sm font-medium text-gray-700',
            labelClassName
          )}
        >
          {label}
          {required && (
            <span 
              className="text-red-500 ml-1" 
              aria-label="required field"
              title="This field is required"
            >
              *
            </span>
          )}
        </label>
        
        {!required && showOptionalText && (
          <span className="text-sm text-gray-500 italic">
            Optional
          </span>
        )}
        
        {isLoading && (
          <div className="flex items-center text-sm text-gray-500">
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-maroon-600 mr-1"></div>
            Validating...
          </div>
        )}
      </div>
      
      <div className="relative">
        {React.cloneElement(children as React.ReactElement, {
          id: fieldId,
          name,
          'aria-invalid': hasError,
          'aria-describedby': cn(
            hasError && errorId,
            helpText && helpId
          ),
        })}
        
        {hasError && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg 
              className="h-5 w-5 text-red-500" 
              fill="currentColor" 
              viewBox="0 0 20 20"
              aria-hidden="true"
            >
              <path 
                fillRule="evenodd" 
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
                clipRule="evenodd" 
              />
            </svg>
          </div>
        )}
      </div>
      
      {helpText && !hasError && (
        <p id={helpId} className="text-sm text-gray-600">
          {helpText}
        </p>
      )}
      
      {hasError && (
        <div id={errorId} className="space-y-1" role="alert">
          {errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-start">
              <svg 
                className="h-4 w-4 text-red-500 mr-1 mt-0.5 flex-shrink-0" 
                fill="currentColor" 
                viewBox="0 0 20 20"
                aria-hidden="true"
              >
                <path 
                  fillRule="evenodd" 
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" 
                  clipRule="evenodd" 
                />
              </svg>
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}