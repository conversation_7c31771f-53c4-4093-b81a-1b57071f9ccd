'use client';

import React from 'react';
import { useSkipLinks } from '@/hooks/useKeyboardNavigation';
import { cn } from '@/lib/utils';

interface SkipNavProps {
  className?: string;
}

/**
 * Skip navigation component for keyboard accessibility
 * Provides quick navigation to main content areas
 */
export function SkipNav({ className }: SkipNavProps) {
  const { skipToContent, skipToNavigation } = useSkipLinks();

  return (
    <div className={cn("sr-only focus-within:not-sr-only", className)}>
      <div className="fixed top-0 left-0 z-[9999] flex gap-2 p-2 bg-white border-b border-gray-200 shadow-lg">
        <button
          onClick={skipToContent}
          className="skip-link px-4 py-2 bg-maroon-700 text-white rounded-md text-sm font-medium hover:bg-maroon-800 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2"
        >
          Skip to main content
        </button>
        <button
          onClick={skipToNavigation}
          className="skip-link px-4 py-2 bg-maroon-700 text-white rounded-md text-sm font-medium hover:bg-maroon-800 focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2"
        >
          Skip to navigation
        </button>
      </div>
    </div>
  );
}