'use client';

import { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { Button } from '@/components/ui';
import { cn } from '@/lib/utils';

interface EventFiltersProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedStatus: string;
  onStatusChange: (status: string) => void;
  selectedType: string;
  onTypeChange: (type: string) => void;
  onClearFilters: () => void;
  hasActiveFilters: boolean;
}

const eventStatuses = [
  { value: 'all', label: 'All Events' },
  { value: 'upcoming', label: 'Upcoming' },
  { value: 'ongoing', label: 'Ongoing' },
  { value: 'completed', label: 'Completed' },
  { value: 'cancelled', label: 'Cancelled' }
];

const eventTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'meeting', label: 'Meetings' },
  { value: 'event', label: 'Events' },
  { value: 'fundraiser', label: 'Fundraisers' },
  { value: 'social', label: 'Social' },
  { value: 'memorial', label: 'Memorial' }
];

export function EventFilters({
  searchQuery,
  onSearchChange,
  selectedStatus,
  onStatusChange,
  selectedType,
  onTypeChange,
  onClearFilters,
  hasActiveFilters
}: EventFiltersProps) {
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  return (
    <div className="bg-white border-b">
      <div className="container-custom py-6">
        <div className="flex flex-col gap-4">
          {/* Search Bar */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search events by title, location, or organizer..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
            />
          </div>

          {/* Desktop Filters */}
          <div className="hidden lg:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <FunnelIcon className="w-5 h-5 text-gray-400" />
                <span className="text-sm font-medium text-gray-700">Status:</span>
                <div className="flex gap-2">
                  {eventStatuses.map((status) => (
                    <button
                      key={status.value}
                      onClick={() => onStatusChange(status.value)}
                      className={cn(
                        'px-3 py-1.5 text-sm rounded-full border transition-colors',
                        selectedStatus === status.value
                          ? 'bg-maroon-700 text-white border-maroon-700'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-maroon-300'
                      )}
                    >
                      {status.label}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">Type:</span>
                <div className="flex gap-2">
                  {eventTypes.map((type) => (
                    <button
                      key={type.value}
                      onClick={() => onTypeChange(type.value)}
                      className={cn(
                        'px-3 py-1.5 text-sm rounded-full border transition-colors',
                        selectedType === type.value
                          ? 'bg-gold-600 text-white border-gold-600'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-gold-300'
                      )}
                    >
                      {type.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                className="flex items-center gap-2"
              >
                <XMarkIcon className="w-4 h-4" />
                Clear Filters
              </Button>
            )}
          </div>

          {/* Mobile Filter Toggle */}
          <div className="lg:hidden flex items-center justify-between">
            <button
              onClick={() => setShowMobileFilters(!showMobileFilters)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <FunnelIcon className="w-5 h-5" />
              <span>Filters</span>
              {hasActiveFilters && (
                <span className="bg-maroon-700 text-white text-xs px-2 py-0.5 rounded-full">
                  Active
                </span>
              )}
            </button>

            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={onClearFilters}
                className="flex items-center gap-2"
              >
                <XMarkIcon className="w-4 h-4" />
                Clear
              </Button>
            )}
          </div>

          {/* Mobile Filters */}
          {showMobileFilters && (
            <div className="lg:hidden space-y-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <div className="flex flex-wrap gap-2">
                  {eventStatuses.map((status) => (
                    <button
                      key={status.value}
                      onClick={() => onStatusChange(status.value)}
                      className={cn(
                        'px-3 py-1.5 text-sm rounded-full border transition-colors',
                        selectedStatus === status.value
                          ? 'bg-maroon-700 text-white border-maroon-700'
                          : 'bg-white text-gray-600 border-gray-300'
                      )}
                    >
                      {status.label}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Type
                </label>
                <div className="flex flex-wrap gap-2">
                  {eventTypes.map((type) => (
                    <button
                      key={type.value}
                      onClick={() => onTypeChange(type.value)}
                      className={cn(
                        'px-3 py-1.5 text-sm rounded-full border transition-colors',
                        selectedType === type.value
                          ? 'bg-gold-600 text-white border-gold-600'
                          : 'bg-white text-gray-600 border-gray-300'
                      )}
                    >
                      {type.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}