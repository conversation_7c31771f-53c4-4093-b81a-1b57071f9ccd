import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FormFileUpload } from '../FormFileUpload';

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn(() => 'mock-url');

describe('FormFileUpload', () => {
  const defaultProps = {
    label: 'Upload File',
    name: 'file',
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders with basic props', () => {
    render(<FormFileUpload {...defaultProps} />);
    
    expect(screen.getByLabelText('Upload File')).toBeInTheDocument();
    expect(screen.getByText('Click to upload')).toBeInTheDocument();
  });

  it('shows required indicator when required', () => {
    render(<FormFileUpload {...defaultProps} required />);
    
    expect(screen.getByLabelText('Upload File *')).toBeInTheDocument();
  });

  it('displays help text when provided', () => {
    const helpText = 'Upload your document here';
    render(<FormFileUpload {...defaultProps} helpText={helpText} />);
    
    expect(screen.getByText(helpText)).toBeInTheDocument();
  });

  it('shows error messages when field has errors and is touched', () => {
    const errors = ['File is required', 'File size too large'];
    render(
      <FormFileUpload 
        {...defaultProps} 
        errors={errors} 
        touched={true} 
      />
    );
    
    errors.forEach(error => {
      expect(screen.getByText(error)).toBeInTheDocument();
    });
  });

  it('calls onChange when file is selected', async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();
    
    render(<FormFileUpload {...defaultProps} onChange={onChange} />);
    
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const input = screen.getByLabelText('Upload File');
    
    await user.upload(input, file);
    
    expect(onChange).toHaveBeenCalledWith(file);
  });

  it('displays selected file information', () => {
    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
    
    render(<FormFileUpload {...defaultProps} value={file} />);
    
    expect(screen.getByText('test.txt')).toBeInTheDocument();
    expect(screen.getByText(/Bytes/)).toBeInTheDocument(); // File size
  });

  it('shows image preview for image files', () => {
    const imageFile = new File(['image content'], 'test.jpg', { type: 'image/jpeg' });
    
    render(<FormFileUpload {...defaultProps} value={imageFile} preview={true} />);
    
    const preview = screen.getByAltText('Preview');
    expect(preview).toBeInTheDocument();
    expect(preview).toHaveAttribute('src', 'mock-url');
  });

  it('allows file removal', async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    render(<FormFileUpload {...defaultProps} value={file} onChange={onChange} />);
    
    const removeButton = screen.getByLabelText('Remove file');
    await user.click(removeButton);
    
    expect(onChange).toHaveBeenCalledWith(null);
  });

  it('handles drag and drop', async () => {
    const onChange = vi.fn();
    
    render(<FormFileUpload {...defaultProps} onChange={onChange} />);
    
    const dropZone = screen.getByText('Click to upload').closest('div');
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    
    fireEvent.dragOver(dropZone!);
    fireEvent.drop(dropZone!, {
      dataTransfer: {
        files: [file],
      },
    });
    
    expect(onChange).toHaveBeenCalledWith(file);
  });

  it('shows drag over state', () => {
    render(<FormFileUpload {...defaultProps} />);
    
    const dropZone = screen.getByText('Click to upload').closest('div');
    
    fireEvent.dragOver(dropZone!);
    
    expect(dropZone).toHaveClass('border-maroon-400', 'bg-maroon-50');
  });

  it('disables interaction when disabled', () => {
    render(<FormFileUpload {...defaultProps} disabled />);
    
    const dropZone = screen.getByText('Click to upload').closest('div');
    expect(dropZone).toHaveClass('cursor-not-allowed', 'opacity-50');
  });

  it('shows accept and maxSize information', () => {
    render(
      <FormFileUpload 
        {...defaultProps} 
        accept="image/*" 
        maxSize={5}
      />
    );
    
    expect(screen.getByText(/Accepted formats: image\/\*/)).toBeInTheDocument();
    expect(screen.getByText(/Max size: 5MB/)).toBeInTheDocument();
  });

  it('formats file size correctly', () => {
    const largeFile = new File(['x'.repeat(1024 * 1024)], 'large.txt', { type: 'text/plain' });
    
    render(<FormFileUpload {...defaultProps} value={largeFile} />);
    
    expect(screen.getByText(/MB/)).toBeInTheDocument();
  });

  it('sets proper accessibility attributes', () => {
    const errors = ['File is required'];
    render(
      <FormFileUpload 
        {...defaultProps} 
        errors={errors} 
        touched={true}
        helpText="Upload help text"
      />
    );
    
    const input = screen.getByLabelText('Upload File');
    
    expect(input).toHaveAttribute('aria-invalid', 'true');
    expect(input).toHaveAttribute('aria-describedby');
  });

  it('prevents file selection when size exceeds limit', async () => {
    const user = userEvent.setup();
    const onChange = vi.fn();
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(<FormFileUpload {...defaultProps} onChange={onChange} maxSize={1} />);
    
    // Create a file larger than 1MB
    const largeFile = new File(['x'.repeat(2 * 1024 * 1024)], 'large.txt', { type: 'text/plain' });
    const input = screen.getByLabelText('Upload File');
    
    await user.upload(input, largeFile);
    
    expect(consoleSpy).toHaveBeenCalledWith('File size exceeds 1MB limit');
    expect(onChange).not.toHaveBeenCalled();
    
    consoleSpy.mockRestore();
  });
});