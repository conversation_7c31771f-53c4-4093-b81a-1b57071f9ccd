import { ValidationSchema, FormErrors, ContactFormData } from '@/lib/types';

// Validation rule types
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  custom?: (value: any) => string | null;
}

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone validation regex (supports various formats)
const PHONE_REGEX = /^[\+]?[1-9][\d]{6,15}$/;


// Humanize field names like "graduationYear" -> "Graduation Year"
function humanizeField(name: string): string {
  const withSpaces = name.replace(/([A-Z])/g, ' $1').trim();
  return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1);
}

/**
 * Validates a single field against its validation rules
 */
export const validateField = (
  value: any,
  rules: ValidationRule,
  fieldName?: string
): string[] => {
  const errors: string[] = [];

  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    const label = fieldName ? humanizeField(fieldName) : 'This field';
    errors.push(`${label} is required`);
    return errors; // Return early if required field is empty
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return errors;
  }

  const stringValue = String(value).trim();

  // Min length validation
  if (rules.minLength && stringValue.length < rules.minLength) {
    errors.push(`Must be at least ${rules.minLength} characters long`);
  }

  // Max length validation
  if (rules.maxLength && stringValue.length > rules.maxLength) {
    errors.push(`${fieldName ? humanizeField(fieldName) : 'Field'} must be no more than ${rules.maxLength} characters`);
  }

  // Email validation
  if (rules.email && !EMAIL_REGEX.test(stringValue)) {
    errors.push('Please enter a valid email address');
  }

  // Phone validation
  if (rules.phone && !PHONE_REGEX.test(stringValue.replace(/[\s\-\(\)]/g, ''))) {
    errors.push('Please enter a valid phone number');
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    errors.push(`${fieldName ? humanizeField(fieldName) : 'Field'} format is invalid`);
  }

  // Custom validation
  if (rules.custom) {
    const customError = rules.custom(value);
    if (customError) {
      errors.push(customError);
    }
  }

  return errors;
};

/**
 * Validates an entire form against a validation schema
 */
export const validateForm = <T extends Record<string, any>>(
  data: T,
  schema: ValidationSchema<T>
): { isValid: boolean; errors: FormErrors } => {
  const errors: FormErrors = {};

  for (const [fieldName, rules] of Object.entries(schema)) {
    // Use humanized field labels in errors to match UI and tests
    const fieldErrors = validateField((data as any)[fieldName], rules as ValidationRule, fieldName);
    if (fieldErrors.length > 0) {
      errors[fieldName] = fieldErrors;
    }
  }

  const isValid = Object.keys(errors).length === 0;
  return { isValid, errors };
};

/**
 * Contact form validation schema
 */
export const contactFormSchema: ValidationSchema<ContactFormData> = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  email: {
    required: true,
    email: true,
    maxLength: 255,
  },
  phone: {
    required: false,
    phone: true,
  },
  subject: {
    required: true,
    minLength: 5,
    maxLength: 200,
  },
  message: {
    required: true,
    minLength: 10,
    maxLength: 2000,
  },
  graduationYear: {
    required: false,
    custom: (value: number) => {
      if (value === undefined || value === null || value === ('' as any)) return null;
      const year = Number(value);
      const currentYear = new Date().getFullYear();
      if (Number.isNaN(year) || year < 1970 || year > currentYear) {
        return 'Year must be between 1970';
      }
      return null;
    },
  },
  inquiryType: {
    required: true,
    custom: (value: string) => {
      const validTypes = ['general', 'membership', 'events', 'chapters', 'donations', 'website', 'other'];
      return validTypes.includes(value) ? null : 'This field is required';
    },
  },
};

/**
 * Alumni search validation schema
 */
export const alumniSearchSchema: ValidationSchema<{
  query: string;
  chapter?: string;
  graduationYear?: number;
  role?: string;
}> = {
  query: {
    required: false,
    minLength: 2,
    maxLength: 100,
  },
  chapter: {
    required: false,
  },
  graduationYear: {
    required: false,
    custom: (value: number) => {
      if (value && (value < 1950 || value > new Date().getFullYear() + 10)) {
        return 'Please enter a valid graduation year';
      }
      return null;
    },
  },
  role: {
    required: false,
  },
};

/**
 * Memorial condolence validation schema
 */
export const condolenceSchema: ValidationSchema<{
  name: string;
  message: string;
  relationship?: string;
}> = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  message: {
    required: true,
    minLength: 10,
    maxLength: 1000,
  },
  relationship: {
    required: false,
    maxLength: 100,
  },
};

/**
 * Utility function to check if form has errors
 */
export const hasFormErrors = (errors: FormErrors): boolean => {
  return Object.keys(errors).length > 0;
};

/**
 * Utility function to get first error message for a field
 */
export const getFirstError = (errors: FormErrors, fieldName: string): string | null => {
  const fieldErrors = errors[fieldName];
  return fieldErrors && fieldErrors.length > 0 ? fieldErrors[0] : null;
};

/**
 * Utility function to get all error messages as a flat array
 */
export const getAllErrors = (errors: FormErrors): string[] => {
  return Object.values(errors).flat();
};

/**
 * Sanitize input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove < and > characters
    .trim();
};

/**
 * Validate file upload
 */
export const validateFile = (
  file: File,
  options: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
    maxFiles?: number;
  } = {}
): string[] => {
  const errors: string[] = [];
  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/*'], maxFiles = 1 } = options;

  // File size validation
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // File type validation
  const isAllowedType = allowedTypes.some(type => {
    if (type.endsWith('/*')) {
      return file.type.startsWith(type.replace('/*', '/'));
    }
    return file.type === type;
  });

  if (!isAllowedType) {
    errors.push(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }

  return errors;
};

// Export alias for condolence form schema
export const condolenceFormSchema = condolenceSchema;
