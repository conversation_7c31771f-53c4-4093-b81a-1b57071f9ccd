'use client';

import React, { useContext } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useReducedMotion } from '@/hooks/useReducedMotion';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  children: React.ReactNode;
}

const CardContext = React.createContext<{ variant: 'default'|'elevated'|'outlined'; hover: boolean }>({ variant: 'default', hover: false });

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ 
    className, 
    variant = 'default', 
    padding = 'md', 
    hover = false, 
    children, 
    ...props 
  }, ref) => {
    const prefersReducedMotion = useReducedMotion();
    
    const baseClasses = 'card card-base';
    
    const variantClasses = {
      default: 'bg-white border border-gray-200',
      elevated: 'bg-white shadow-lg border border-gray-100 card-elevated',
      outlined: 'bg-white border-2 border-gray-300 card-outlined'
    };

    const paddingClasses = {
      sm: 'p-4',
      md: 'p-6',
      lg: 'p-8'
    };

    // Animation variants for hover effects
    const cardVariants = {
      hover: prefersReducedMotion ? {} : {
        y: -6,
        boxShadow: '0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        borderColor: 'rgba(212, 160, 23, 0.3)',
        transition: {
          type: 'spring',
          stiffness: 300,
          damping: 20,
        },
      },
      tap: prefersReducedMotion ? {} : {
        y: -2,
        transition: {
          type: 'spring',
          stiffness: 400,
          damping: 17,
        },
      },
    };

    if (hover) {
      const MotionDiv = motion.div;
      return (
        <CardContext.Provider value={{ variant, hover }}>
          <MotionDiv
            className={cn(
              baseClasses,
              variantClasses[variant],
              paddingClasses[padding],
              'cursor-pointer card-hover',
              className
            )}
            ref={ref}
            tabIndex={-1}
            variants={cardVariants}
            whileHover="hover"
            whileTap="tap"
            {...props}
          >
            {React.isValidElement(children) && (children as any).type === 'div' ? (
              React.cloneElement(children as any, {
                className: cn('card', variantClasses[variant], hover && 'card-hover', className, (children as any).props?.className),
              })
            ) : (
              <div className={cn('card', variantClasses[variant], hover && 'card-hover', className)}>
                {children}
              </div>
            )}
          </MotionDiv>
        </CardContext.Provider>
      );
    }

    return (
      <CardContext.Provider value={{ variant, hover }}>
        <div
          className={cn(
            baseClasses,
            variantClasses[variant],
            paddingClasses[padding],
            className
          )}
          ref={ref}
          {...props}
        >
          {React.isValidElement(children) && (children as any).type === 'div' ? (
            React.cloneElement(children as any, {
              className: cn('card', variantClasses[variant], hover && 'card-hover', className, (children as any).props?.className),
            })
          ) : (
            <div className={cn('card', variantClasses[variant], hover && 'card-hover', className)}>
              {children}
            </div>
          )}
        </div>
      </CardContext.Provider>
    );
  }
);

Card.displayName = 'Card';

// Card sub-components for better composition
const CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    const ctx = useContext(CardContext);
    return (
      <div
        ref={ref}
        className={cn(
          'card-header flex flex-col space-y-1.5 pb-4',
          ctx.variant === 'elevated' && 'card-elevated',
          ctx.hover && 'card-hover',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);
CardHeader.displayName = 'CardHeader';

interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> { as?: 'h1'|'h2'|'h3'|'h4'|'h5'|'h6' }
const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, children, as = 'h3', ...props }, ref) => {
    const HeadingTag = as as any;
    return (
      <HeadingTag
        ref={ref}
        className={cn('card-title heading-3 text-xl font-semibold leading-none tracking-tight', className)}
        {...props}
      >
        {children}
      </HeadingTag>
    );
  }
);
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<HTMLParagraphElement, React.HTMLAttributes<HTMLParagraphElement>>(
  ({ className, children, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('body-base text-gray-600', className)}
      {...props}
    >
      {children}
    </p>
  )
);
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('card-content pt-0', className)}
      {...props}
    >
      {children}
    </div>
  )
);
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('flex items-center pt-4', className)}
      {...props}
    >
      {children}
    </div>
  )
);
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };