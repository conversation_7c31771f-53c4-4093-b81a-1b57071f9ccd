'use client';

import { useEffect, useState } from 'react';
import { 
  WebVitalsMonitor, 
  WebVitalsMetrics, 
  WEB_VITALS_THRESHOLDS,
  getPerformanceRating,
  calculateWebVitalsScore,
  setupWebVitalsMonitoring
} from '@/lib/webVitals';

interface WebVitalsMonitorProps {
  showInDevelopment?: boolean;
  onMetricsUpdate?: (metrics: Partial<WebVitalsMetrics>) => void;
}

export function WebVitalsMonitorComponent({ 
  showInDevelopment = false,
  onMetricsUpdate 
}: WebVitalsMonitorProps) {
  const [metrics, setMetrics] = useState<Partial<WebVitalsMetrics>>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development if enabled
    if (process.env.NODE_ENV === 'development' && showInDevelopment) {
      setIsVisible(true);
    }

    // Setup global monitoring
    setupWebVitalsMonitoring();

    // Create monitor instance
    const monitor = new WebVitalsMonitor();
    
    monitor.onMetricsUpdate((newMetrics) => {
      setMetrics(newMetrics);
      onMetricsUpdate?.(newMetrics);
    });

    // Keyboard shortcut to toggle visibility in development
    const handleKeyPress = (event: KeyboardEvent) => {
      if (
        process.env.NODE_ENV === 'development' &&
        event.ctrlKey &&
        event.shiftKey &&
        event.key === 'P'
      ) {
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);

    return () => {
      window.removeEventListener('keydown', handleKeyPress);
    };
  }, [showInDevelopment, onMetricsUpdate]);

  if (!isVisible) return null;

  const score = calculateWebVitalsScore(metrics);

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900">Core Web Vitals</h3>
        <div className={`text-xs px-2 py-1 rounded ${
          score >= 90 ? 'bg-green-100 text-green-800' :
          score >= 75 ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          Score: {score}
        </div>
      </div>

      <div className="space-y-2">
        {Object.entries(metrics).map(([key, value]) => {
          if (value === undefined) return null;

          const threshold = WEB_VITALS_THRESHOLDS[key as keyof WebVitalsMetrics];
          const rating = getPerformanceRating(value, threshold);
          
          return (
            <div key={key} className="flex items-center justify-between text-xs">
              <span className="font-medium text-gray-700">
                {key.toUpperCase()}:
              </span>
              <div className="flex items-center gap-2">
                <span className="text-gray-900">
                  {key === 'cls' ? value.toFixed(3) : Math.round(value)}
                  {key !== 'cls' && 'ms'}
                </span>
                <div className={`w-2 h-2 rounded-full ${
                  rating === 'good' ? 'bg-green-500' :
                  rating === 'needs-improvement' ? 'bg-yellow-500' :
                  'bg-red-500'
                }`} />
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-3 pt-2 border-t border-gray-200">
        <div className="text-xs text-gray-500">
          Press Ctrl+Shift+P to toggle
        </div>
      </div>
    </div>
  );
}

/**
 * Performance hints component for development
 */
export function PerformanceHints() {
  const [hints, setHints] = useState<string[]>([]);

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    const checkPerformance = () => {
      const newHints: string[] = [];

      // Check for large images without optimization
      const images = document.querySelectorAll('img');
      images.forEach((img) => {
        if (img.naturalWidth > 1920 && !img.loading) {
          newHints.push(`Large image without lazy loading: ${img.src}`);
        }
      });

      // Check for missing alt text
      const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
      if (imagesWithoutAlt.length > 0) {
        newHints.push(`${imagesWithoutAlt.length} images missing alt text`);
      }

      // Check for inline styles (potential CLS issues)
      const elementsWithInlineStyles = document.querySelectorAll('[style*="width"], [style*="height"]');
      if (elementsWithInlineStyles.length > 10) {
        newHints.push('Many elements with inline styles detected');
      }

      setHints(newHints);
    };

    // Check after DOM is loaded
    setTimeout(checkPerformance, 2000);

    // Check periodically
    const interval = setInterval(checkPerformance, 10000);

    return () => clearInterval(interval);
  }, []);

  if (process.env.NODE_ENV !== 'development' || hints.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4 max-w-md">
      <h3 className="text-sm font-semibold text-yellow-800 mb-2">
        Performance Hints
      </h3>
      <ul className="space-y-1">
        {hints.map((hint, index) => (
          <li key={index} className="text-xs text-yellow-700">
            • {hint}
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * Layout shift detector component
 */
export function LayoutShiftDetector() {
  const [shifts, setShifts] = useState<number>(0);
  const [lastShift, setLastShift] = useState<number>(0);

  useEffect(() => {
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
      return;
    }

    let observer: PerformanceObserver;

    try {
      observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
            const shiftValue = (entry as any).value;
            setShifts(prev => prev + 1);
            setLastShift(shiftValue);
            
            if (shiftValue > 0.1) {
              console.warn('Large layout shift detected:', {
                value: shiftValue,
                sources: (entry as any).sources,
                startTime: entry.startTime,
              });
            }
          }
        });
      });

      observer.observe({ entryTypes: ['layout-shift'] });
    } catch (error) {
      console.warn('Layout shift detection not supported:', error);
    }

    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  }, []);

  if (process.env.NODE_ENV !== 'development' || shifts === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 bg-red-50 border border-red-200 rounded-lg shadow-lg p-3">
      <div className="text-sm font-semibold text-red-800">
        Layout Shifts: {shifts}
      </div>
      <div className="text-xs text-red-600">
        Last: {lastShift.toFixed(4)}
      </div>
    </div>
  );
}