import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { usePerformanceMonitor, useImageLoadingMetrics } from '../usePerformanceMonitor';

// Mock performance API
const mockPerformance = {
  now: vi.fn(() => 1000),
  memory: {
    usedJSHeapSize: 1024 * 1024 * 10 // 10MB
  }
};
Object.defineProperty(window, 'performance', {
  value: mockPerformance,
  writable: true
});

describe('usePerformanceMonitor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockPerformance.now.mockReturnValue(1000);
  });

  it('calls onMetrics when component unmounts', () => {
    const mockOnMetrics = vi.fn();
    
    const { unmount } = renderHook(() => usePerformanceMonitor({
      enabled: true,
      onMetrics: mockOnMetrics
    }));

    // Simulate time passing
    mockPerformance.now.mockReturnValue(1500);
    
    unmount();

    expect(mockOnMetrics).toHaveBeenCalledWith(
      expect.objectContaining({
        loadTime: expect.any(Number),
        renderTime: expect.any(Number),
        imageCount: 0,
        memoryUsage: 1024 * 1024 * 10
      })
    );
  });

  it('does not call onMetrics when disabled', () => {
    const mockOnMetrics = vi.fn();
    
    const { unmount } = renderHook(() => usePerformanceMonitor({
      enabled: false,
      onMetrics: mockOnMetrics
    }));

    unmount();

    expect(mockOnMetrics).not.toHaveBeenCalled();
  });

  it('provides image count management functions', () => {
    const { result } = renderHook(() => usePerformanceMonitor({
      enabled: true
    }));

    expect(result.current.incrementImageCount).toBeDefined();
    expect(result.current.resetImageCount).toBeDefined();
    expect(typeof result.current.incrementImageCount).toBe('function');
    expect(typeof result.current.resetImageCount).toBe('function');
  });
});

describe('useImageLoadingMetrics', () => {
  it('provides image loading metric functions', () => {
    const { result } = renderHook(() => useImageLoadingMetrics());

    expect(result.current.recordImageLoad).toBeDefined();
    expect(result.current.getMetrics).toBeDefined();
    expect(result.current.resetMetrics).toBeDefined();
    expect(typeof result.current.recordImageLoad).toBe('function');
    expect(typeof result.current.getMetrics).toBe('function');
    expect(typeof result.current.resetMetrics).toBe('function');
  });

  it('records successful image loads', () => {
    const { result } = renderHook(() => useImageLoadingMetrics());

    result.current.recordImageLoad(100, true);
    result.current.recordImageLoad(200, true);

    const metrics = result.current.getMetrics();
    expect(metrics.loadedImages).toBe(2);
    expect(metrics.failedImages).toBe(0);
    expect(metrics.averageLoadTime).toBe(150);
    expect(metrics.loadTimes).toEqual([100, 200]);
  });

  it('records failed image loads', () => {
    const { result } = renderHook(() => useImageLoadingMetrics());

    result.current.recordImageLoad(100, false);
    result.current.recordImageLoad(200, true);

    const metrics = result.current.getMetrics();
    expect(metrics.loadedImages).toBe(1);
    expect(metrics.failedImages).toBe(1);
    expect(metrics.averageLoadTime).toBe(200);
    expect(metrics.loadTimes).toEqual([200]);
  });

  it('resets metrics correctly', () => {
    const { result } = renderHook(() => useImageLoadingMetrics());

    result.current.recordImageLoad(100, true);
    result.current.resetMetrics();

    const metrics = result.current.getMetrics();
    expect(metrics.loadedImages).toBe(0);
    expect(metrics.failedImages).toBe(0);
    expect(metrics.averageLoadTime).toBe(0);
    expect(metrics.loadTimes).toEqual([]);
  });
});