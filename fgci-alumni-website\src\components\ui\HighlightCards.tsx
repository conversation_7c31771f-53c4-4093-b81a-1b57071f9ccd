'use client';

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Card, CardContent } from './Card';
import { Button } from './Button';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

export interface HighlightCard {
  id: string;
  title: string;
  description: string;
  icon?: React.ReactNode;
  image?: string;
  stats?: {
    label: string;
    value: string | number;
  };
  ctaText?: string;
  ctaLink?: string;
  ctaAction?: () => void;
  color?: 'maroon' | 'gold' | 'blue' | 'green';
}

export interface HighlightCardsProps {
  cards: HighlightCard[];
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export function HighlightCards({
  cards,
  columns = 3,
  className
}: HighlightCardsProps) {
  const getGridClasses = () => {
    switch (columns) {
      case 1:
        return 'grid-cols-1';
      case 2:
        return 'grid-cols-1 md:grid-cols-2';
      case 3:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      case 4:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default:
        return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    }
  };

  const getColorClasses = (color?: string) => {
    switch (color) {
      case 'maroon':
        return {
          iconBg: 'bg-maroon-100',
          iconColor: 'text-maroon-700',
          accent: 'border-maroon-200 hover:border-maroon-300',
          button: 'text-maroon-700 hover:text-maroon-800'
        };
      case 'gold':
        return {
          iconBg: 'bg-gold-100',
          iconColor: 'text-gold-700',
          accent: 'border-gold-200 hover:border-gold-300',
          button: 'text-gold-700 hover:text-gold-800'
        };
      case 'blue':
        return {
          iconBg: 'bg-blue-100',
          iconColor: 'text-blue-700',
          accent: 'border-blue-200 hover:border-blue-300',
          button: 'text-blue-700 hover:text-blue-800'
        };
      case 'green':
        return {
          iconBg: 'bg-green-100',
          iconColor: 'text-green-700',
          accent: 'border-green-200 hover:border-green-300',
          button: 'text-green-700 hover:text-green-800'
        };
      default:
        return {
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-700',
          accent: 'border-gray-200 hover:border-gray-300',
          button: 'text-gray-700 hover:text-gray-800'
        };
    }
  };

  if (!cards.length) return null;

  return (
    <div className={cn('w-full', className)} data-testid="highlight-cards">
      <div className={cn('grid gap-6', getGridClasses())}>
        {cards.map((card) => {
          const colors = getColorClasses(card.color);
          
          return (
            <Card
              key={card.id}
              variant="elevated"
              hover={!!card.ctaAction || !!card.ctaLink}
              className={cn(
                'group transition-all duration-300 hover:shadow-xl motion-reduce:transition-none',
                colors.accent
              )}
              onClick={card.ctaAction}
              data-testid="highlight-card"
            >
              <CardContent className="p-6">
                {/* Image or Icon */}
                {card.image ? (
                  <div className="mb-4 overflow-hidden rounded-lg">
                    <img
                      src={card.image}
                      alt={card.title}
                      className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105 motion-reduce:transition-none"
                    />
                  </div>
                ) : card.icon ? (
                  <div className={cn(
                    'w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto transition-transform duration-300 group-hover:scale-110 motion-reduce:transition-none',
                    colors.iconBg
                  )}>
                    <div className={cn('w-8 h-8', colors.iconColor)}>
                      {card.icon}
                    </div>
                  </div>
                ) : null}

                {/* Content */}
                <div className="text-center">
                  <h3 className="heading-3 text-xl font-semibold mb-3 text-gray-900">
                    {card.title}
                  </h3>
                  
                  <p className="body-base text-gray-600 mb-4 leading-relaxed">
                    {card.description}
                  </p>

                  {/* Stats */}
                  {card.stats && (
                    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900 mb-1">
                        {card.stats.value}
                      </div>
                      <div className="text-sm text-gray-600">
                        {card.stats.label}
                      </div>
                    </div>
                  )}

                  {/* CTA Button */}
                  {card.ctaText && (
                    card.ctaLink ? (
                      <Link href={card.ctaLink}>
                        <Button
                          variant="ghost"
                          size="sm"
                          className={cn(
                            'group-hover:translate-x-1 transition-transform duration-200 motion-reduce:transition-none',
                            colors.button
                          )}
                          icon={<ArrowRightIcon className="w-4 h-4" />}
                        >
                          {card.ctaText}
                        </Button>
                      </Link>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        className={cn(
                          'group-hover:translate-x-1 transition-transform duration-200 motion-reduce:transition-none',
                          colors.button
                        )}
                        icon={<ArrowRightIcon className="w-4 h-4" />}
                        onClick={(e) => {
                          e.stopPropagation();
                          card.ctaAction?.();
                        }}
                      >
                        {card.ctaText}
                      </Button>
                    )
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

// Preset highlight cards for common use cases
export const createChapterHighlight = (count: number | string): HighlightCard => ({
  id: 'chapters',
  title: 'Chapters',
  description: 'Connect with alumni in your region through our organized chapters worldwide.',
  stats: {
    label: 'Active Chapters',
    value: count
  },
  ctaText: 'Explore Chapters',
  color: 'maroon'
});

export const createSetHighlight = (count: number | string): HighlightCard => ({
  id: 'sets',
  title: 'Sets',
  description: 'Reconnect with your graduating class and explore activities by graduation year.',
  stats: {
    label: 'Graduation Sets',
    value: count
  },
  ctaText: 'View Sets',
  color: 'gold'
});

export const createEventHighlight = (count: number | string): HighlightCard => ({
  id: 'events',
  title: 'Events',
  description: 'Stay updated with alumni events, reunions, and community activities.',
  stats: {
    label: 'Upcoming Events',
    value: count
  },
  ctaText: 'View Events',
  color: 'blue'
});

export const createGalleryHighlight = (count: number | string): HighlightCard => ({
  id: 'gallery',
  title: 'Gallery',
  description: 'Browse through memories and moments captured at various alumni events.',
  stats: {
    label: 'Photos & Videos',
    value: count
  },
  ctaText: 'View Gallery',
  color: 'green'
});
