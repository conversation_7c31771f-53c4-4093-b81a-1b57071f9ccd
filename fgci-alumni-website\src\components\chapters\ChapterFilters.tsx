'use client';

import { 
  MagnifyingGlassIcon, 
  FunnelIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';

interface ChapterFiltersProps {
  searchQuery: string;
  selectedType: string;
  onSearchChange: (query: string) => void;
  onTypeChange: (type: string) => void;
  onClearFilters: () => void;
  className?: string;
}

const chapterTypes = [
  { value: 'all', label: 'All Types' },
  { value: 'regional', label: 'Regional' },
  { value: 'diaspora', label: 'Diaspora' },
  { value: 'school-based', label: 'School-based' }
];

export function ChapterFilters({
  searchQuery,
  selectedType,
  onSearchChange,
  onTypeChange,
  onClearFilters,
  className
}: ChapterFiltersProps) {
  const hasActiveFilters = searchQuery.trim() || selectedType !== 'all';

  return (
    <div className={cn("bg-white border-b", className)}>
      <div className="container-custom py-6">
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          {/* Search Input */}
          <div className="relative flex-1 max-w-md">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              placeholder="Search chapters by name or region..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className={cn(
                "w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg",
                "focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500",
                "placeholder:text-gray-400 text-sm",
                "transition-colors duration-200"
              )}
            />
          </div>

          {/* Filter Controls */}
          <div className="flex flex-wrap items-center gap-3">
            <div className="flex items-center gap-2">
              <FunnelIcon className="w-5 h-5 text-gray-400" />
              <span className="text-sm text-gray-600 font-medium">Filter by:</span>
            </div>
            
            {/* Type Filter Chips */}
            <div className="flex flex-wrap gap-2">
              {chapterTypes.map((type) => (
                <button
                  key={type.value}
                  onClick={() => onTypeChange(type.value)}
                  className={cn(
                    'px-3 py-1.5 text-sm rounded-full border transition-all duration-200',
                    'hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-maroon-500 focus:ring-offset-1',
                    selectedType === type.value
                      ? 'bg-maroon-700 text-white border-maroon-700 shadow-sm'
                      : 'bg-white text-gray-600 border-gray-300 hover:border-maroon-300 hover:text-maroon-700'
                  )}
                >
                  {type.label}
                </button>
              ))}
            </div>

            {/* Clear Filters Button */}
            {hasActiveFilters && (
              <button
                onClick={onClearFilters}
                className={cn(
                  "flex items-center gap-1.5 px-3 py-1.5 text-sm",
                  "text-gray-500 hover:text-gray-700",
                  "border border-gray-300 rounded-full",
                  "hover:border-gray-400 transition-colors duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-1"
                )}
              >
                <XMarkIcon className="w-4 h-4" />
                <span>Clear</span>
              </button>
            )}
          </div>
        </div>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="mt-4 flex flex-wrap items-center gap-2 text-sm text-gray-600">
            <span>Active filters:</span>
            {searchQuery.trim() && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md">
                Search: "{searchQuery.trim()}"
              </span>
            )}
            {selectedType !== 'all' && (
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-md">
                Type: {chapterTypes.find(t => t.value === selectedType)?.label}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}