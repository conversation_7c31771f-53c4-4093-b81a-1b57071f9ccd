{"name": "fgc-ikom-alumni-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:mock": "cross-env NEXT_PUBLIC_USE_MOCKS=true next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:report": "playwright show-report", "test:ci": "vitest run --coverage", "lighthouse": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "bundle-analyzer": "cross-env ANALYZE=true next build", "perf:audit": "npm run lighthouse && npm run bundle-analyzer", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepare": "husky"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "dependencies": {"@headlessui/react": "^2.2.8", "@heroicons/react": "^2.2.0", "@tanstack/react-query": "^5.90.1", "clsx": "^2.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "framer-motion": "^12.23.18", "lucide-react": "^0.544.0", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.1.0"}, "devDependencies": {"@axe-core/react": "^4.10.2", "@chromatic-com/storybook": "^4.1.1", "@eslint/eslintrc": "^3", "@playwright/test": "^1.55.0", "@storybook/addon-a11y": "^9.1.7", "@storybook/addon-docs": "^9.1.7", "@storybook/addon-onboarding": "^9.1.7", "@storybook/addon-vitest": "^9.1.7", "@storybook/nextjs-vite": "^9.1.7", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "cross-env": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.5.3", "eslint-plugin-storybook": "^9.1.7", "husky": "^9.1.7", "jest": "^30.1.3", "jest-axe": "^10.0.0", "jsdom": "^27.0.0", "lint-staged": "^16.2.0", "msw": "^2.11.3", "playwright": "^1.55.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "storybook": "^9.1.7", "tailwindcss": "^4", "typescript": "^5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "msw": {"workerDirectory": ["public"]}}