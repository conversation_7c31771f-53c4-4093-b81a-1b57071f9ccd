import { render, screen, fireEvent } from '@testing-library/react';
import { GalleryGrid } from '../GalleryGrid';
import type { GalleryImage } from '../../../lib/types';

// Mock the dependencies
jest.mock('../../../lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' ')
}));

jest.mock('../Lightbox', () => ({
  Lightbox: ({ isOpen, children }: { isOpen: boolean; children: React.ReactNode }) => 
    isOpen ? <div data-testid="lightbox">{children}</div> : null
}));

jest.mock('../OptimizedImage', () => ({
  OptimizedImage: ({ src, alt, onClick }: { src: string; alt: string; onClick: () => void }) => (
    <div data-testid="optimized-image" onClick={onClick}>
      <img src={src} alt={alt} />
    </div>
  )
}));

const mockImages: GalleryImage[] = [
  {
    id: '1',
    url: 'https://example.com/image1.jpg',
    thumbnailUrl: 'https://example.com/thumb1.jpg',
    alt: 'Test image 1',
    caption: 'Test caption 1',
    photographer: 'Test photographer 1'
  },
  {
    id: '2',
    url: 'https://example.com/image2.jpg',
    thumbnailUrl: 'https://example.com/thumb2.jpg',
    alt: 'Test image 2',
    caption: 'Test caption 2',
    photographer: 'Test photographer 2'
  }
];

describe('GalleryGrid', () => {
  it('renders images correctly', () => {
    render(<GalleryGrid images={mockImages} />);
    
    expect(screen.getAllByTestId('optimized-image')).toHaveLength(2);
  });

  it('opens lightbox when image is clicked', () => {
    render(<GalleryGrid images={mockImages} />);
    
    const firstImage = screen.getAllByTestId('optimized-image')[0];
    fireEvent.click(firstImage);
    
    expect(screen.getByTestId('lightbox')).toBeInTheDocument();
  });

  it('handles empty images array', () => {
    render(<GalleryGrid images={[]} />);
    
    expect(screen.getByText('No Images')).toBeInTheDocument();
    expect(screen.getByText('No images available in this gallery.')).toBeInTheDocument();
  });

  it('calls onImageClick when provided', () => {
    const mockOnImageClick = jest.fn();
    render(<GalleryGrid images={mockImages} onImageClick={mockOnImageClick} />);
    
    const firstImage = screen.getAllByTestId('optimized-image')[0];
    fireEvent.click(firstImage);
    
    expect(mockOnImageClick).toHaveBeenCalledWith(mockImages[0], 0);
  });

  it('applies correct grid classes for different column counts', () => {
    const { rerender } = render(<GalleryGrid images={mockImages} columns={2} />);
    
    let gridContainer = screen.getByRole('grid', { hidden: true });
    expect(gridContainer).toHaveClass('grid-cols-2');
    
    rerender(<GalleryGrid images={mockImages} columns={3} />);
    gridContainer = screen.getByRole('grid', { hidden: true });
    expect(gridContainer).toHaveClass('grid-cols-2', 'md:grid-cols-3');
    
    rerender(<GalleryGrid images={mockImages} columns={4} />);
    gridContainer = screen.getByRole('grid', { hidden: true });
    expect(gridContainer).toHaveClass('grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-4');
  });

  it('disables lightbox when showLightbox is false', () => {
    render(<GalleryGrid images={mockImages} showLightbox={false} />);
    
    const firstImage = screen.getAllByTestId('optimized-image')[0];
    fireEvent.click(firstImage);
    
    expect(screen.queryByTestId('lightbox')).not.toBeInTheDocument();
  });
});