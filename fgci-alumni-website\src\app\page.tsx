import { Metadata } from 'next';
import { generatePageMetadata } from '@/components/seo';
import { REVALIDATION_TIMES } from '@/lib/isr';
import HomePageClient from './HomePageClient';

export const metadata: Metadata = generatePageMetadata({
  title: 'Home',
  description: 'Welcome to the FGC Ikom Alumni Association. Connect with fellow alumni, explore chapters, sets, events, and galleries.',
  keywords: ['home', 'welcome', 'alumni network', 'community', 'FGC Ikom'],
  pathname: '/',
  ogType: 'website',
});

// Enable ISR with daily revalidation
export const revalidate = REVALIDATION_TIMES.DAILY;

export default function HomePage() {
  return <HomePageClient />;
}