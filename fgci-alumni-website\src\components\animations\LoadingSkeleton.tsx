'use client';

import { motion } from 'framer-motion';
import { useReducedMotion } from '@/hooks/useReducedMotion';
import { cn } from '@/lib/utils';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'avatar' | 'card' | 'image' | 'button';
  lines?: number;
  width?: string;
  height?: string;
}

const shimmerVariants = {
  initial: { x: '-100%' },
  animate: { x: '100%' },
};

const shimmerTransition = {
  repeat: Infinity,
  duration: 1.5,
  ease: 'linear',
};

export function LoadingSkeleton({
  className,
  variant = 'text',
  lines = 1,
  width,
  height,
}: LoadingSkeletonProps) {
  const prefersReducedMotion = useReducedMotion();

  const baseClasses = 'bg-gray-200 rounded overflow-hidden relative';
  
  const variantClasses = {
    text: 'h-4 mb-2 last:mb-0',
    avatar: 'w-10 h-10 rounded-full',
    card: 'h-48 rounded-lg',
    image: 'w-full h-32 rounded-md',
    button: 'h-10 w-24 rounded-md',
  };

  const skeletonClass = cn(
    baseClasses,
    variantClasses[variant],
    className
  );

  const style = {
    width: width || (variant === 'text' && lines > 1 ? '100%' : undefined),
    height: height || undefined,
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, index) => (
          <SkeletonItem
            key={index}
            className={cn(
              skeletonClass,
              index === lines - 1 && 'w-3/4' // Last line is shorter
            )}
            prefersReducedMotion={prefersReducedMotion}
            style={style}
          />
        ))}
      </div>
    );
  }

  return (
    <SkeletonItem
      className={skeletonClass}
      prefersReducedMotion={prefersReducedMotion}
      style={style}
    />
  );
}

interface SkeletonItemProps {
  className: string;
  prefersReducedMotion: boolean;
  style?: React.CSSProperties;
}

function SkeletonItem({ className, prefersReducedMotion, style }: SkeletonItemProps) {
  if (prefersReducedMotion) {
    return <div className={className} style={style} />;
  }

  return (
    <div className={className} style={style}>
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent"
        variants={shimmerVariants}
        initial="initial"
        animate="animate"
        transition={shimmerTransition}
      />
    </div>
  );
}

// Preset skeleton components for common use cases
export function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('p-6 space-y-4', className)}>
      <LoadingSkeleton variant="avatar" />
      <LoadingSkeleton variant="text" lines={2} />
      <LoadingSkeleton variant="button" />
    </div>
  );
}

export function ListSkeleton({ 
  items = 3, 
  className 
}: { 
  items?: number; 
  className?: string; 
}) {
  return (
    <div className={cn('space-y-4', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <LoadingSkeleton variant="avatar" />
          <div className="flex-1">
            <LoadingSkeleton variant="text" lines={2} />
          </div>
        </div>
      ))}
    </div>
  );
}

export function GallerySkeleton({ 
  items = 6, 
  className 
}: { 
  items?: number; 
  className?: string; 
}) {
  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4', className)}>
      {Array.from({ length: items }).map((_, index) => (
        <LoadingSkeleton key={index} variant="image" className="aspect-square" />
      ))}
    </div>
  );
}