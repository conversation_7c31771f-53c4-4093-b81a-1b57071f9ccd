/**
 * Accessibility utility functions for testing and validation
 */

/**
 * Calculate color contrast ratio between two colors
 * @param color1 - First color in hex format
 * @param color2 - Second color in hex format
 * @returns Contrast ratio (1-21)
 */
export function calculateContrastRatio(color1: string, color2: string): number {
  const getLuminance = (color: string): number => {
    // Remove # if present
    const hex = color.replace('#', '');
    
    // Convert to RGB
    const r = parseInt(hex.substring(0, 2), 16) / 255;
    const g = parseInt(hex.substring(2, 4), 16) / 255;
    const b = parseInt(hex.substring(4, 6), 16) / 255;
    
    // Calculate relative luminance
    const sRGB = [r, g, b].map(c => {
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    
    return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);
  
  return (brightest + 0.05) / (darkest + 0.05);
}

/**
 * Check if color combination meets WCAG contrast requirements
 * @param foreground - Foreground color in hex
 * @param background - Background color in hex
 * @param level - WCAG level ('AA' or 'AAA')
 * @param size - Text size ('normal' or 'large')
 * @returns Whether the combination passes
 */
export function meetsContrastRequirement(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA',
  size: 'normal' | 'large' = 'normal'
): boolean {
  const ratio = calculateContrastRatio(foreground, background);
  
  const requirements = {
    AA: { normal: 4.5, large: 3 },
    AAA: { normal: 7, large: 4.5 }
  };
  
  return ratio >= requirements[level][size];
}

/**
 * Get accessible color alternatives for better contrast
 * @param foreground - Current foreground color
 * @param background - Current background color
 * @param level - Target WCAG level
 * @returns Suggested color alternatives
 */
export function getAccessibleColorAlternatives(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA'
): {
  currentRatio: number;
  passes: boolean;
  suggestions: {
    darkerForeground?: string;
    lighterForeground?: string;
    darkerBackground?: string;
    lighterBackground?: string;
  };
} {
  const currentRatio = calculateContrastRatio(foreground, background);
  const passes = meetsContrastRequirement(foreground, background, level);
  
  const suggestions: any = {};
  
  if (!passes) {
    // Suggest darker foreground
    const darkerFg = adjustColorBrightness(foreground, -20);
    if (meetsContrastRequirement(darkerFg, background, level)) {
      suggestions.darkerForeground = darkerFg;
    }
    
    // Suggest lighter foreground
    const lighterFg = adjustColorBrightness(foreground, 20);
    if (meetsContrastRequirement(lighterFg, background, level)) {
      suggestions.lighterForeground = lighterFg;
    }
    
    // Suggest darker background
    const darkerBg = adjustColorBrightness(background, -20);
    if (meetsContrastRequirement(foreground, darkerBg, level)) {
      suggestions.darkerBackground = darkerBg;
    }
    
    // Suggest lighter background
    const lighterBg = adjustColorBrightness(background, 20);
    if (meetsContrastRequirement(foreground, lighterBg, level)) {
      suggestions.lighterBackground = lighterBg;
    }
  }
  
  return {
    currentRatio,
    passes,
    suggestions
  };
}

/**
 * Adjust color brightness
 * @param color - Color in hex format
 * @param percent - Percentage to adjust (-100 to 100)
 * @returns Adjusted color in hex format
 */
export function adjustColorBrightness(color: string, percent: number): string {
  const hex = color.replace('#', '');
  const num = parseInt(hex, 16);
  
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  
  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255))
    .toString(16)
    .slice(1);
}

/**
 * Check if element has sufficient focus indicators
 * @param element - DOM element to check
 * @returns Focus indicator assessment
 */
export function checkFocusIndicators(element: HTMLElement): {
  hasVisibleFocus: boolean;
  hasOutline: boolean;
  hasBoxShadow: boolean;
  recommendations: string[];
} {
  const computedStyle = window.getComputedStyle(element, ':focus-visible');
  
  const hasOutline = computedStyle.outline !== 'none' && 
                    computedStyle.outline !== '0px' &&
                    computedStyle.outline !== '';
  
  const hasBoxShadow = computedStyle.boxShadow !== 'none' &&
                      computedStyle.boxShadow !== '';
  
  const hasVisibleFocus = hasOutline || hasBoxShadow;
  
  const recommendations: string[] = [];
  
  if (!hasVisibleFocus) {
    recommendations.push('Add visible focus indicator (outline or box-shadow)');
  }
  
  if (hasOutline) {
    const outlineWidth = parseInt(computedStyle.outlineWidth) || 0;
    if (outlineWidth < 2) {
      recommendations.push('Increase outline width to at least 2px');
    }
  }
  
  return {
    hasVisibleFocus,
    hasOutline,
    hasBoxShadow,
    recommendations
  };
}

/**
 * Validate alt text quality
 * @param altText - Alt text to validate
 * @param context - Context about the image
 * @returns Validation results
 */
export function validateAltText(
  altText: string,
  context?: {
    isDecorative?: boolean;
    hasCaption?: boolean;
    isInLink?: boolean;
  }
): {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  // Check for decorative images
  if (context?.isDecorative && altText !== '') {
    issues.push('Decorative images should have empty alt text');
    suggestions.push('Use alt="" for decorative images');
  }
  
  // Check for missing alt text
  if (!context?.isDecorative && !altText.trim()) {
    issues.push('Missing alt text for meaningful image');
    suggestions.push('Provide descriptive alt text that conveys the image content');
  }
  
  // Check for redundant phrases
  const redundantPhrases = [
    'image of', 'picture of', 'photo of', 'graphic of',
    'image showing', 'picture showing', 'photo showing'
  ];
  
  const lowerAlt = altText.toLowerCase();
  redundantPhrases.forEach(phrase => {
    if (lowerAlt.includes(phrase)) {
      issues.push(`Avoid redundant phrase: "${phrase}"`);
      suggestions.push('Remove redundant phrases and focus on content description');
    }
  });
  
  // Check length
  if (altText.length > 125) {
    issues.push('Alt text is too long (over 125 characters)');
    suggestions.push('Consider using a caption or long description for detailed information');
  }
  
  // Check for filename-like text
  if (altText.includes('.jpg') || altText.includes('.png') || altText.includes('.gif')) {
    issues.push('Alt text appears to be a filename');
    suggestions.push('Replace filename with descriptive text');
  }
  
  // Check for link context
  if (context?.isInLink && !altText.includes('link')) {
    suggestions.push('Consider indicating that this image is a link');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    suggestions
  };
}

/**
 * Check heading structure for proper hierarchy
 * @param container - Container element to check
 * @returns Heading structure analysis
 */
export function checkHeadingStructure(container: HTMLElement = document.body): {
  isValid: boolean;
  issues: string[];
  headings: Array<{ level: number; text: string; element: HTMLElement }>;
} {
  const headings = Array.from(container.querySelectorAll('h1, h2, h3, h4, h5, h6'))
    .map(el => ({
      level: parseInt(el.tagName.charAt(1)),
      text: el.textContent?.trim() || '',
      element: el as HTMLElement
    }));
  
  const issues: string[] = [];
  
  // Check for missing h1
  if (!headings.some(h => h.level === 1)) {
    issues.push('Missing h1 element - each page should have exactly one h1');
  }
  
  // Check for multiple h1s
  const h1Count = headings.filter(h => h.level === 1).length;
  if (h1Count > 1) {
    issues.push(`Multiple h1 elements found (${h1Count}) - should have only one per page`);
  }
  
  // Check for skipped levels
  for (let i = 1; i < headings.length; i++) {
    const current = headings[i];
    const previous = headings[i - 1];
    
    if (current.level > previous.level + 1) {
      issues.push(`Heading level skipped: h${previous.level} followed by h${current.level}`);
    }
  }
  
  // Check for empty headings
  headings.forEach((heading, index) => {
    if (!heading.text) {
      issues.push(`Empty heading found: h${heading.level} at position ${index + 1}`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues,
    headings
  };
}

/**
 * Check for keyboard accessibility issues
 * @param container - Container to check
 * @returns Keyboard accessibility analysis
 */
export function checkKeyboardAccessibility(container: HTMLElement = document.body): {
  focusableElements: HTMLElement[];
  issues: string[];
  tabOrder: HTMLElement[];
} {
  const focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'textarea:not([disabled])',
    'select:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ');
  
  const focusableElements = Array.from(
    container.querySelectorAll(focusableSelectors)
  ) as HTMLElement[];
  
  const issues: string[] = [];
  
  // Check for elements without visible focus indicators
  focusableElements.forEach(el => {
    const focusCheck = checkFocusIndicators(el);
    if (!focusCheck.hasVisibleFocus) {
      issues.push(`Element lacks visible focus indicator: ${el.tagName.toLowerCase()}`);
    }
  });
  
  // Check tab order
  const tabOrder = focusableElements
    .map(el => ({
      element: el,
      tabIndex: el.tabIndex || 0
    }))
    .sort((a, b) => {
      if (a.tabIndex === 0 && b.tabIndex === 0) return 0;
      if (a.tabIndex === 0) return 1;
      if (b.tabIndex === 0) return -1;
      return a.tabIndex - b.tabIndex;
    })
    .map(item => item.element);
  
  // Check for positive tabindex values (anti-pattern)
  focusableElements.forEach(el => {
    if (el.tabIndex > 0) {
      issues.push(`Positive tabindex found (${el.tabIndex}) - use 0 or -1 instead`);
    }
  });
  
  return {
    focusableElements,
    issues,
    tabOrder
  };
}

/**
 * Generate accessibility report for a page or component
 * @param container - Container to analyze
 * @returns Comprehensive accessibility report
 */
export function generateAccessibilityReport(container: HTMLElement = document.body): {
  score: number;
  issues: Array<{
    type: 'error' | 'warning' | 'info';
    category: string;
    message: string;
    element?: HTMLElement;
  }>;
  summary: {
    totalIssues: number;
    errors: number;
    warnings: number;
    passedChecks: number;
  };
} {
  const issues: any[] = [];
  
  // Check heading structure
  const headingCheck = checkHeadingStructure(container);
  headingCheck.issues.forEach(issue => {
    issues.push({
      type: 'error',
      category: 'Heading Structure',
      message: issue
    });
  });
  
  // Check keyboard accessibility
  const keyboardCheck = checkKeyboardAccessibility(container);
  keyboardCheck.issues.forEach(issue => {
    issues.push({
      type: 'warning',
      category: 'Keyboard Navigation',
      message: issue
    });
  });
  
  // Check images for alt text
  const images = Array.from(container.querySelectorAll('img')) as HTMLImageElement[];
  images.forEach(img => {
    const altValidation = validateAltText(img.alt, {
      isDecorative: img.getAttribute('role') === 'presentation' || 
                   img.getAttribute('aria-hidden') === 'true'
    });
    
    altValidation.issues.forEach(issue => {
      issues.push({
        type: 'error',
        category: 'Images',
        message: issue,
        element: img
      });
    });
  });
  
  // Calculate score
  const totalChecks = headingCheck.headings.length + 
                     keyboardCheck.focusableElements.length + 
                     images.length;
  const passedChecks = totalChecks - issues.length;
  const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;
  
  return {
    score,
    issues,
    summary: {
      totalIssues: issues.length,
      errors: issues.filter(i => i.type === 'error').length,
      warnings: issues.filter(i => i.type === 'warning').length,
      passedChecks
    }
  };
}