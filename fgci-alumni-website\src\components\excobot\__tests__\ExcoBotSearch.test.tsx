import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import { ExcoBotSearch } from '../ExcoBotSearch';
import { mockAlumniDirectory } from '@/mocks/data/alumni';

// Mock the useAlumniSearch hook
vi.mock('@/hooks/useAlumniSearch', () => ({
  useAlumniSearch: vi.fn(() => ({
    data: mockAlumniDirectory.slice(0, 5),
    isLoading: false,
    error: null,
  })),
}));

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('ExcoBotSearch', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the search interface correctly', () => {
    renderWithQueryClient(<ExcoBotSearch />);
    
    expect(screen.getByPlaceholderText(/search alumni by name/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /filters/i })).toBeInTheDocument();
    expect(screen.getByLabelText(/sort results/i)).toBeInTheDocument();
  });

  it('displays welcome message when no search is started', () => {
    renderWithQueryClient(<ExcoBotSearch />);
    
    expect(screen.getByText(/welcome to excobot/i)).toBeInTheDocument();
    expect(screen.getByText(/start searching to find and connect/i)).toBeInTheDocument();
  });

  it('handles search input correctly', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    const searchInput = screen.getByPlaceholderText(/search alumni by name/i);
    await user.type(searchInput, 'Adaora');
    
    expect(searchInput).toHaveValue('Adaora');
  });

  it('shows and hides filters panel', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await user.click(filtersButton);
    
    expect(screen.getByText(/filter alumni/i)).toBeInTheDocument();
    
    const closeButton = screen.getByLabelText(/close filters/i);
    await user.click(closeButton);
    
    expect(screen.queryByText(/filter alumni/i)).not.toBeInTheDocument();
  });

  it('displays active search filters', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    const searchInput = screen.getByPlaceholderText(/search alumni by name/i);
    await user.type(searchInput, 'President');
    
    await waitFor(() => {
      expect(screen.getByText(/active filters/i)).toBeInTheDocument();
      expect(screen.getByText(/"President"/i)).toBeInTheDocument();
    });
  });

  it('clears search when clear button is clicked', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    const searchInput = screen.getByPlaceholderText(/search alumni by name/i);
    await user.type(searchInput, 'test');
    
    const clearButton = screen.getByLabelText(/clear search/i);
    await user.click(clearButton);
    
    expect(searchInput).toHaveValue('');
  });

  it('handles sorting changes', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    const sortSelect = screen.getByLabelText(/sort results/i);
    await user.selectOptions(sortSelect, 'year');
    
    expect(sortSelect).toHaveValue('year');
  });

  it('shows quick filter suggestions', () => {
    renderWithQueryClient(<ExcoBotSearch />);
    
    expect(screen.getByText(/quick filters/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /presidents/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /lagos chapter/i })).toBeInTheDocument();
  });

  it('applies quick filters when clicked', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    const presidentsFilter = screen.getByRole('button', { name: /presidents/i });
    await user.click(presidentsFilter);
    
    await waitFor(() => {
      expect(screen.getByText(/active filters/i)).toBeInTheDocument();
    });
  });

  it('clears all filters when clear all is clicked', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    // Add a search term
    const searchInput = screen.getByPlaceholderText(/search alumni by name/i);
    await user.type(searchInput, 'test');
    
    await waitFor(() => {
      expect(screen.getByText(/clear all/i)).toBeInTheDocument();
    });
    
    const clearAllButton = screen.getByRole('button', { name: /clear all/i });
    await user.click(clearAllButton);
    
    expect(searchInput).toHaveValue('');
    expect(screen.queryByText(/active filters/i)).not.toBeInTheDocument();
  });

  it('displays filter count badge', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    // Open filters and apply one
    const filtersButton = screen.getByRole('button', { name: /filters/i });
    await user.click(filtersButton);
    
    const chapterSelect = screen.getByLabelText(/chapter/i);
    await user.selectOptions(chapterSelect, 'Lagos Chapter');
    
    const applyButton = screen.getByRole('button', { name: /apply filters/i });
    await user.click(applyButton);
    
    // Check for filter count badge
    await waitFor(() => {
      const filtersButtonWithBadge = screen.getByRole('button', { name: /filters/i });
      expect(filtersButtonWithBadge.parentElement).toHaveTextContent('1');
    });
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    renderWithQueryClient(<ExcoBotSearch />);
    
    const searchInput = screen.getByPlaceholderText(/search alumni by name/i);
    
    // Tab to search input
    await user.tab();
    expect(searchInput).toHaveFocus();
    
    // Tab to filters button
    await user.tab();
    expect(screen.getByRole('button', { name: /filters/i })).toHaveFocus();
    
    // Tab to sort select
    await user.tab();
    expect(screen.getByLabelText(/sort results/i)).toHaveFocus();
  });

  it('shows accessibility attributes', () => {
    renderWithQueryClient(<ExcoBotSearch />);
    
    const searchInput = screen.getByPlaceholderText(/search alumni by name/i);
    expect(searchInput).toHaveAttribute('aria-label', 'Search alumni directory');
    
    const sortSelect = screen.getByLabelText(/sort results/i);
    expect(sortSelect).toHaveAttribute('aria-label', 'Sort results');
  });
});