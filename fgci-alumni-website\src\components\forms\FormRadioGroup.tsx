'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

interface FormRadioGroupProps {
  label: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  options: RadioOption[];
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  touched?: boolean;
  className?: string;
  helpText?: string;
  orientation?: 'vertical' | 'horizontal';
}

export function FormRadioGroup({
  label,
  name,
  value,
  onChange,
  onBlur,
  options,
  required = false,
  disabled = false,
  errors = [],
  touched = false,
  className,
  helpText,
  orientation = 'vertical',
}: FormRadioGroupProps) {
  const hasError = touched && errors.length > 0;
  const groupId = `group-${name}`;
  const errorId = `${groupId}-error`;
  const helpId = `${groupId}-help`;

  return (
    <fieldset className={cn('space-y-2', className)}>
      <legend className="block text-sm font-medium text-gray-700">
        {label}
        {required && (
          <span className="text-red-500 ml-1" aria-label="required">
            *
          </span>
        )}
      </legend>
      
      {helpText && !hasError && (
        <p id={helpId} className="text-sm text-gray-600">
          {helpText}
        </p>
      )}
      
      <div 
        className={cn(
          'space-y-3',
          orientation === 'horizontal' && 'flex flex-wrap gap-6 space-y-0'
        )}
        role="radiogroup"
        aria-labelledby={groupId}
        aria-invalid={hasError}
        aria-describedby={cn(
          hasError && errorId,
          helpText && helpId
        )}
      >
        {options.map((option) => {
          const optionId = `${name}-${option.value}`;
          const isDisabled = disabled || option.disabled;
          
          return (
            <div key={option.value} className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  type="radio"
                  id={optionId}
                  name={name}
                  value={option.value}
                  checked={value === option.value}
                  onChange={onChange}
                  onBlur={onBlur}
                  required={required}
                  disabled={isDisabled}
                  className={cn(
                    'w-4 h-4 border transition-colors',
                    'focus:ring-2 focus:ring-maroon-500 focus:ring-offset-2',
                    'disabled:cursor-not-allowed disabled:opacity-50',
                    hasError
                      ? 'border-red-500 text-red-600 focus:ring-red-500'
                      : 'border-gray-300 text-maroon-600'
                  )}
                />
              </div>
              <div className="ml-3 text-sm">
                <label 
                  htmlFor={optionId} 
                  className={cn(
                    'font-medium cursor-pointer',
                    isDisabled ? 'text-gray-400 cursor-not-allowed' : 'text-gray-700'
                  )}
                >
                  {option.label}
                </label>
                {option.description && (
                  <p className="text-gray-600 mt-1">
                    {option.description}
                  </p>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      {hasError && (
        <div id={errorId} className="space-y-1" role="alert">
          {errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600">
              {error}
            </p>
          ))}
        </div>
      )}
    </fieldset>
  );
}