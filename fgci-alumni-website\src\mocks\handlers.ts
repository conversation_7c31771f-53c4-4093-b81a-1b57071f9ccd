import { http, HttpResponse } from 'msw';
import { mockChapters } from './data/chapters';
import { mockSets } from './data/sets';
import { mockEvents } from './data/events';
import { mockGalleryImages, mockGalleryAlbums } from './data/gallery';
import { mockAlumniDirectory } from './data/alumni';
import { memorials } from './data/memorials';
import { ApiResponse, PaginatedResponse, SearchResponse, Memorial, CondolenceFormData } from '../lib/types';

// Helper function to create paginated response
function createPaginatedResponse<T>(
  data: T[],
  page: number = 1,
  limit: number = 10,
  query?: string,
  filters?: Record<string, unknown>
): PaginatedResponse<T> {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedData = data.slice(startIndex, endIndex);
  const totalPages = Math.ceil(data.length / limit);

  return {
    data: paginatedData,
    success: true,
    meta: {
      total: data.length,
      page,
      limit,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
      totalPages,
      timestamp: new Date().toISOString()
    }
  };
}

// Helper function to create search response
function createSearchResponse<T>(
  data: T[],
  page: number = 1,
  limit: number = 10,
  query?: string,
  filters?: Record<string, unknown>,
  searchTime: number = Math.random() * 100
): SearchResponse<T> {
  const paginatedResponse = createPaginatedResponse(data, page, limit);
  
  return {
    ...paginatedResponse,
    meta: {
      ...paginatedResponse.meta,
      query,
      filters,
      searchTime
    }
  };
}

// Helper function to simulate network delay
const delay = (ms: number = 300) => new Promise(resolve => setTimeout(resolve, ms));

export const handlers = [
  // Chapters API
  http.get('/api/chapters', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search');
    const type = url.searchParams.get('type');
    const region = url.searchParams.get('region');

    let filteredChapters = [...mockChapters];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredChapters = filteredChapters.filter(chapter =>
        chapter.name.toLowerCase().includes(searchLower) ||
        chapter.region?.toLowerCase().includes(searchLower) ||
        chapter.summary?.toLowerCase().includes(searchLower)
      );
    }

    // Apply type filter
    if (type) {
      filteredChapters = filteredChapters.filter(chapter => chapter.type === type);
    }

    // Apply region filter
    if (region) {
      filteredChapters = filteredChapters.filter(chapter => 
        chapter.region?.toLowerCase().includes(region.toLowerCase())
      );
    }

    return HttpResponse.json(
      createPaginatedResponse(filteredChapters, page, limit)
    );
  }),

  http.get('/api/chapters/:slug', async ({ params }) => {
    await delay();
    
    const { slug } = params;
    const chapter = mockChapters.find(c => c.slug === slug);

    if (!chapter) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Chapter not found',
          data: null
        } as ApiResponse<null>,
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: chapter,
      meta: {
        timestamp: new Date().toISOString()
      }
    } as ApiResponse<typeof chapter>);
  }),

  http.get('/api/chapters/:slug/activities', async ({ params, request }) => {
    await delay();
    
    const { slug } = params;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    const chapter = mockChapters.find(c => c.slug === slug);

    if (!chapter) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Chapter not found',
          data: []
        } as ApiResponse<[]>,
        { status: 404 }
      );
    }

    return HttpResponse.json(
      createPaginatedResponse(chapter.activities, page, limit)
    );
  }),

  // Sets API
  http.get('/api/sets', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search');
    const year = url.searchParams.get('year');

    let filteredSets = [...mockSets];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredSets = filteredSets.filter(set =>
        set.name.toLowerCase().includes(searchLower) ||
        set.motto?.toLowerCase().includes(searchLower) ||
        set.year.toString().includes(search)
      );
    }

    // Apply year filter
    if (year) {
      filteredSets = filteredSets.filter(set => set.year.toString() === year);
    }

    // Sort by year (descending)
    filteredSets.sort((a, b) => b.year - a.year);

    return HttpResponse.json(
      createPaginatedResponse(filteredSets, page, limit)
    );
  }),

  http.get('/api/sets/:slug', async ({ params }) => {
    await delay();
    
    const { slug } = params;
    const set = mockSets.find(s => s.slug === slug);

    if (!set) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Set not found',
          data: null
        } as ApiResponse<null>,
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: set,
      meta: {
        timestamp: new Date().toISOString()
      }
    } as ApiResponse<typeof set>);
  }),

  http.get('/api/sets/:slug/activities', async ({ params, request }) => {
    await delay();
    
    const { slug } = params;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    const set = mockSets.find(s => s.slug === slug);

    if (!set) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Set not found',
          data: []
        } as ApiResponse<[]>,
        { status: 404 }
      );
    }

    return HttpResponse.json(
      createPaginatedResponse(set.activities, page, limit)
    );
  }),

  // Events API
  http.get('/api/events', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search');
    const status = url.searchParams.get('status');
    const organizer = url.searchParams.get('organizer');

    let filteredEvents = [...mockEvents];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredEvents = filteredEvents.filter(event =>
        event.title.toLowerCase().includes(searchLower) ||
        event.description.toLowerCase().includes(searchLower) ||
        event.location.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (status) {
      filteredEvents = filteredEvents.filter(event => event.status === status);
    }

    // Apply organizer filter
    if (organizer) {
      filteredEvents = filteredEvents.filter(event => 
        event.organizer.toLowerCase().includes(organizer.toLowerCase())
      );
    }

    // Sort by date (upcoming first, then by date)
    filteredEvents.sort((a, b) => {
      if (a.status === 'upcoming' && b.status !== 'upcoming') return -1;
      if (b.status === 'upcoming' && a.status !== 'upcoming') return 1;
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });

    return HttpResponse.json(
      createPaginatedResponse(filteredEvents, page, limit)
    );
  }),

  http.get('/api/events/:slug', async ({ params }) => {
    await delay();
    
    const { slug } = params;
    const event = mockEvents.find(e => e.slug === slug);

    if (!event) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Event not found',
          data: null
        } as ApiResponse<null>,
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: event,
      meta: {
        timestamp: new Date().toISOString()
      }
    } as ApiResponse<typeof event>);
  }),

  // Gallery API
  http.get('/api/gallery', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const search = url.searchParams.get('search');
    const album = url.searchParams.get('album');
    const tag = url.searchParams.get('tag');

    let filteredImages = [...mockGalleryImages];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredImages = filteredImages.filter(image =>
        image.alt.toLowerCase().includes(searchLower) ||
        image.caption?.toLowerCase().includes(searchLower) ||
        image.photographer?.toLowerCase().includes(searchLower)
      );
    }

    // Apply album filter
    if (album) {
      filteredImages = filteredImages.filter(image => image.album === album);
    }

    // Apply tag filter
    if (tag) {
      filteredImages = filteredImages.filter(image => 
        image.tags?.includes(tag)
      );
    }

    // Sort by date (newest first)
    filteredImages.sort((a, b) => {
      if (!a.date || !b.date) return 0;
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    });

    return HttpResponse.json(
      createPaginatedResponse(filteredImages, page, limit)
    );
  }),

  http.get('/api/gallery/albums', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search');

    let filteredAlbums = [...mockGalleryAlbums];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredAlbums = filteredAlbums.filter(album =>
        album.name.toLowerCase().includes(searchLower) ||
        album.description?.toLowerCase().includes(searchLower)
      );
    }

    // Sort by updated date (newest first)
    filteredAlbums.sort((a, b) => {
      const dateA = new Date(a.updatedDate || a.createdDate);
      const dateB = new Date(b.updatedDate || b.createdDate);
      return dateB.getTime() - dateA.getTime();
    });

    return HttpResponse.json(
      createPaginatedResponse(filteredAlbums, page, limit)
    );
  }),

  http.get('/api/gallery/albums/:id', async ({ params }) => {
    await delay();
    
    const { id } = params;
    const album = mockGalleryAlbums.find(a => a.id === id);

    if (!album) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Album not found',
          data: null
        } as ApiResponse<null>,
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: album,
      meta: {
        timestamp: new Date().toISOString()
      }
    } as ApiResponse<typeof album>);
  }),

  // ExcoBot Alumni Directory API
  http.get('/api/excobot/search', async ({ request }) => {
    await delay(500); // Slightly longer delay for search
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const query = url.searchParams.get('q');
    const chapter = url.searchParams.get('chapter');
    const graduationYear = url.searchParams.get('graduationYear');
    const currentRole = url.searchParams.get('currentRole');
    const location = url.searchParams.get('location');
    const position = url.searchParams.get('position');
    const availability = url.searchParams.get('availability');
    const sort = url.searchParams.get('sort') || 'name';

    let filteredAlumni = [...mockAlumniDirectory];

    // Apply search query
    if (query) {
      const queryLower = query.toLowerCase();
      filteredAlumni = filteredAlumni.filter(person =>
        person.name.toLowerCase().includes(queryLower) ||
        person.role?.toLowerCase().includes(queryLower) ||
        person.chapter?.toLowerCase().includes(queryLower) ||
        person.currentLocation?.toLowerCase().includes(queryLower) ||
        person.bio?.toLowerCase().includes(queryLower) ||
        person.graduationYear?.toString().includes(query)
      );
    }

    // Apply chapter filter
    if (chapter) {
      filteredAlumni = filteredAlumni.filter(person => 
        person.chapter === chapter
      );
    }

    // Apply graduation year filter
    if (graduationYear) {
      filteredAlumni = filteredAlumni.filter(person => 
        person.graduationYear?.toString() === graduationYear
      );
    }

    // Apply current role filter
    if (currentRole) {
      filteredAlumni = filteredAlumni.filter(person => 
        person.role?.toLowerCase().includes(currentRole.toLowerCase())
      );
    }

    // Apply location filter
    if (location) {
      filteredAlumni = filteredAlumni.filter(person => 
        person.currentLocation === location
      );
    }

    // Apply position type filter
    if (position) {
      const executiveRoles = ['President', 'Vice President', 'Secretary', 'Treasurer'];
      const leaderRoles = ['Set Leader'];
      const professionalRoles = ['Engineer', 'Doctor', 'Manager', 'Analyst', 'Director'];
      const academicRoles = ['Professor', 'Teacher', 'Researcher'];

      filteredAlumni = filteredAlumni.filter(person => {
        const role = person.role || '';
        switch (position) {
          case 'executive':
            return executiveRoles.some(execRole => role.includes(execRole));
          case 'leader':
            return leaderRoles.some(leaderRole => role.includes(leaderRole));
          case 'professional':
            return professionalRoles.some(profRole => role.includes(profRole));
          case 'academic':
            return academicRoles.some(acadRole => role.includes(acadRole));
          default:
            return true;
        }
      });
    }

    // Apply sorting
    filteredAlumni.sort((a, b) => {
      switch (sort) {
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'year':
          return (b.graduationYear || 0) - (a.graduationYear || 0);
        case 'year-desc':
          return (a.graduationYear || 0) - (b.graduationYear || 0);
        case 'chapter':
          return (a.chapter || '').localeCompare(b.chapter || '');
        case 'role':
          return (a.role || '').localeCompare(b.role || '');
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    const filters = { chapter, graduationYear, currentRole, location, position, availability };
    const searchTime = Math.random() * 200 + 50; // 50-250ms

    return HttpResponse.json({
      data: filteredAlumni,
      success: true,
      meta: {
        total: filteredAlumni.length,
        query: query || undefined,
        filters,
        searchTime,
        timestamp: new Date().toISOString()
      }
    });
  }),

  // ExcoBot suggestions API
  http.get('/api/excobot/suggestions', async ({ request }) => {
    await delay(200);
    
    const url = new URL(request.url);
    const query = url.searchParams.get('q');
    
    if (!query || query.length < 2) {
      return HttpResponse.json({ suggestions: [] });
    }

    const queryLower = query.toLowerCase();
    const suggestions = new Set<string>();

    // Add name suggestions
    mockAlumniDirectory.forEach(person => {
      if (person.name.toLowerCase().includes(queryLower)) {
        suggestions.add(person.name);
      }
      if (person.role?.toLowerCase().includes(queryLower)) {
        suggestions.add(person.role);
      }
      if (person.chapter?.toLowerCase().includes(queryLower)) {
        suggestions.add(person.chapter);
      }
      if (person.currentLocation?.toLowerCase().includes(queryLower)) {
        suggestions.add(person.currentLocation);
      }
    });

    return HttpResponse.json({
      suggestions: Array.from(suggestions).slice(0, 8)
    });
  }),

  // ExcoBot filter options API
  http.get('/api/excobot/filter-options', async () => {
    await delay(200);
    
    const chapters = [...new Set(mockAlumniDirectory.map(p => p.chapter).filter(Boolean))];
    const graduationYears = [...new Set(mockAlumniDirectory.map(p => p.graduationYear).filter(Boolean))].sort((a, b) => b - a);
    const roles = [...new Set(mockAlumniDirectory.map(p => p.role).filter(Boolean))];
    const locations = [...new Set(mockAlumniDirectory.map(p => p.currentLocation).filter(Boolean))];

    return HttpResponse.json({
      chapters,
      graduationYears,
      roles,
      locations
    });
  }),

  // Contact Form API
  http.post('/api/contact', async ({ request }) => {
    await delay(1000); // Simulate form submission delay
    
    try {
      const formData = await request.json() as any;
      
      // Basic validation
      if (!formData?.name || !formData?.email || !formData?.message) {
        return HttpResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            data: null,
            fieldErrors: {
              name: !formData?.name ? ['Name is required'] : [],
              email: !formData?.email ? ['Email is required'] : [],
              message: !formData?.message ? ['Message is required'] : []
            }
          },
          { status: 400 }
        );
      }

      // Simulate successful submission
      return HttpResponse.json({
        success: true,
        data: {
          id: `contact-${Date.now()}`,
          message: 'Thank you for your message. We will get back to you soon!',
          submittedAt: new Date().toISOString()
        },
        meta: {
          timestamp: new Date().toISOString()
        }
      } as ApiResponse<{
        id: string;
        message: string;
        submittedAt: string;
      }>);
    } catch (error) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          data: null
        } as ApiResponse<null>,
        { status: 400 }
      );
    }
  }),

  // Memorial API
  http.get('/api/memorials', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search');
    const chapter = url.searchParams.get('chapter');
    const graduationYear = url.searchParams.get('graduationYear');

    let filteredMemorials = [...memorials];

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredMemorials = filteredMemorials.filter(memorial =>
        memorial.person.name.toLowerCase().includes(searchLower) ||
        memorial.person.role?.toLowerCase().includes(searchLower) ||
        memorial.person.chapter?.toLowerCase().includes(searchLower) ||
        memorial.tribute.toLowerCase().includes(searchLower)
      );
    }

    // Apply chapter filter
    if (chapter) {
      filteredMemorials = filteredMemorials.filter(memorial => 
        memorial.person.chapter === chapter
      );
    }

    // Apply graduation year filter (by decade)
    if (graduationYear) {
      const decade = graduationYear.replace('s', '');
      filteredMemorials = filteredMemorials.filter(memorial => {
        if (!memorial.person.graduationYear) return false;
        const personDecade = Math.floor(memorial.person.graduationYear / 10) * 10;
        return personDecade.toString() === decade;
      });
    }

    // Sort by date of passing (most recent first)
    filteredMemorials.sort((a, b) => 
      new Date(b.dateOfPassing).getTime() - new Date(a.dateOfPassing).getTime()
    );

    return HttpResponse.json(
      createPaginatedResponse(filteredMemorials, page, limit)
    );
  }),

  http.get('/api/memorials/:id', async ({ params }) => {
    await delay();
    
    const { id } = params;
    const memorial = memorials.find(m => m.id === id);

    if (!memorial) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Memorial not found',
          data: null
        } as ApiResponse<null>,
        { status: 404 }
      );
    }

    return HttpResponse.json({
      success: true,
      data: memorial,
      meta: {
        timestamp: new Date().toISOString()
      }
    } as ApiResponse<Memorial>);
  }),

  http.post('/api/memorials/:id/condolences', async ({ params, request }) => {
    await delay(1000); // Simulate form submission delay
    
    try {
      const { id } = params;
      const condolenceData = await request.json() as CondolenceFormData;
      
      // Basic validation
      if (!condolenceData?.name || !condolenceData?.message) {
        return HttpResponse.json(
          {
            success: false,
            error: 'Missing required fields',
            data: null,
            fieldErrors: {
              name: !condolenceData?.name ? ['Name is required'] : [],
              message: !condolenceData?.message ? ['Message is required'] : []
            }
          },
          { status: 400 }
        );
      }

      // Find the memorial
      const memorial = memorials.find(m => m.id === id);
      if (!memorial) {
        return HttpResponse.json(
          {
            success: false,
            error: 'Memorial not found',
            data: null
          } as ApiResponse<null>,
          { status: 404 }
        );
      }

      // Create new condolence
      const newCondolence = {
        id: `cond-${Date.now()}`,
        name: condolenceData.name,
        message: condolenceData.message,
        date: new Date().toISOString(),
        approved: false // Requires moderation
      };

      // Add to memorial (in real app, this would be saved to database)
      memorial.condolences = memorial.condolences || [];
      memorial.condolences.push(newCondolence);

      return HttpResponse.json({
        success: true,
        data: {
          id: newCondolence.id,
          message: 'Your condolence message has been submitted for review.',
          submittedAt: newCondolence.date
        },
        meta: {
          timestamp: new Date().toISOString()
        }
      } as ApiResponse<{
        id: string;
        message: string;
        submittedAt: string;
      }>);
    } catch (error) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          data: null
        } as ApiResponse<null>,
        { status: 400 }
      );
    }
  }),

  http.get('/api/memorials/:id/condolences', async ({ params, request }) => {
    await delay();
    
    const { id } = params;
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    const memorial = memorials.find(m => m.id === id);

    if (!memorial) {
      return HttpResponse.json(
        {
          success: false,
          error: 'Memorial not found',
          data: []
        } as ApiResponse<[]>,
        { status: 404 }
      );
    }

    // Only return approved condolences
    const approvedCondolences = memorial.condolences?.filter(c => c.approved) || [];
    
    return HttpResponse.json(
      createPaginatedResponse(approvedCondolences, page, limit)
    );
  }),

  http.get('/api/memorials/search', async ({ request }) => {
    await delay(300);
    
    const url = new URL(request.url);
    const query = url.searchParams.get('q');
    const chapter = url.searchParams.get('chapter');
    const graduationYear = url.searchParams.get('graduationYear');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');

    if (!query || query.length < 2) {
      return HttpResponse.json(
        createSearchResponse([], page, limit, query, { chapter, graduationYear })
      );
    }

    let filteredMemorials = [...memorials];
    const queryLower = query.toLowerCase();

    // Apply search query
    filteredMemorials = filteredMemorials.filter(memorial =>
      memorial.person.name.toLowerCase().includes(queryLower) ||
      memorial.person.role?.toLowerCase().includes(queryLower) ||
      memorial.person.chapter?.toLowerCase().includes(queryLower) ||
      memorial.tribute.toLowerCase().includes(queryLower)
    );

    // Apply additional filters
    if (chapter) {
      filteredMemorials = filteredMemorials.filter(memorial => 
        memorial.person.chapter === chapter
      );
    }

    if (graduationYear) {
      const decade = graduationYear.replace('s', '');
      filteredMemorials = filteredMemorials.filter(memorial => {
        if (!memorial.person.graduationYear) return false;
        const personDecade = Math.floor(memorial.person.graduationYear / 10) * 10;
        return personDecade.toString() === decade;
      });
    }

    const searchTime = Math.random() * 150 + 50; // 50-200ms

    return HttpResponse.json(
      createSearchResponse(filteredMemorials, page, limit, query, { chapter, graduationYear }, searchTime)
    );
  }),

  http.get('/api/memorials/stats', async () => {
    await delay(200);
    
    // Calculate statistics
    const total = memorials.length;
    const byDecade: Record<string, number> = {};
    const byChapter: Record<string, number> = {};
    
    memorials.forEach(memorial => {
      // Count by decade
      if (memorial.person.graduationYear) {
        const decade = Math.floor(memorial.person.graduationYear / 10) * 10;
        const decadeKey = `${decade}s`;
        byDecade[decadeKey] = (byDecade[decadeKey] || 0) + 1;
      }
      
      // Count by chapter
      if (memorial.person.chapter) {
        byChapter[memorial.person.chapter] = (byChapter[memorial.person.chapter] || 0) + 1;
      }
    });

    // Count recent memorials (last 2 years)
    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
    const recentCount = memorials.filter(memorial => 
      new Date(memorial.dateOfPassing) >= twoYearsAgo
    ).length;

    return HttpResponse.json({
      success: true,
      data: {
        total,
        byDecade,
        byChapter,
        recentCount
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    } as ApiResponse<{
      total: number;
      byDecade: Record<string, number>;
      byChapter: Record<string, number>;
      recentCount: number;
    }>);
  }),

  // Health check endpoint
  http.get('/api/health', async () => {
    await delay(100);
    
    return HttpResponse.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        environment: 'development'
      },
      meta: {
        timestamp: new Date().toISOString()
      }
    });
  })
];