import { render, screen, fireEvent } from '@/lib/test-utils';
import { SkipNav } from '../SkipNav';
import { axe, toHaveNoViolations } from 'jest-axe';

expect.extend(toHaveNoViolations);

describe('SkipNav Component', () => {
  it('renders skip navigation link', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toBeInTheDocument();
    expect(skipLink).toHaveAttribute('href', '#main-content');
  });

  it('renders with custom target', () => {
    render(<SkipNav target="#custom-target" />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveAttribute('href', '#custom-target');
  });

  it('renders with custom text', () => {
    render(<SkipNav text="Skip to navigation" />);
    
    expect(screen.getByText('Skip to navigation')).toBeInTheDocument();
  });

  it('is visually hidden by default', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('sr-only');
  });

  it('becomes visible when focused', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    
    fireEvent.focus(skipLink);
    expect(skipLink).toHaveClass('focus:not-sr-only');
  });

  it('has proper positioning when focused', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('focus:absolute', 'focus:top-4', 'focus:left-4');
  });

  it('has proper z-index for visibility', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('focus:z-50');
  });

  it('handles keyboard navigation', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    
    // Should be focusable with Tab
    skipLink.focus();
    expect(document.activeElement).toBe(skipLink);
    
    // Should activate with Enter
    fireEvent.keyDown(skipLink, { key: 'Enter' });
    // Note: In a real implementation, this would scroll to the target
  });

  it('handles click events', () => {
    const mockScrollIntoView = jest.fn();
    
    // Mock the target element
    const targetElement = document.createElement('div');
    targetElement.id = 'main-content';
    targetElement.scrollIntoView = mockScrollIntoView;
    document.body.appendChild(targetElement);
    
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    fireEvent.click(skipLink);
    
    expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: 'smooth' });
    
    document.body.removeChild(targetElement);
  });

  it('handles missing target gracefully', () => {
    render(<SkipNav target="#non-existent" />);
    
    const skipLink = screen.getByText('Skip to main content');
    
    // Should not throw error when target doesn't exist
    expect(() => {
      fireEvent.click(skipLink);
    }).not.toThrow();
  });

  it('renders multiple skip links', () => {
    render(
      <div>
        <SkipNav target="#main-content" text="Skip to main content" />
        <SkipNav target="#navigation" text="Skip to navigation" />
        <SkipNav target="#footer" text="Skip to footer" />
      </div>
    );
    
    expect(screen.getByText('Skip to main content')).toBeInTheDocument();
    expect(screen.getByText('Skip to navigation')).toBeInTheDocument();
    expect(screen.getByText('Skip to footer')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveAttribute('role', 'link');
    expect(skipLink.tagName).toBe('A');
  });

  it('has no accessibility violations', async () => {
    const { container } = render(<SkipNav />);
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('works with keyboard-only navigation', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    
    // Should be the first focusable element
    fireEvent.keyDown(document.body, { key: 'Tab' });
    expect(document.activeElement).toBe(skipLink);
  });

  it('renders with custom styling', () => {
    render(<SkipNav className="custom-skip-nav" />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('custom-skip-nav');
  });

  it('supports different focus styles', () => {
    render(<SkipNav focusStyle="outline" />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('focus:outline-2');
  });

  it('handles focus and blur events', () => {
    const onFocus = jest.fn();
    const onBlur = jest.fn();
    
    render(<SkipNav onFocus={onFocus} onBlur={onBlur} />);
    
    const skipLink = screen.getByText('Skip to main content');
    
    fireEvent.focus(skipLink);
    expect(onFocus).toHaveBeenCalledTimes(1);
    
    fireEvent.blur(skipLink);
    expect(onBlur).toHaveBeenCalledTimes(1);
  });

  it('integrates with screen readers', () => {
    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    
    // Should be announced by screen readers
    expect(skipLink).toHaveAttribute('href');
    expect(skipLink.textContent).toBeTruthy();
  });

  it('works in different viewport sizes', () => {
    // Test mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });

    const { rerender } = render(<SkipNav />);
    
    let skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('focus:left-4');
    
    // Test desktop viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    rerender(<SkipNav />);
    
    skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('focus:left-4');
  });

  it('maintains focus visibility in high contrast mode', () => {
    // Mock high contrast media query
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-contrast: high)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(<SkipNav />);
    
    const skipLink = screen.getByText('Skip to main content');
    expect(skipLink).toHaveClass('focus:bg-white', 'focus:text-black');
  });
});