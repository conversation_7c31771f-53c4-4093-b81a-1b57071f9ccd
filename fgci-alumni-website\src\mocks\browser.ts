import { setupWorker } from 'msw/browser';
import { handlers } from './handlers';

const isBrowser = typeof window !== 'undefined';
// This configures a Service Worker with the given request handlers.
export const worker = isBrowser ? setupWorker(...handlers) : undefined as any;

// Start the worker conditionally based on environment
export async function enableMocking() {
  if (typeof window === 'undefined') {
    return;
  }

  // Only enable mocking in development when explicitly requested
  if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_MOCKS === 'true') {
    console.log('🔧 Enabling API mocking with MSW...');
    
    try {
      await worker.start({
        onUnhandledRequest: 'warn',
        serviceWorker: {
          url: '/mockServiceWorker.js'
        }
      });
      
      console.log('✅ MSW worker started successfully');
    } catch (error) {
      console.error('❌ Failed to start MSW worker:', error);
    }
  }
}

// Export worker for potential use in tests or other contexts
export { worker as mswWorker };