'use client';

import { forwardRef } from 'react';
import { OptimizedImage } from '@/components/gallery/OptimizedImage';
import { RESPONSIVE_SIZES } from '@/lib/imageOptimization';

interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  priority?: boolean;
  usage?: 'hero' | 'gallery' | 'card' | 'avatar' | 'thumbnail';
  aspectRatio?: string;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * ResponsiveImage component that automatically handles responsive sizing
 * and optimization based on usage context
 */
export const ResponsiveImage = forwardRef<HTMLDivElement, ResponsiveImageProps>(
  ({
    src,
    alt,
    className,
    priority = false,
    usage = 'gallery',
    aspectRatio,
    onLoad,
    onError
  }, ref) => {
    // Get responsive configuration based on usage
    const getResponsiveConfig = () => {
      switch (usage) {
        case 'hero':
          return {
            sizes: RESPONSIVE_SIZES.hero,
            quality: 85,
            aspectRatio: aspectRatio || '16/9'
          };
        case 'card':
          return {
            sizes: RESPONSIVE_SIZES.card,
            quality: 75,
            aspectRatio: aspectRatio || '4/3'
          };
        case 'avatar':
          return {
            sizes: RESPONSIVE_SIZES.avatar,
            quality: 70,
            aspectRatio: aspectRatio || '1/1'
          };
        case 'thumbnail':
          return {
            sizes: RESPONSIVE_SIZES.gallery,
            quality: 65,
            aspectRatio: aspectRatio || '1/1'
          };
        default:
          return {
            sizes: RESPONSIVE_SIZES.gallery,
            quality: 75,
            aspectRatio: aspectRatio || '4/3'
          };
      }
    };

    const config = getResponsiveConfig();

    return (
      <OptimizedImage
        ref={ref}
        src={src}
        alt={alt}
        className={className}
        priority={priority}
        usage={usage}
        sizes={config.sizes}
        quality={config.quality}
        aspectRatio={config.aspectRatio}
        onLoad={onLoad}
        onError={onError}
        enablePerformanceMonitoring={process.env.NODE_ENV === 'development'}
      />
    );
  }
);

ResponsiveImage.displayName = 'ResponsiveImage';