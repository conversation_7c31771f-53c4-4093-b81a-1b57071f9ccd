'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, Button } from '@/components/ui';
import { GalleryGrid } from '@/components/gallery/GalleryGrid';
import { GallerySkeleton, AlbumSkeleton } from '@/components/gallery/GallerySkeleton';
import { GalleryErrorBoundary } from '@/components/gallery/GalleryErrorBoundary';
import { useGalleryImages, useGalleryAlbums } from '@/hooks/useGallery';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { usePerformanceMonitor, useCoreWebVitals } from '@/hooks/usePerformanceMonitor';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  PhotoIcon, 
  EyeIcon,
  CalendarDaysIcon,
  ChevronLeftIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import type { GalleryImage } from '@/lib/types';

const albumCategories = [
  { value: 'all', label: 'All Albums' },
  { value: 'homecoming-2023', label: 'Homecoming 2023' },
  { value: 'lagos-chapter-2024', label: 'Lagos Chapter 2024' },
  { value: 'class-2015-reunion', label: 'Class of 2015 Reunion' },
  { value: 'tech-conference-2023', label: 'Tech Conference 2023' },
  { value: 'sports-festival-2023', label: 'Sports Festival 2023' },
  { value: 'medical-outreach-2024', label: 'Medical Outreach 2024' },
  { value: 'graduation-ceremonies', label: 'Graduation Ceremonies' }
];

const imageTags = [
  { value: 'all', label: 'All Tags' },
  { value: 'homecoming', label: 'Homecoming' },
  { value: 'reunion', label: 'Reunion' },
  { value: 'tech', label: 'Technology' },
  { value: 'sports', label: 'Sports' },
  { value: 'medical', label: 'Medical' },
  { value: 'graduation', label: 'Graduation' },
  { value: 'awards', label: 'Awards' },
  { value: 'cultural', label: 'Cultural' }
];

interface GalleryViewMode {
  mode: 'albums' | 'images';
  selectedAlbum?: string;
}

export default function GalleryPage() {
  // Performance monitoring
  usePerformanceMonitor({
    enabled: process.env.NODE_ENV === 'development',
    onMetrics: (metrics) => {
      // Could send to analytics service in production
      console.log('Gallery performance:', metrics);
    }
  });
  useCoreWebVitals();

  // State management
  const [viewMode, setViewMode] = useState<GalleryViewMode>({ mode: 'albums' });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAlbum, setSelectedAlbum] = useState('all');
  const [selectedTag, setSelectedTag] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [allImages, setAllImages] = useState<GalleryImage[]>([]);

  // API queries
  const albumsQuery = useGalleryAlbums({ 
    page: 1, 
    limit: 50,
    search: searchQuery 
  });

  const imagesQuery = useGalleryImages({
    page: currentPage,
    limit: 20,
    album: selectedAlbum !== 'all' ? selectedAlbum : undefined,
    tag: selectedTag !== 'all' ? selectedTag : undefined,
    search: searchQuery
  });

  // Update images list for infinite scroll
  useEffect(() => {
    if (imagesQuery.data?.data) {
      if (currentPage === 1) {
        setAllImages(imagesQuery.data.data);
      } else {
        setAllImages(prev => [...prev, ...imagesQuery.data.data]);
      }
      setHasNextPage(imagesQuery.data.meta?.hasNext ?? false);
    }
  }, [imagesQuery.data, currentPage]);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
    setAllImages([]);
  }, [selectedAlbum, selectedTag, searchQuery]);

  // Infinite scroll handler with debouncing
  const loadMoreImages = useCallback(() => {
    if (hasNextPage && !imagesQuery.isLoading && !imagesQuery.isFetching) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasNextPage, imagesQuery.isLoading, imagesQuery.isFetching]);

  // Infinite scroll hook with performance optimizations
  const sentinelRef = useInfiniteScroll({
    hasNextPage,
    isLoading: imagesQuery.isLoading,
    onLoadMore: loadMoreImages,
    enabled: viewMode.mode === 'images',
    debounceMs: 150, // Slightly higher debounce for better performance
    rootMargin: '300px' // Load earlier for smoother experience
  });

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleAlbumSelect = (albumId: string) => {
    if (albumId === 'all') {
      setViewMode({ mode: 'images' });
      setSelectedAlbum('all');
    } else {
      setViewMode({ mode: 'images', selectedAlbum: albumId });
      setSelectedAlbum(albumId);
    }
  };

  const handleBackToAlbums = () => {
    setViewMode({ mode: 'albums' });
    setSelectedAlbum('all');
    setSelectedTag('all');
    setSearchQuery('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getAlbumById = (albumId: string) => {
    return albumsQuery.data?.data.find(album => album.id === albumId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b">
        <div className="container-custom py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
              Photo Gallery
            </h1>
            <p className="body-large text-gray-600 mb-8">
              Explore our rich collection of memories captured through the years. From annual 
              homecomings to chapter events, fundraising galas to memorial services - relive 
              the moments that define our alumni community.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <PhotoIcon className="w-4 h-4" />
                <span>
                  {albumsQuery.data?.data.reduce((sum, album) => sum + album.images.length, 0) || 0} Total Photos
                </span>
              </div>
              <div className="flex items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4" />
                <span>{albumsQuery.data?.data.length || 0} Albums</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation and Search Section */}
      <div className="bg-white border-b">
        <div className="container-custom py-6">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Navigation */}
            <div className="flex items-center gap-4">
              {viewMode.mode === 'images' && (
                <Button
                  variant="outline"
                  onClick={handleBackToAlbums}
                  className="flex items-center gap-2"
                >
                  <ChevronLeftIcon className="w-4 h-4" />
                  Back to Albums
                </Button>
              )}
              
              {viewMode.selectedAlbum && (
                <div className="text-sm text-gray-600">
                  Viewing: <span className="font-medium">{getAlbumById(viewMode.selectedAlbum)?.name}</span>
                </div>
              )}
            </div>

            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder={viewMode.mode === 'albums' ? "Search albums..." : "Search images..."}
                value={searchQuery}
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500"
              />
            </div>
          </div>

          {/* Filters for Images View */}
          {viewMode.mode === 'images' && (
            <div className="mt-4 flex flex-wrap gap-4 items-center">
              {/* Album Filter */}
              <div className="flex items-center gap-2">
                <FunnelIcon className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-600">Album:</span>
                <div className="flex gap-2 flex-wrap">
                  {albumCategories.map((category) => (
                    <button
                      key={category.value}
                      onClick={() => handleAlbumSelect(category.value)}
                      className={cn(
                        'px-3 py-1.5 text-sm rounded-full border transition-colors',
                        selectedAlbum === category.value
                          ? 'bg-maroon-700 text-white border-maroon-700'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-maroon-300'
                      )}
                    >
                      {category.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Tag Filter */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Tag:</span>
                <div className="flex gap-2 flex-wrap">
                  {imageTags.map((tag) => (
                    <button
                      key={tag.value}
                      onClick={() => setSelectedTag(tag.value)}
                      className={cn(
                        'px-3 py-1.5 text-sm rounded-full border transition-colors',
                        selectedTag === tag.value
                          ? 'bg-gold-500 text-white border-gold-500'
                          : 'bg-white text-gray-600 border-gray-300 hover:border-gold-300'
                      )}
                    >
                      {tag.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content Area */}
      <div className="container-custom py-12">
        <GalleryErrorBoundary>
          {viewMode.mode === 'albums' ? (
          // Albums Grid View
          <>
            {albumsQuery.isLoading ? (
              <AlbumSkeleton count={6} />
            ) : albumsQuery.error ? (
              <div className="text-center py-12">
                <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="heading-3 text-xl text-gray-600 mb-2">Failed to Load Albums</h3>
                <p className="text-gray-500 mb-4">
                  {albumsQuery.error.message || 'Unable to load gallery albums. Please try again.'}
                </p>
                <Button
                  variant="outline"
                  onClick={() => albumsQuery.refetch()}
                >
                  Try Again
                </Button>
              </div>
            ) : albumsQuery.data?.data.length === 0 ? (
              <div className="text-center py-12">
                <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="heading-3 text-xl text-gray-600 mb-2">No Albums Found</h3>
                <p className="text-gray-500 mb-4">
                  {searchQuery 
                    ? 'Try adjusting your search criteria.'
                    : 'No photo albums are currently available.'}
                </p>
                {searchQuery && (
                  <Button
                    variant="outline"
                    onClick={() => setSearchQuery('')}
                  >
                    Clear Search
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {albumsQuery.data?.data.map((album) => (
                  <Card
                    key={album.id}
                    variant="elevated"
                    hover
                    className="group overflow-hidden cursor-pointer"
                    onClick={() => handleAlbumSelect(album.id)}
                  >
                    {/* Album Cover */}
                    <div className="relative h-64 overflow-hidden">
                      <img
                        src={album.coverImage?.url || album.images[0]?.url}
                        alt={album.name}
                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
                      <div className="absolute top-4 right-4">
                        <span className="px-2 py-1 text-xs font-bold rounded-full bg-black/60 text-white">
                          {album.images.length} photos
                        </span>
                      </div>
                      
                      {/* Hover Overlay */}
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
                          <EyeIcon className="w-6 h-6 text-gray-900" />
                        </div>
                      </div>
                    </div>

                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                        {album.name}
                      </h3>
                      <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                        {album.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{formatDate(album.createdDate)}</span>
                        <div className="flex items-center">
                          <PhotoIcon className="w-3 h-3 mr-1" />
                          <span>{album.images.length}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        ) : (
          // Images Grid View with Infinite Scroll
          <>
            {currentPage === 1 && imagesQuery.isLoading ? (
              <GallerySkeleton columns={4} count={20} />
            ) : imagesQuery.error ? (
              <div className="text-center py-12">
                <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="heading-3 text-xl text-gray-600 mb-2">Failed to Load Images</h3>
                <p className="text-gray-500 mb-4">
                  {imagesQuery.error.message || 'Unable to load gallery images. Please try again.'}
                </p>
                <Button
                  variant="outline"
                  onClick={() => imagesQuery.refetch()}
                >
                  Try Again
                </Button>
              </div>
            ) : allImages.length === 0 ? (
              <div className="text-center py-12">
                <PhotoIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="heading-3 text-xl text-gray-600 mb-2">No Images Found</h3>
                <p className="text-gray-500 mb-4">
                  {searchQuery || selectedAlbum !== 'all' || selectedTag !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'No images are currently available.'}
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedAlbum('all');
                    setSelectedTag('all');
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            ) : (
              <>
                <GalleryGrid
                  images={allImages}
                  columns={4}
                  className="mb-8"
                />
                
                {/* Loading indicator for infinite scroll */}
                {(imagesQuery.isLoading || imagesQuery.isFetching) && currentPage > 1 && (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-maroon-700 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading more images...</p>
                  </div>
                )}
                
                {/* Scroll sentinel for infinite scroll */}
                {hasNextPage && !imagesQuery.isLoading && (
                  <div ref={sentinelRef} className="h-4" />
                )}
                
                {/* End of results indicator */}
                {!hasNextPage && allImages.length > 0 && (
                  <div className="text-center py-8 text-gray-500 border-t border-gray-200 pt-8">
                    <PhotoIcon className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="font-medium">You've reached the end of the gallery</p>
                    <p className="text-sm mt-1">
                      Showing {allImages.length} image{allImages.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                )}
              </>
            )}
          </>
        )}
        </GalleryErrorBoundary>
      </div>
    </div>
  );
}
