{"buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/**/*.tsx": {"maxDuration": 30}, "app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(html|json|xml))", "headers": [{"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/alumni", "destination": "/excobot", "permanent": true}, {"source": "/directory", "destination": "/excobot", "permanent": true}], "rewrites": [{"source": "/storybook/:path*", "destination": "https://your-storybook-url.chromatic.com/:path*"}], "env": {"NEXT_PUBLIC_USE_MOCKS": "false", "NODE_ENV": "production"}, "build": {"env": {"NEXT_PUBLIC_USE_MOCKS": "false"}}, "crons": [{"path": "/api/health-check", "schedule": "0 */6 * * *"}]}