import { useQuery } from '@tanstack/react-query';
import { apiClient } from '../lib/api-client';
import { Set, PaginatedResponse, SearchFilters } from '../lib/types';

interface UseSetsOptions {
  page?: number;
  limit?: number;
  search?: string;
  year?: number;
}

export function useSets(options: UseSetsOptions = {}) {
  const { page = 1, limit = 10, search, year } = options;

  return useQuery({
    queryKey: ['sets', { page, limit, search, year }],
    queryFn: async (): Promise<PaginatedResponse<Set>> => {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      
      if (search) {
        params.append('search', search);
      }
      
      if (year) {
        params.append('year', year.toString());
      }

      const response = await apiClient.get(`/api/sets?${params.toString()}`);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useSet(slug: string) {
  return useQuery({
    queryKey: ['set', slug],
    queryFn: async () => {
      const response = await apiClient.get(`/api/sets/${slug}`);
      return response.data;
    },
    enabled: !!slug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}

export function useSetActivities(slug: string, options: { page?: number; limit?: number } = {}) {
  const { page = 1, limit = 10 } = options;

  return useQuery({
    queryKey: ['set-activities', slug, { page, limit }],
    queryFn: async () => {
      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      const response = await apiClient.get(`/api/sets/${slug}/activities?${params.toString()}`);
      return response.data;
    },
    enabled: !!slug,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}