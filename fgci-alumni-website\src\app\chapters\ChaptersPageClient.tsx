'use client';

import { useState, useMemo } from 'react';
import { Button } from '@/components/ui';
import { ChapterCard } from '@/components/chapters/ChapterCard';
import { ChapterFilters } from '@/components/chapters/ChapterFilters';
import { LoadingStates } from '@/components/ui/LoadingStates';
import { Breadcrumbs, StructuredData } from '@/components/seo';
import {
  MapPinIcon,
  UsersIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useChapters } from '@/hooks/useChapters';
import { generateBreadcrumbStructuredData } from '@/lib/seo';

export default function ChaptersPageClient() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');

  // Fetch chapters data with React Query
  const {
    data: chaptersResponse,
    isLoading,
    error,
    refetch
  } = useChapters({
    search: searchQuery.trim() || undefined,
    type: selectedType !== 'all' ? selectedType : undefined,
    limit: 50 // Get more chapters for client-side filtering
  });

  const chapters = chaptersResponse?.data || [];

  // Client-side filtering for real-time search
  const filteredChapters = useMemo(() => {
    let filtered = chapters;

    // Additional client-side search filtering for real-time experience
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(chapter =>
        chapter.name.toLowerCase().includes(query) ||
        chapter.region?.toLowerCase().includes(query) ||
        chapter.summary?.toLowerCase().includes(query)
      );
    }

    return filtered;
  }, [chapters, searchQuery]);

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  const handleTypeChange = (type: string) => {
    setSelectedType(type);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedType('all');
  };

  const totalMembers = chapters.reduce((sum, chapter) => sum + (chapter.memberCount || 0), 0);

  // Structured data for the chapters page
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: 'FGC Ikom Alumni Chapters',
    description: 'Explore FGC Ikom alumni chapters worldwide. Find your local chapter and connect with fellow alumni in your area.',
    url: 'https://www.fgcikomalumni.org.ng/chapters',
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: chapters.length,
      itemListElement: chapters.map((chapter, index) => ({
        '@type': 'Organization',
        position: index + 1,
        name: chapter.name,
        description: chapter.summary,
        url: `https://www.fgcikomalumni.org.ng/chapters/${chapter.slug}`,
        location: chapter.region ? {
          '@type': 'Place',
          name: chapter.region
        } : undefined
      }))
    }
  };

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container-custom py-12">
          <Breadcrumbs />
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="w-16 h-16 text-red-400 mx-auto mb-4" />
            <h3 className="heading-3 text-xl text-gray-900 mb-2">Failed to Load Chapters</h3>
            <p className="text-gray-600 mb-4">
              We're having trouble loading the chapters. Please try again.
            </p>
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <StructuredData data={structuredData} id="chapters-page" />

      <div className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <div className="bg-white border-b border-gold-200">
          <div className="container-custom py-12">
            <Breadcrumbs className="mb-6" />
            <div className="text-center max-w-3xl mx-auto">
              <h1 className="heading-1 text-maroon-800 text-4xl sm:text-5xl mb-4">
                Alumni Chapters
              </h1>
              <div className="h-1 w-16 bg-gold-500 rounded mx-auto mt-2"></div>

              <p className="body-large text-gray-600 mb-8">
                Connect with fellow FGC Ikom alumni in your region through our organized chapters worldwide.
                Each chapter organizes local events, networking opportunities, and community service activities.
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                <div className="flex items-center gap-1">
                  <UsersIcon className="w-4 h-4" />
                  <span>{totalMembers.toLocaleString()} Total Members</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPinIcon className="w-4 h-4" />
                  <span>{chapters.length} Active Chapters</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filter Section */}
        <ChapterFilters
          searchQuery={searchQuery}
          selectedType={selectedType}
          onSearchChange={handleSearchChange}
          onTypeChange={handleTypeChange}
          onClearFilters={handleClearFilters}
        />

        {/* Chapters Grid */}
        <div className="container-custom py-12">
          {isLoading ? (
            <LoadingStates.ChapterGrid />
          ) : filteredChapters.length === 0 ? (
            <div className="text-center py-12">
              <MapPinIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="heading-3 text-xl text-gray-600 mb-2">No Chapters Found</h3>
              <p className="text-gray-500 mb-4">
                {searchQuery || selectedType !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'No chapters are currently available.'}
              </p>
              {(searchQuery || selectedType !== 'all') && (
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                >
                  Clear Filters
                </Button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredChapters.map((chapter) => (
                <ChapterCard
                  key={chapter.id}
                  chapter={chapter}
                  showLastActivity={true}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
}