// SEO utilities and configurations

export interface SEOConfig {
  title: string;
  description: string;
  keywords?: string[];
  canonical?: string;
  ogImage?: string;
  ogType?: 'website' | 'article' | 'profile';
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  noindex?: boolean;
  nofollow?: boolean;
  structuredData?: any;
}

export interface BreadcrumbItem {
  name: string;
  url: string;
}

// Default SEO configuration
export const DEFAULT_SEO: SEOConfig = {
  title: 'FGC Ikom Alumni Association',
  description: 'Official website of the Federal Government College Ikom Alumni Association. Connect with fellow alumni, explore chapters, sets, events, and galleries.',
  keywords: ['FGC', 'Ikom', 'Alumni', 'Federal Government College', 'Cross River', 'Nigeria', 'Education'],
  ogImage: '/images/og-image.jpg',
  ogType: 'website',
  twitterCard: 'summary_large_image'
};

// Site configuration
export const SITE_CONFIG = {
  name: 'FGC Ikom Alumni Association',
  url: 'https://www.fgcikomalumni.org.ng',
  description: 'Official website of the Federal Government College Ikom Alumni Association',
  logo: '/images/logo.png',
  social: {
    twitter: '@fgcikomalumni',
    facebook: 'https://facebook.com/fgcikomalumni',
    linkedin: 'https://linkedin.com/company/fgcikomalumni',
    instagram: '@fgcikomalumni'
  },
  contact: {
    email: '<EMAIL>',
    phone: '+234-************'
  }
};

// Generate page title with site name
export function generateTitle(pageTitle?: string): string {
  if (!pageTitle) return DEFAULT_SEO.title;
  return `${pageTitle} | ${SITE_CONFIG.name}`;
}

// Generate canonical URL
export function generateCanonicalUrl(path: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${SITE_CONFIG.url}${cleanPath}`;
}

// Generate Open Graph image URL
export function generateOGImageUrl(title?: string, description?: string): string {
  if (!title) return `${SITE_CONFIG.url}/images/og-image.jpg`;
  
  // In production, you'd use a service like Vercel OG or generate dynamic images
  const params = new URLSearchParams();
  params.set('title', title);
  if (description) params.set('description', description);
  
  return `${SITE_CONFIG.url}/api/og?${params.toString()}`;
}

// Generate structured data for organization
export function generateOrganizationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: SITE_CONFIG.name,
    url: SITE_CONFIG.url,
    logo: `${SITE_CONFIG.url}${SITE_CONFIG.logo}`,
    description: SITE_CONFIG.description,
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: SITE_CONFIG.contact.phone,
      contactType: 'customer service',
      email: SITE_CONFIG.contact.email
    },
    sameAs: [
      SITE_CONFIG.social.facebook,
      SITE_CONFIG.social.linkedin,
      `https://twitter.com/${SITE_CONFIG.social.twitter.replace('@', '')}`
    ]
  };
}

// Generate structured data for breadcrumbs
export function generateBreadcrumbStructuredData(breadcrumbs: BreadcrumbItem[]) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: `${SITE_CONFIG.url}${item.url}`
    }))
  };
}

// Generate structured data for events
export function generateEventStructuredData(event: {
  name: string;
  description: string;
  startDate: string;
  endDate?: string;
  location: string;
  organizer: string;
  image?: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Event',
    name: event.name,
    description: event.description,
    startDate: event.startDate,
    endDate: event.endDate,
    location: {
      '@type': 'Place',
      name: event.location
    },
    organizer: {
      '@type': 'Organization',
      name: event.organizer
    },
    image: event.image ? `${SITE_CONFIG.url}${event.image}` : undefined
  };
}

// Generate structured data for person (alumni profile)
export function generatePersonStructuredData(person: {
  name: string;
  jobTitle?: string;
  worksFor?: string;
  alumniOf: string;
  image?: string;
  email?: string;
}) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Person',
    name: person.name,
    jobTitle: person.jobTitle,
    worksFor: person.worksFor ? {
      '@type': 'Organization',
      name: person.worksFor
    } : undefined,
    alumniOf: {
      '@type': 'EducationalOrganization',
      name: person.alumniOf
    },
    image: person.image,
    email: person.email
  };
}

// Generate structured data for educational organization
export function generateEducationalOrganizationStructuredData() {
  return {
    '@context': 'https://schema.org',
    '@type': 'EducationalOrganization',
    name: 'Federal Government College Ikom',
    alternateName: 'FGC Ikom',
    url: SITE_CONFIG.url,
    logo: `${SITE_CONFIG.url}${SITE_CONFIG.logo}`,
    description: 'Federal Government College Ikom is a prestigious secondary school in Cross River State, Nigeria.',
    address: {
      '@type': 'PostalAddress',
      addressLocality: 'Ikom',
      addressRegion: 'Cross River State',
      addressCountry: 'Nigeria'
    },
    alumni: {
      '@type': 'Organization',
      name: SITE_CONFIG.name,
      url: SITE_CONFIG.url
    }
  };
}

// Generate robots meta content
export function generateRobotsContent(noindex?: boolean, nofollow?: boolean): string {
  const directives = [];
  
  if (noindex) {
    directives.push('noindex');
  } else {
    directives.push('index');
  }
  
  if (nofollow) {
    directives.push('nofollow');
  } else {
    directives.push('follow');
  }
  
  return directives.join(', ');
}

// Generate keywords string
export function generateKeywords(pageKeywords: string[] = []): string {
  const defaultKeywords = DEFAULT_SEO.keywords || [];
  const allKeywords = [...new Set([...defaultKeywords, ...pageKeywords])];
  return allKeywords.join(', ');
}

// SEO page configurations
export const PAGE_SEO_CONFIGS: Record<string, Partial<SEOConfig>> = {
  '/': {
    title: 'Home',
    description: 'Welcome to the FGC Ikom Alumni Association. Connect with fellow alumni, explore chapters, sets, events, and galleries.',
    keywords: ['home', 'welcome', 'alumni network', 'community']
  },
  '/chapters': {
    title: 'Alumni Chapters',
    description: 'Explore FGC Ikom alumni chapters worldwide. Find your local chapter and connect with fellow alumni in your area.',
    keywords: ['chapters', 'local', 'regional', 'diaspora', 'community']
  },
  '/sets': {
    title: 'Graduation Sets',
    description: 'Browse FGC Ikom graduation sets by year. Find your classmates and reconnect with your graduating class.',
    keywords: ['graduation', 'sets', 'classmates', 'year groups', 'reunion']
  },
  '/events': {
    title: 'Alumni Events',
    description: 'Discover upcoming FGC Ikom alumni events, reunions, and activities. Stay connected with your alumni community.',
    keywords: ['events', 'reunions', 'activities', 'networking', 'community']
  },
  '/gallery': {
    title: 'Photo Gallery',
    description: 'Browse photos from FGC Ikom alumni events, reunions, and activities. Relive memories and see what\'s happening.',
    keywords: ['photos', 'gallery', 'memories', 'events', 'reunions']
  },
  '/excobot': {
    title: 'Alumni Directory',
    description: 'Search the FGC Ikom alumni directory. Find and connect with fellow alumni by name, profession, location, or graduation year.',
    keywords: ['directory', 'search', 'alumni', 'networking', 'contact']
  },
  '/roll-of-honour': {
    title: 'Roll of Honour',
    description: 'Honor and remember departed FGC Ikom alumni. Share tributes and celebrate the lives of those who have passed.',
    keywords: ['memorial', 'tribute', 'honor', 'remembrance', 'departed']
  },
  '/chapter-excos': {
    title: 'Chapter Executives',
    description: 'Meet the dedicated executives leading FGC Ikom alumni chapters worldwide. Contact your chapter leadership.',
    keywords: ['executives', 'leadership', 'chapter', 'contact', 'administration']
  },
  '/contact': {
    title: 'Contact Us',
    description: 'Get in touch with the FGC Ikom Alumni Association. Contact us for membership, events, or general inquiries.',
    keywords: ['contact', 'support', 'membership', 'inquiries', 'help']
  }
};

// Get SEO config for a specific page
export function getPageSEOConfig(pathname: string): SEOConfig {
  const pageConfig = PAGE_SEO_CONFIGS[pathname] || {};
  
  return {
    ...DEFAULT_SEO,
    ...pageConfig,
    title: generateTitle(pageConfig.title),
    canonical: generateCanonicalUrl(pathname),
    keywords: pageConfig.keywords ? 
      [...(DEFAULT_SEO.keywords || []), ...pageConfig.keywords] : 
      DEFAULT_SEO.keywords
  };
}
