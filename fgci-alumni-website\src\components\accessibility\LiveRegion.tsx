'use client';

import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface LiveRegionProps {
  message?: string;
  priority?: 'polite' | 'assertive' | 'off';
  atomic?: boolean;
  relevant?: 'additions' | 'removals' | 'text' | 'all';
  className?: string;
  id?: string;
}

/**
 * Live Region component for announcing dynamic content changes to screen readers
 * Use this component to announce status updates, form validation, loading states, etc.
 */
export function LiveRegion({
  message = '',
  priority = 'polite',
  atomic = true,
  relevant = 'all',
  className,
  id = 'live-region'
}: LiveRegionProps) {
  const regionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (regionRef.current && message) {
      // Clear and set message to ensure it's announced
      regionRef.current.textContent = '';
      setTimeout(() => {
        if (regionRef.current) {
          regionRef.current.textContent = message;
        }
      }, 10);
    }
  }, [message]);

  return (
    <div
      ref={regionRef}
      id={id}
      aria-live={priority}
      aria-atomic={atomic}
      aria-relevant={relevant}
      className={cn('sr-only', className)}
    >
      {message}
    </div>
  );
}

/**
 * Status announcement component for form validation and user feedback
 */
interface StatusAnnouncementProps {
  status: 'success' | 'error' | 'warning' | 'info' | 'loading' | null;
  message?: string;
  className?: string;
}

export function StatusAnnouncement({ 
  status, 
  message, 
  className 
}: StatusAnnouncementProps) {
  const getStatusMessage = () => {
    if (message) return message;
    
    switch (status) {
      case 'success':
        return 'Operation completed successfully';
      case 'error':
        return 'An error occurred';
      case 'warning':
        return 'Warning: Please review your input';
      case 'info':
        return 'Information updated';
      case 'loading':
        return 'Loading, please wait';
      default:
        return '';
    }
  };

  const getPriority = (): 'polite' | 'assertive' => {
    return status === 'error' ? 'assertive' : 'polite';
  };

  if (!status) return null;

  return (
    <LiveRegion
      message={getStatusMessage()}
      priority={getPriority()}
      className={className}
      id={`status-${status}`}
    />
  );
}

/**
 * Loading announcement component
 */
interface LoadingAnnouncementProps {
  isLoading: boolean;
  message?: string;
  className?: string;
}

export function LoadingAnnouncement({ 
  isLoading, 
  message = 'Loading content, please wait', 
  className 
}: LoadingAnnouncementProps) {
  return (
    <LiveRegion
      message={isLoading ? message : ''}
      priority="polite"
      className={className}
      id="loading-announcement"
    />
  );
}

/**
 * Navigation announcement component
 */
interface NavigationAnnouncementProps {
  currentPage?: string;
  totalPages?: number;
  className?: string;
}

export function NavigationAnnouncement({ 
  currentPage, 
  totalPages, 
  className 
}: NavigationAnnouncementProps) {
  const getMessage = () => {
    if (!currentPage) return '';
    
    if (totalPages) {
      return `Navigated to ${currentPage}, page ${totalPages}`;
    }
    
    return `Navigated to ${currentPage}`;
  };

  return (
    <LiveRegion
      message={getMessage()}
      priority="polite"
      className={className}
      id="navigation-announcement"
    />
  );
}