'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon, Squares2X2Icon, ListBulletIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';

interface ChapterExcosSearchProps {
  query: string;
  onSearch: (query: string) => void;
  viewMode: 'grid' | 'list';
  onViewModeChange: (mode: 'grid' | 'list') => void;
  resultsCount: number;
}

export function ChapterExcosSearch({
  query,
  onSearch,
  viewMode,
  onViewModeChange,
  resultsCount
}: ChapterExcosSearchProps) {
  const [localQuery, setLocalQuery] = useState(query);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(localQuery);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalQuery(value);
    // Real-time search
    onSearch(value);
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        {/* Search Form */}
        <form onSubmit={handleSubmit} className="flex-1 max-w-2xl">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              value={localQuery}
              onChange={handleInputChange}
              placeholder="Search by name, position, chapter, or region..."
              className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-maroon-500 focus:border-maroon-500 transition-colors"
              aria-label="Search chapter executives"
            />
          </div>
        </form>

        {/* View Controls and Results Count */}
        <div className="flex items-center gap-4">
          <div className="text-sm text-gray-600">
            {resultsCount} {resultsCount === 1 ? 'executive' : 'executives'} found
          </div>
          
          <div className="flex items-center bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => onViewModeChange('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid'
                  ? 'bg-white text-maroon-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              aria-label="Grid view"
            >
              <Squares2X2Icon className="h-4 w-4" />
            </button>
            <button
              onClick={() => onViewModeChange('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list'
                  ? 'bg-white text-maroon-600 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              aria-label="List view"
            >
              <ListBulletIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Search Tips */}
      {!query && (
        <div className="mt-4 text-sm text-gray-500">
          <p className="mb-2">Search tips:</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>Search by executive name, position (President, Secretary, etc.)</li>
            <li>Filter by chapter name or region</li>
            <li>Use filters below to narrow down results</li>
          </ul>
        </div>
      )}
    </div>
  );
}