'use client';

import { useState, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiClient } from '@/lib/api-client';
import { Chapter, Person, SearchFilters } from '@/lib/types';

interface ChapterExco extends Person {
  chapterName: string;
  chapterSlug: string;
  chapterRegion?: string;
  chapterType: 'regional' | 'diaspora' | 'school-based';
  termStartDate?: string;
  termEndDate?: string;
}

interface UseChapterExcosOptions extends SearchFilters {
  query?: string;
}

export function useChapterExcos(options: UseChapterExcosOptions = {}) {
  const [searchQuery, setSearchQuery] = useState(options.query || '');
  const [filters, setFilters] = useState<SearchFilters>(options);

  // Fetch all chapters to get executive data
  const {
    data: chaptersData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['chapters'],
    queryFn: () => apiClient.get<Chapter[]>('/api/chapters'),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Transform chapters data into flat executive list
  const allExecutives = useMemo(() => {
    if (!chaptersData?.data) return [];

    const executives: ChapterExco[] = [];
    
    chaptersData.data.forEach((chapter) => {
      chapter.exco.forEach((person, index) => {
        // Generate realistic term dates based on position and chapter
        const currentYear = new Date().getFullYear();
        const isCurrentTerm = Math.random() > 0.3; // 70% chance of current term
        
        let termStartDate: string;
        let termEndDate: string | undefined;
        
        if (isCurrentTerm) {
          // Current term (started this year or last year)
          const startYear = Math.random() > 0.5 ? currentYear : currentYear - 1;
          termStartDate = `${startYear}-01-01`;
          termEndDate = `${currentYear + 1}-12-31`;
        } else {
          // Previous term
          const startYear = currentYear - Math.floor(Math.random() * 3) - 1; // 1-4 years ago
          termStartDate = `${startYear}-01-01`;
          termEndDate = `${startYear + 1}-12-31`;
        }
        
        executives.push({
          ...person,
          chapterName: chapter.name,
          chapterSlug: chapter.slug,
          chapterRegion: chapter.region,
          chapterType: chapter.type,
          termStartDate,
          termEndDate,
        });
      });
    });

    return executives;
  }, [chaptersData]);

  // Filter and search executives
  const filteredExecutives = useMemo(() => {
    let result = allExecutives;

    // Apply text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      result = result.filter((exec) => {
        return (
          exec.name.toLowerCase().includes(query) ||
          exec.role?.toLowerCase().includes(query) ||
          exec.chapterName.toLowerCase().includes(query) ||
          exec.chapterRegion?.toLowerCase().includes(query) ||
          exec.currentLocation?.toLowerCase().includes(query) ||
          exec.email?.toLowerCase().includes(query)
        );
      });
    }

    // Apply filters
    if (filters.chapter) {
      result = result.filter((exec) => exec.chapterSlug === filters.chapter);
    }

    if (filters.position) {
      result = result.filter((exec) => exec.role === filters.position);
    }

    if (filters.type) {
      result = result.filter((exec) => {
        const role = exec.role || '';
        switch (filters.type) {
          case 'executive':
            return ['President', 'Vice President'].some(pos => role.includes(pos));
          case 'officer':
            return ['Public Relations Officer', 'Welfare Officer'].some(pos => role.includes(pos));
          case 'secretary':
            return ['Secretary', 'Social Secretary'].some(pos => role.includes(pos));
          case 'financial':
            return ['Treasurer', 'Financial Secretary'].some(pos => role.includes(pos));
          default:
            return true;
        }
      });
    }

    if (filters.region) {
      result = result.filter((exec) => exec.chapterRegion === filters.region);
    }

    if (filters.termStatus) {
      const currentYear = new Date().getFullYear();
      result = result.filter((exec) => {
        if (!exec.termStartDate) return true;
        
        const startYear = new Date(exec.termStartDate).getFullYear();
        const endYear = exec.termEndDate ? new Date(exec.termEndDate).getFullYear() : null;
        
        switch (filters.termStatus) {
          case 'current':
            return endYear ? (currentYear >= startYear && currentYear <= endYear) : (currentYear >= startYear);
          case 'past':
            return endYear ? currentYear > endYear : false;
          case 'upcoming':
            return currentYear < startYear;
          default:
            return true;
        }
      });
    }

    if (filters.graduationYear) {
      result = result.filter((exec) => exec.graduationYear === filters.graduationYear);
    }

    if (filters.location) {
      result = result.filter((exec) => 
        exec.currentLocation?.toLowerCase().includes(filters.location!.toLowerCase())
      );
    }

    // Sort by chapter name, then by role hierarchy
    const roleHierarchy: Record<string, number> = {
      'President': 1,
      'Vice President': 2,
      'Secretary': 3,
      'Treasurer': 4,
      'Financial Secretary': 5,
      'Public Relations Officer': 6,
      'Social Secretary': 7,
      'Welfare Officer': 8,
    };

    result.sort((a, b) => {
      // First sort by chapter name
      const chapterCompare = a.chapterName.localeCompare(b.chapterName);
      if (chapterCompare !== 0) return chapterCompare;

      // Then sort by role hierarchy
      const aRoleOrder = roleHierarchy[a.role || ''] || 999;
      const bRoleOrder = roleHierarchy[b.role || ''] || 999;
      if (aRoleOrder !== bRoleOrder) return aRoleOrder - bRoleOrder;

      // Finally sort by name
      return a.name.localeCompare(b.name);
    });

    return result;
  }, [allExecutives, searchQuery, filters]);

  // Update search query
  const updateSearchQuery = (query: string) => {
    setSearchQuery(query);
  };

  // Update filters
  const updateFilters = (newFilters: SearchFilters) => {
    setFilters(newFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery('');
    setFilters({});
  };

  // Get unique values for filter options
  const filterOptions = useMemo(() => {
    const chapters = new Set<string>();
    const positions = new Set<string>();
    const regions = new Set<string>();
    const locations = new Set<string>();

    allExecutives.forEach((exec) => {
      chapters.add(exec.chapterSlug);
      if (exec.role) positions.add(exec.role);
      if (exec.chapterRegion) regions.add(exec.chapterRegion);
      if (exec.currentLocation) locations.add(exec.currentLocation);
    });

    return {
      chapters: Array.from(chapters).sort(),
      positions: Array.from(positions).sort(),
      regions: Array.from(regions).sort(),
      locations: Array.from(locations).sort(),
    };
  }, [allExecutives]);

  // Statistics
  const statistics = useMemo(() => {
    const totalExecutives = allExecutives.length;
    const totalChapters = new Set(allExecutives.map(exec => exec.chapterSlug)).size;
    const totalRegions = new Set(allExecutives.map(exec => exec.chapterRegion)).size;
    
    const chapterTypes = allExecutives.reduce((acc, exec) => {
      acc[exec.chapterType] = (acc[exec.chapterType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalExecutives,
      totalChapters,
      totalRegions,
      chapterTypes,
    };
  }, [allExecutives]);

  return {
    data: filteredExecutives,
    loading: isLoading,
    error: error?.message || null,
    refetch,
    searchQuery,
    filters,
    updateSearchQuery,
    updateFilters,
    clearFilters,
    filterOptions,
    statistics,
  };
}