'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useReducedMotion } from '@/hooks/useReducedMotion';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    loading = false,
    icon,
    children,
    disabled,
    onClick,
    onKeyDown,
    ...props
  }, ref) => {
    const prefersReducedMotion = useReducedMotion();
    
    const baseClasses = 'btn-base focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2 transition-shadow';

    const variantClasses = {
      primary: 'bg-maroon-700 text-white shadow-sm hover:bg-maroon-800 hover:shadow-md active:bg-maroon-900 disabled:bg-gray-300',
      secondary: 'bg-gold-500 text-maroon-900 shadow-sm hover:bg-gold-600 hover:shadow-md active:bg-gold-700 disabled:bg-gray-300',
      outline: 'border-2 border-maroon-700 text-maroon-700 bg-transparent hover:bg-maroon-700 hover:text-white active:bg-maroon-800 disabled:border-gray-300 disabled:text-gray-300',
      ghost: 'text-maroon-700 bg-transparent hover:bg-maroon-50 active:bg-maroon-100 disabled:text-gray-300'
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm gap-1.5',
      md: 'px-4 py-2 text-base gap-2',
      lg: 'px-6 py-3 text-lg gap-2.5'
    };

    const isDisabled = disabled || loading;

    // Animation variants
    const buttonVariants = {
      hover: prefersReducedMotion ? {} : {
        scale: 1.02,
        boxShadow: variant === 'primary' || variant === 'secondary' 
          ? '0 8px 25px -5px rgba(212, 160, 23, 0.3)' 
          : '0 4px 12px -2px rgba(0, 0, 0, 0.1)',
        transition: {
          type: 'spring',
          stiffness: 400,
          damping: 17,
        },
      },
      tap: prefersReducedMotion ? {} : {
        scale: 0.98,
        transition: {
          type: 'spring',
          stiffness: 400,
          damping: 17,
        },
      },
    };

    const MotionButton = motion.button;

    return (
      <MotionButton
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          isDisabled && 'cursor-not-allowed opacity-50',
          className
        )}
        disabled={isDisabled}
        ref={ref}
        aria-disabled={isDisabled}
        variants={buttonVariants}
        whileHover={!isDisabled ? 'hover' : undefined}
        whileTap={!isDisabled ? 'tap' : undefined}
        onClick={onClick}
        onKeyDown={(e) => {
          onKeyDown?.(e);
          if (isDisabled) return;
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick?.(e as any);
          }
        }}
        {...props}
      >
        {loading && (
          <svg 
            className="animate-spin h-4 w-4" 
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <circle 
              className="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              strokeWidth="4"
            />
            <path 
              className="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {!loading && icon && (
          <span className="flex-shrink-0" aria-hidden="true">
            {icon}
          </span>
        )}
        <span>{children}</span>
      </MotionButton>
    );
  }
);

Button.displayName = 'Button';

export { Button };