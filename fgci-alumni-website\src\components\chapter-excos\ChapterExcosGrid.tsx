'use client';

import { motion } from 'framer-motion';
import { ChapterExcoCard } from './ChapterExcoCard';
import { ChapterExcoListItem } from './ChapterExcoListItem';
import { ChapterExcoGroup } from './ChapterExcoGroup';
import { Person, Chapter } from '@/lib/types';

interface ChapterExco extends Person {
  chapterName: string;
  chapterSlug: string;
  chapterRegion?: string;
  chapterType: 'regional' | 'diaspora' | 'school-based';
  termStartDate?: string;
  termEndDate?: string;
}

interface ChapterExcosGridProps {
  chapterExcos: ChapterExco[];
  viewMode: 'grid' | 'list';
  searchQuery: string;
}

export function ChapterExcosGrid({
  chapterExcos,
  viewMode,
  searchQuery
}: ChapterExcosGridProps) {
  if (chapterExcos.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="max-w-md mx-auto">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No executives found</h3>
          <p className="text-gray-500">
            {searchQuery 
              ? `No executives match your search for "${searchQuery}". Try adjusting your search terms or filters.`
              : 'No executives available at the moment.'
            }
          </p>
        </div>
      </div>
    );
  }

  // Group executives by chapter for better organization
  const groupedExcos = chapterExcos.reduce((groups, exco) => {
    const key = exco.chapterSlug;
    if (!groups[key]) {
      groups[key] = {
        chapterName: exco.chapterName,
        chapterSlug: exco.chapterSlug,
        chapterRegion: exco.chapterRegion,
        chapterType: exco.chapterType,
        executives: []
      };
    }
    groups[key].executives.push(exco);
    return groups;
  }, {} as Record<string, {
    chapterName: string;
    chapterSlug: string;
    chapterRegion?: string;
    chapterType: 'regional' | 'diaspora' | 'school-based';
    executives: ChapterExco[];
  }>);

  const chapterGroups = Object.values(groupedExcos);

  if (viewMode === 'list') {
    return (
      <div className="space-y-8">
        {chapterGroups.map((group, groupIndex) => (
          <motion.div
            key={group.chapterSlug}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: groupIndex * 0.1 }}
          >
            <ChapterExcoGroup
              chapterName={group.chapterName}
              chapterRegion={group.chapterRegion}
              chapterType={group.chapterType}
              executiveCount={group.executives.length}
            />
            
            <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              {group.executives.map((exco, index) => (
                <ChapterExcoListItem
                  key={exco.id}
                  executive={exco}
                  isLast={index === group.executives.length - 1}
                />
              ))}
            </div>
          </motion.div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {chapterGroups.map((group, groupIndex) => (
        <motion.div
          key={group.chapterSlug}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, delay: groupIndex * 0.1 }}
        >
          <ChapterExcoGroup
            chapterName={group.chapterName}
            chapterRegion={group.chapterRegion}
            chapterType={group.chapterType}
            executiveCount={group.executives.length}
          />
          
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {group.executives.map((exco, index) => (
              <motion.div
                key={exco.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: (groupIndex * 0.1) + (index * 0.05) }}
              >
                <ChapterExcoCard executive={exco} />
              </motion.div>
            ))}
          </div>
        </motion.div>
      ))}
    </div>
  );
}