import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Button } from './Button';
import { HomeIcon, ArrowRightIcon, PlusIcon } from '@heroicons/react/24/outline';

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A versatile button component with multiple variants, sizes, and states. Supports icons and loading states.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline', 'ghost', 'danger'],
      description: 'Visual style variant of the button',
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
      description: 'Size of the button',
    },
    disabled: {
      control: 'boolean',
      description: 'Whether the button is disabled',
    },
    loading: {
      control: 'boolean',
      description: 'Whether the button is in loading state',
    },
    children: {
      control: 'text',
      description: 'Button content',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic variants
export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Outline Button',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Ghost Button',
  },
};

export const Danger: Story = {
  args: {
    variant: 'danger',
    children: 'Danger Button',
  },
};

// Sizes
export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Small Button',
  },
};

export const Medium: Story = {
  args: {
    size: 'md',
    children: 'Medium Button',
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    children: 'Large Button',
  },
};

// With icons
export const WithIcon: Story = {
  args: {
    icon: <HomeIcon className="w-4 h-4" />,
    children: 'Home',
  },
};

export const WithTrailingIcon: Story = {
  args: {
    children: 'Continue',
    icon: <ArrowRightIcon className="w-4 h-4" />,
    iconPosition: 'right',
  },
};

export const IconOnly: Story = {
  args: {
    icon: <PlusIcon className="w-4 h-4" />,
    'aria-label': 'Add item',
  },
};

// States
export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Disabled Button',
  },
};

export const Loading: Story = {
  args: {
    loading: true,
    children: 'Loading...',
  },
};

// Interactive examples
export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button variant="primary">Primary</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="outline">Outline</Button>
      <Button variant="ghost">Ghost</Button>
      <Button variant="danger">Danger</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All button variants displayed together for comparison.',
      },
    },
  },
};

export const AllSizes: Story = {
  render: () => (
    <div className="flex items-center gap-4">
      <Button size="sm">Small</Button>
      <Button size="md">Medium</Button>
      <Button size="lg">Large</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'All button sizes displayed together for comparison.',
      },
    },
  },
};

export const WithDifferentIcons: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <Button icon={<HomeIcon className="w-4 h-4" />}>Home</Button>
      <Button icon={<PlusIcon className="w-4 h-4" />}>Add</Button>
      <Button icon={<ArrowRightIcon className="w-4 h-4" />} iconPosition="right">
        Continue
      </Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Buttons with different icons and positions.',
      },
    },
  },
};

// Accessibility story
export const AccessibilityExample: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Keyboard Navigation</h3>
        <p className="text-sm text-gray-600 mb-4">
          Use Tab to navigate between buttons, Enter or Space to activate.
        </p>
        <div className="flex gap-2">
          <Button>First</Button>
          <Button>Second</Button>
          <Button>Third</Button>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-2">Screen Reader Support</h3>
        <p className="text-sm text-gray-600 mb-4">
          Buttons with proper ARIA labels and descriptions.
        </p>
        <div className="flex gap-2">
          <Button aria-label="Close dialog">×</Button>
          <Button aria-describedby="help-text">Help</Button>
          <Button disabled aria-label="Save (disabled because form is invalid)">
            Save
          </Button>
        </div>
        <p id="help-text" className="text-xs text-gray-500 mt-2">
          This button opens the help documentation
        </p>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Examples of accessible button implementations with proper ARIA attributes.',
      },
    },
  },
};
