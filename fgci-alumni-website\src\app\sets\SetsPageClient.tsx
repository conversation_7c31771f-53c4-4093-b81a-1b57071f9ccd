'use client';

import { useState, useMemo } from 'react';
import { SetCard } from '../../components/sets/SetCard';
import { SetFilters } from '../../components/sets/SetFilters';
import { useSets } from '../../hooks/useSets';
import { LoadingGrid, ErrorState } from '../../components/ui/LoadingStates';
import { Button } from '../../components/ui/Button';
import { Breadcrumbs, StructuredData } from '@/components/seo';
import { AcademicCapIcon, UsersIcon, CalendarDaysIcon } from '@heroicons/react/24/outline';

export default function SetsPageClient() {
  const [search, setSearch] = useState('');
  const [selectedYear, setSelectedYear] = useState<number | undefined>();
  const [page, setPage] = useState(1);

  const { data, isLoading, error, refetch } = useSets({
    page,
    limit: 12,
    search: search || undefined,
    year: selectedYear,
  });

  // Extract available years from all sets for filter options
  const availableYears = useMemo(() => {
    if (!data?.data) return [];
    
    const years = data.data.map(set => set.year);
    return [...new Set(years)].sort((a, b) => b - a); // Sort descending
  }, [data?.data]);

  const handleSearchChange = (newSearch: string) => {
    setSearch(newSearch);
    setPage(1); // Reset to first page when searching
  };

  const handleYearChange = (year: number | undefined) => {
    setSelectedYear(year);
    setPage(1); // Reset to first page when filtering
  };

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
  };

  // Structured data for the sets page
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: 'FGC Ikom Graduation Sets',
    description: 'Browse FGC Ikom graduation sets by year. Find your classmates and reconnect with your graduating class.',
    url: 'https://www.fgcikomalumni.org.ng/sets',
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: data?.meta?.total || 0,
      itemListElement: data?.data?.map((set, index) => ({
        '@type': 'Organization',
        position: index + 1,
        name: set.name,
        description: set.motto,
        url: `https://www.fgcikomalumni.org.ng/sets/${set.slug}`,
        foundingDate: set.year.toString(),
        numberOfMembers: set.memberCount
      })) || []
    }
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <Breadcrumbs />
          <ErrorState
            title="Failed to load sets"
            message="We couldn't load the graduation sets. Please try again."
            onRetry={() => refetch()}
          />
        </div>
      </div>
    );
  }

  return (
    <>
      <StructuredData data={structuredData} id="sets-page" />
      
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-maroon-700 to-maroon-800 text-white">
          <div className="container mx-auto px-4 py-16">
            <Breadcrumbs className="mb-6 text-white" />
            <div className="max-w-4xl mx-auto text-center">
              <div className="flex justify-center mb-6">
                <div className="bg-white/10 p-4 rounded-full">
                  <AcademicCapIcon className="w-12 h-12" />
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Graduation Sets
              </h1>
              <p className="text-xl text-maroon-100 mb-8 max-w-2xl mx-auto">
                Connect with your graduating class and explore the achievements of FGC Ikom alumni 
                across different years. Each set represents a unique cohort with their own motto, 
                leadership, and legacy.
              </p>
              
              {/* Stats */}
              {data?.meta && (
                <div className="flex justify-center space-x-8 text-center">
                  <div className="bg-white/10 rounded-lg p-4">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <UsersIcon className="w-5 h-5" />
                      <span className="text-2xl font-bold">{data.meta.total}</span>
                    </div>
                    <p className="text-sm text-maroon-100">Graduation Sets</p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-4">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <CalendarDaysIcon className="w-5 h-5" />
                      <span className="text-2xl font-bold">{availableYears.length}</span>
                    </div>
                    <p className="text-sm text-maroon-100">Years Represented</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-7xl mx-auto">
            {/* Filters */}
            <div className="mb-8">
              <SetFilters
                onSearchChange={handleSearchChange}
                onYearChange={handleYearChange}
                searchValue={search}
                yearValue={selectedYear}
                availableYears={availableYears}
              />
            </div>

            {/* Results Summary */}
            {data?.meta && (
              <div className="mb-6">
                <p className="text-gray-600">
                  {search || selectedYear ? (
                    <>
                      Showing {data.data.length} of {data.meta.total} sets
                      {search && ` matching "${search}"`}
                      {selectedYear && ` from ${selectedYear}`}
                    </>
                  ) : (
                    `Showing ${data.data.length} of ${data.meta.total} graduation sets`
                  )}
                </p>
              </div>
            )}

            {/* Sets Grid */}
            {isLoading && page === 1 ? (
              <LoadingGrid count={6} />
            ) : (
              <>
                {data?.data && data.data.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                    {data.data.map((set) => (
                      <SetCard
                        key={set.id}
                        set={set}
                        showMemberCount={true}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <AcademicCapIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No sets found
                    </h3>
                    <p className="text-gray-500 mb-4">
                      {search || selectedYear
                        ? "Try adjusting your search criteria or filters."
                        : "No graduation sets are available at the moment."}
                    </p>
                    {(search || selectedYear) && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSearch('');
                          setSelectedYear(undefined);
                          setPage(1);
                        }}
                      >
                        Clear Filters
                      </Button>
                    )}
                  </div>
                )}

                {/* Load More Button */}
                {data?.meta?.hasNext && (
                  <div className="text-center">
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={handleLoadMore}
                      disabled={isLoading}
                      loading={isLoading && page > 1}
                    >
                      {isLoading && page > 1 ? 'Loading...' : 'Load More Sets'}
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
}