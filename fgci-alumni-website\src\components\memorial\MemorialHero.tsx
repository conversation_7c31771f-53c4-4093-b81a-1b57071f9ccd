'use client';

import { motion } from 'framer-motion';

export function MemorialHero() {
  return (
    <section className="relative bg-gradient-to-b from-gray-800 to-gray-900 text-white py-20">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-[url('/images/patterns/memorial-pattern.svg')] bg-repeat opacity-20"></div>
      </div>
      
      <div className="relative container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {/* Memorial Symbol */}
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-700 rounded-full mb-4">
                <svg 
                  className="w-8 h-8 text-gray-300" 
                  fill="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" />
                </svg>
              </div>
            </div>
            
            <h1 className="text-4xl md:text-5xl font-serif font-bold mb-6 text-gray-100">
              Roll of Honour
            </h1>
            
            <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
              In loving memory of our departed alumni who made lasting contributions 
              to their communities and left indelible marks on the world.
            </p>
            
            <div className="text-lg text-gray-400 italic">
              "Their legacy lives on in the lives they touched and the communities they served."
            </div>
          </motion.div>
        </div>
      </div>
      
      {/* Subtle bottom border */}
      <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-600 to-transparent"></div>
    </section>
  );
}