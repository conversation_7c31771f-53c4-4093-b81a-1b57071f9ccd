import { MetadataRoute } from 'next';
import { SITE_CONFIG } from '@/lib/seo';

export default function robots(): MetadataRoute.Robots {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: isProduction ? '/' : [],
        disallow: isProduction ? [
          '/api/',
          '/admin/',
          '/_next/',
          '/private/',
          '/testing-dashboard/',
          '/accessibility-test/',
          '/performance-test/',
          '/animations-test/',
          '/seo-test/',
          '/documentation/',
          '/*.json$',
          '/search?*',
          '/filter?*'
        ] : ['/'],
      },
      {
        userAgent: 'Googlebot',
        allow: isProduction ? '/' : [],
        disallow: isProduction ? [
          '/api/',
          '/admin/',
          '/private/',
          '/testing-dashboard/',
          '/accessibility-test/',
          '/performance-test/',
          '/animations-test/',
          '/seo-test/',
          '/documentation/',
          '/*.json$'
        ] : ['/'],
        crawlDelay: 1,
      },
      {
        userAgent: 'Bingbot',
        allow: isProduction ? '/' : [],
        disallow: isProduction ? [
          '/api/',
          '/admin/',
          '/private/',
          '/testing-dashboard/',
          '/*.json$'
        ] : ['/'],
        crawlDelay: 2,
      },
      // Block aggressive crawlers in production
      ...(isProduction ? [
        {
          userAgent: 'AhrefsBot',
          disallow: ['/'],
        },
        {
          userAgent: 'SemrushBot',
          disallow: ['/'],
        },
        {
          userAgent: 'MJ12bot',
          disallow: ['/'],
        }
      ] : [])
    ],
    sitemap: `${SITE_CONFIG.url}/sitemap.xml`,
    host: SITE_CONFIG.url,
  };
}
