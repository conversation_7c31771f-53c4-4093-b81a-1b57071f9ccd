import React from 'react';
import { render, screen } from '@testing-library/react';
import { FormValidationSummary } from '../FormValidationSummary';
import { FormErrors } from '@/lib/types';

describe('FormValidationSummary', () => {
  it('renders nothing when no errors and showSuccessMessage is false', () => {
    const { container } = render(
      <FormValidationSummary 
        errors={{}} 
        isValid={true} 
        showSuccessMessage={false} 
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  it('shows success message when valid and showSuccessMessage is true', () => {
    render(
      <FormValidationSummary 
        errors={{}} 
        isValid={true} 
        showSuccessMessage={true}
        successMessage="Form is valid!"
      />
    );
    
    expect(screen.getByText('Form is valid!')).toBeInTheDocument();
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('displays error summary with correct count', () => {
    const errors: FormErrors = {
      name: ['Name is required'],
      email: ['Email is required', 'Invalid email format'],
      phone: ['Invalid phone number'],
    };

    render(
      <FormValidationSummary 
        errors={errors} 
        isValid={false}
        title="Please fix the following errors:"
      />
    );
    
    expect(screen.getByText(/Please fix the following errors: \(4 errors\)/)).toBeInTheDocument();
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  it('displays all field errors correctly', () => {
    const errors: FormErrors = {
      name: ['Name is required'],
      email: ['Email is required', 'Invalid email format'],
    };

    render(
      <FormValidationSummary 
        errors={errors} 
        isValid={false}
      />
    );
    
    expect(screen.getByText('Name is required')).toBeInTheDocument();
    expect(screen.getByText('Email is required')).toBeInTheDocument();
    expect(screen.getByText('Invalid email format')).toBeInTheDocument();
  });

  it('formats field names correctly', () => {
    const errors: FormErrors = {
      graduationYear: ['Invalid year'],
      inquiryType: ['Required field'],
    };

    render(
      <FormValidationSummary 
        errors={errors} 
        isValid={false}
      />
    );
    
    expect(screen.getByText(/graduation year:/i)).toBeInTheDocument();
    expect(screen.getByText(/inquiry type:/i)).toBeInTheDocument();
  });

  it('handles single error correctly', () => {
    const errors: FormErrors = {
      name: ['Name is required'],
    };

    render(
      <FormValidationSummary 
        errors={errors} 
        isValid={false}
      />
    );
    
    expect(screen.getByText(/\(1 error\)/)).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const errors: FormErrors = {
      name: ['Name is required'],
    };

    render(
      <FormValidationSummary 
        errors={errors} 
        isValid={false}
        className="custom-class"
      />
    );
    
    const container = screen.getByRole('alert');
    expect(container).toHaveClass('custom-class');
  });

  it('uses custom title', () => {
    const errors: FormErrors = {
      name: ['Name is required'],
    };

    render(
      <FormValidationSummary 
        errors={errors} 
        isValid={false}
        title="Custom error title:"
      />
    );
    
    expect(screen.getByText(/Custom error title: \(1 error\)/)).toBeInTheDocument();
  });
});