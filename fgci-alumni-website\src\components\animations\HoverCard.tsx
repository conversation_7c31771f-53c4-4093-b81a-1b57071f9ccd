'use client';

import React, { useRef, useEffect } from 'react';
import { addHoverEffect, prefersReducedMotion } from '@/lib/animations';
import { cn } from '@/lib/utils';

interface HoverCardProps {
  children: React.ReactNode;
  className?: string;
  hoverEffect?: 'lift' | 'scale' | 'glow' | 'tilt';
  intensity?: 'subtle' | 'medium' | 'strong';
  disabled?: boolean;
}

export function HoverCard({
  children,
  className,
  hoverEffect = 'lift',
  intensity = 'medium',
  disabled = false
}: HoverCardProps) {
  const cardRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const card = cardRef.current;
    if (!card || disabled || prefersReducedMotion()) return;

    const getHoverKeyframes = () => {
      const intensityMap = {
        subtle: 0.5,
        medium: 1,
        strong: 1.5
      };
      
      const factor = intensityMap[intensity];

      switch (hoverEffect) {
        case 'lift':
          return [
            { transform: 'translateY(0) scale(1)', boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)' },
            { transform: `translateY(-${4 * factor}px) scale(1)`, boxShadow: `0 ${8 * factor}px ${24 * factor}px rgba(0, 0, 0, 0.15)` }
          ];
        
        case 'scale':
          return [
            { transform: 'scale(1)' },
            { transform: `scale(${1 + 0.05 * factor})` }
          ];
        
        case 'glow':
          return [
            { boxShadow: '0 0 0 rgba(139, 69, 19, 0)' },
            { boxShadow: `0 0 ${20 * factor}px rgba(139, 69, 19, ${0.3 * factor})` }
          ];
        
        case 'tilt':
          return [
            { transform: 'perspective(1000px) rotateX(0deg) rotateY(0deg)' },
            { transform: `perspective(1000px) rotateX(${2 * factor}deg) rotateY(${2 * factor}deg)` }
          ];
        
        default:
          return [
            { transform: 'translateY(0)' },
            { transform: `translateY(-${4 * factor}px)` }
          ];
      }
    };

    addHoverEffect(card, getHoverKeyframes(), {
      duration: 200,
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    });
  }, [hoverEffect, intensity, disabled]);

  return (
    <div
      ref={cardRef}
      className={cn(
        'transition-all duration-200 ease-out cursor-pointer',
        !disabled && 'hover:cursor-pointer',
        className
      )}
      style={{
        transformStyle: 'preserve-3d'
      }}
    >
      {children}
    </div>
  );
}

export default HoverCard;
