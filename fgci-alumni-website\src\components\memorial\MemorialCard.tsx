'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { Memorial } from '@/lib/types';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { MemorialModal } from './MemorialModal';

interface MemorialCardProps {
  memorial: Memorial;
  index: number;
}

export function MemorialCard({ memorial, index }: MemorialCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { person, dateOfPassing, tribute } = memorial;

  // Calculate years since passing
  const yearsPassed = new Date().getFullYear() - new Date(dateOfPassing).getFullYear();
  const passingDate = new Date(dateOfPassing).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: index * 0.1 }}
        className="h-full"
      >
        <Card
          variant="elevated"
          padding="lg"
          className="h-full bg-white border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg group cursor-pointer"
          onClick={() => setIsModalOpen(true)}
        >
          <div className="flex flex-col h-full">
            {/* Photo and Basic Info */}
            <div className="flex items-start space-x-4 mb-4">
              <div className="relative flex-shrink-0">
                <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-200 border-2 border-gray-300">
                  {person.photo ? (
                    <Image
                      src={person.photo}
                      alt={`${person.name} memorial photo`}
                      width={80}
                      height={80}
                      className="w-full h-full object-cover grayscale group-hover:grayscale-0 transition-all duration-500"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                      </svg>
                    </div>
                  )}
                </div>
                
                {/* Memorial symbol overlay */}
                <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center border-2 border-white">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" />
                  </svg>
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-gray-900 mb-1 group-hover:text-maroon-700 transition-colors">
                  {person.name}
                </h3>
                <p className="text-sm text-gray-600 mb-1">
                  Class of {person.graduationYear}
                </p>
                {person.role && (
                  <p className="text-sm text-gray-500 italic">
                    {person.role}
                  </p>
                )}
              </div>
            </div>
            
            {/* Chapter and Date */}
            <div className="mb-4 space-y-2">
              {person.chapter && (
                <div className="flex items-center text-sm text-gray-600">
                  <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  {person.chapter}
                </div>
              )}
              
              <div className="flex items-center text-sm text-gray-500">
                <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                {passingDate}
              </div>
            </div>
            
            {/* Tribute Preview */}
            <div className="flex-1 mb-4">
              <p className="text-sm text-gray-700 leading-relaxed line-clamp-3">
                {tribute}
              </p>
            </div>
            
            {/* View Details Button */}
            <div className="pt-4 border-t border-gray-100">
              <Button
                variant="outline"
                size="sm"
                className="w-full text-gray-600 border-gray-300 hover:border-gray-400 hover:text-gray-700"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsModalOpen(true);
                }}
              >
                View Full Tribute
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>
      
      {/* Memorial Detail Modal */}
      <MemorialModal
        memorial={memorial}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}