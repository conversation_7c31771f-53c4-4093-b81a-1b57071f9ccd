import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { AlumniCard } from '../AlumniCard';
import { Person } from '@/lib/types';

const mockPerson: Person = {
  id: 'test-1',
  name: '<PERSON>',
  role: 'President - Lagos Chapter',
  phone: '+234-************',
  email: '<EMAIL>',
  photo: '/images/people/john-doe.jpg',
  graduationYear: 2015,
  chapter: 'Lagos Chapter',
  currentLocation: 'Lagos, Nigeria',
  bio: 'Software Engineer and community leader passionate about alumni engagement.',
  socialLinks: {
    linkedin: 'https://linkedin.com/in/john-doe',
    twitter: 'https://twitter.com/john_doe',
  },
};

// Mock window.location.href
const mockLocationHref = vi.fn();
Object.defineProperty(window, 'location', {
  value: {
    href: mockLocationHref,
  },
  writable: true,
});

// Mock window.open
const mockWindowOpen = vi.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true,
});

describe('AlumniCard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders alumni information correctly', () => {
    render(<AlumniCard person={mockPerson} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('President - Lagos Chapter')).toBeInTheDocument();
    expect(screen.getByText('Class of 2015')).toBeInTheDocument();
    expect(screen.getByText('Lagos Chapter')).toBeInTheDocument();
    expect(screen.getByText('Lagos, Nigeria')).toBeInTheDocument();
    expect(screen.getByText(/Software Engineer and community leader/)).toBeInTheDocument();
  });

  it('highlights search terms in text', () => {
    render(<AlumniCard person={mockPerson} searchQuery="President" />);
    
    const highlightedText = screen.getByText('President');
    expect(highlightedText.tagName).toBe('MARK');
    expect(highlightedText).toHaveClass('bg-gold-200');
  });

  it('shows and hides contact information', async () => {
    const user = userEvent.setup();
    render(<AlumniCard person={mockPerson} />);
    
    // Contact info should be hidden initially
    expect(screen.queryByText('+234-************')).not.toBeInTheDocument();
    
    // Click show contact info
    const showButton = screen.getByLabelText(/show contact info/i);
    await user.click(showButton);
    
    // Contact info should now be visible
    expect(screen.getByText('+234-************')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    
    // Click hide contact info
    const hideButton = screen.getByLabelText(/hide contact info/i);
    await user.click(hideButton);
    
    // Contact info should be hidden again
    expect(screen.queryByText('+234-************')).not.toBeInTheDocument();
  });

  it('handles phone call action', async () => {
    const user = userEvent.setup();
    render(<AlumniCard person={mockPerson} />);
    
    // Show contact info first
    const showButton = screen.getByLabelText(/show contact info/i);
    await user.click(showButton);
    
    // Click call button
    const callButton = screen.getByRole('button', { name: /call/i });
    await user.click(callButton);
    
    expect(mockLocationHref).toHaveBeenCalledWith('tel:+234-************');
  });

  it('handles email action', async () => {
    const user = userEvent.setup();
    render(<AlumniCard person={mockPerson} />);
    
    // Show contact info first
    const showButton = screen.getByLabelText(/show contact info/i);
    await user.click(showButton);
    
    // Click email button
    const emailButton = screen.getByRole('button', { name: /email/i });
    await user.click(emailButton);
    
    expect(mockLocationHref).toHaveBeenCalledWith('mailto:<EMAIL>');
  });

  it('handles social media links', async () => {
    const user = userEvent.setup();
    render(<AlumniCard person={mockPerson} />);
    
    // Click LinkedIn button
    const linkedinButton = screen.getByLabelText(/linkedin profile/i);
    await user.click(linkedinButton);
    
    expect(mockWindowOpen).toHaveBeenCalledWith(
      'https://linkedin.com/in/john-doe',
      '_blank',
      'noopener,noreferrer'
    );
    
    // Click Twitter button
    const twitterButton = screen.getByLabelText(/twitter profile/i);
    await user.click(twitterButton);
    
    expect(mockWindowOpen).toHaveBeenCalledWith(
      'https://twitter.com/john_doe',
      '_blank',
      'noopener,noreferrer'
    );
  });

  it('renders compact mode correctly', () => {
    render(<AlumniCard person={mockPerson} compact />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('President - Lagos Chapter')).toBeInTheDocument();
    
    // Bio should not be visible in compact mode
    expect(screen.queryByText(/Software Engineer and community leader/)).not.toBeInTheDocument();
  });

  it('handles missing contact information gracefully', () => {
    const personWithoutContact: Person = {
      ...mockPerson,
      phone: undefined,
      email: undefined,
    };
    
    render(<AlumniCard person={personWithoutContact} />);
    
    // Contact information section should not be visible
    expect(screen.queryByText(/contact information/i)).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /call/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /email/i })).not.toBeInTheDocument();
  });

  it('handles missing social links gracefully', () => {
    const personWithoutSocial: Person = {
      ...mockPerson,
      socialLinks: undefined,
    };
    
    render(<AlumniCard person={personWithoutSocial} />);
    
    // Social profiles section should not be visible
    expect(screen.queryByText(/social profiles/i)).not.toBeInTheDocument();
  });

  it('handles image error gracefully', () => {
    render(<AlumniCard person={mockPerson} />);
    
    const avatar = screen.getByRole('img');
    
    // Simulate image error
    fireEvent.error(avatar);
    
    // Should show fallback initials
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('shows contact info when showContactInfo is false', () => {
    render(<AlumniCard person={mockPerson} showContactInfo={false} />);
    
    // Contact information section should not be visible
    expect(screen.queryByText(/contact information/i)).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /call/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /email/i })).not.toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<AlumniCard person={mockPerson} />);
    
    const showButton = screen.getByLabelText(/show contact info/i);
    expect(showButton).toHaveAttribute('aria-label', 'Show contact info');
    
    const callButton = screen.getByLabelText(/call john doe/i);
    expect(callButton).toHaveAttribute('aria-label', 'Call John Doe');
    
    const emailButton = screen.getByLabelText(/email john doe/i);
    expect(emailButton).toHaveAttribute('aria-label', 'Email John Doe');
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<AlumniCard person={mockPerson} />);
    
    // Tab through interactive elements
    await user.tab();
    expect(screen.getByLabelText(/show contact info/i)).toHaveFocus();
    
    await user.tab();
    expect(screen.getByLabelText(/linkedin profile/i)).toHaveFocus();
    
    await user.tab();
    expect(screen.getByLabelText(/twitter profile/i)).toHaveFocus();
  });
});