'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { FormErrors } from '@/lib/types';
import { ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface FormValidationSummaryProps {
  errors: FormErrors;
  isValid: boolean;
  showSuccessMessage?: boolean;
  className?: string;
  title?: string;
  successMessage?: string;
}

export function FormValidationSummary({
  errors,
  isValid,
  showSuccessMessage = false,
  className,
  title = 'Please fix the following errors:',
  successMessage = 'All fields are valid',
}: FormValidationSummaryProps) {
  const errorCount = Object.keys(errors).length;
  const totalErrors = Object.values(errors).reduce((sum, fieldErrors) => sum + fieldErrors.length, 0);

  if (errorCount === 0 && !showSuccessMessage) {
    return null;
  }

  if (isValid && showSuccessMessage) {
    return (
      <div 
        className={cn(
          'p-4 border border-green-200 bg-green-50 rounded-lg flex items-start',
          className
        )}
        role="status"
        aria-live="polite"
      >
        <CheckCircleIcon className="w-5 h-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
        <div>
          <p className="text-sm font-medium text-green-800">
            {successMessage}
          </p>
        </div>
      </div>
    );
  }

  if (errorCount === 0) {
    return null;
  }

  return (
    <div 
      className={cn(
        'p-4 border border-red-200 bg-red-50 rounded-lg',
        className
      )}
      role="alert"
      aria-live="assertive"
    >
      <div className="flex items-start">
        <ExclamationTriangleIcon className="w-5 h-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800 mb-2">
            {title} ({totalErrors} {totalErrors === 1 ? 'error' : 'errors'})
          </h3>
          <ul className="space-y-1 text-sm text-red-700">
            {Object.entries(errors).map(([fieldName, fieldErrors]) => (
              <li key={fieldName}>
                <strong className="capitalize">
                  {fieldName.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                </strong>
                <ul className="ml-4 mt-1 space-y-1">
                  {fieldErrors.map((error, index) => (
                    <li key={index} className="list-disc list-inside">
                      {error}
                    </li>
                  ))}
                </ul>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
}