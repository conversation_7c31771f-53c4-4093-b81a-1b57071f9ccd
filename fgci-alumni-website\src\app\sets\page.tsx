﻿import { Metadata } from 'next';
import { generatePageMetadata } from '@/components/seo';
import { REVALIDATION_TIMES } from '@/lib/isr';
import SetsPageClient from './SetsPageClient';

export const metadata: Metadata = generatePageMetadata({
  title: 'Graduation Sets',
  description: 'Browse FGC Ikom graduation sets by year. Find your classmates and reconnect with your graduating class.',
  keywords: ['graduation', 'sets', 'classmates', 'year groups', 'reunion', 'alumni'],
  pathname: '/sets',
  ogType: 'website',
});

// Enable ISR with weekly revalidation
export const revalidate = REVALIDATION_TIMES.WEEKLY;

export default function SetsPage() {
  return <SetsPageClient />;
}
