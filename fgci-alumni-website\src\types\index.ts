// Core type definitions for FGC Ikom Alumni Website

export interface Person {
  id: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  email?: string;
  phone?: string;
  whatsapp?: string;
  profileImage?: string;
  graduationYear: number;
  setId: string;
  chapterId?: string;
  currentCity?: string;
  currentCountry?: string;
  occupation?: string;
  company?: string;
  bio?: string;
  socialLinks?: SocialLinks;
  isDeceased?: boolean;
  dateOfDeath?: string;
  tributeMessage?: string;
}

export interface SocialLinks {
  facebook?: string;
  twitter?: string;
  instagram?: string;
  linkedin?: string;
}

export interface Chapter {
  id: string;
  name: string;
  slug: string;
  description: string;
  coverImage: string;
  region: 'North' | 'South' | 'East' | 'West' | 'Diaspora' | 'School-based';
  location: string;
  establishedDate: string;
  memberCount: number;
  executives: Executive[];
  activities: Activity[];
  contactInfo: ContactInfo;
  gallery?: GalleryImage[];
  motto?: string;
  meetingSchedule?: string;
}

export interface Executive {
  personId: string;
  person?: Person;
  position: string;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  responsibilities?: string[];
  contactEmail?: string;
  contactPhone?: string;
}

export interface Set {
  id: string;
  year: number;
  name: string;
  slug: string;
  motto?: string;
  description?: string;
  coverImage?: string;
  memberCount: number;
  executives: Executive[];
  activities: Activity[];
  gallery?: GalleryImage[];
  documents?: Document[];
  achievements?: string[];
}

export interface Activity {
  id: string;
  title: string;
  description: string;
  date: string;
  location?: string;
  type: 'meeting' | 'social' | 'charity' | 'academic' | 'sports' | 'other';
  images?: string[];
  attendeeCount?: number;
  organizer?: string;
}

export interface Event {
  id: string;
  title: string;
  slug: string;
  description: string;
  longDescription?: string;
  startDate: string;
  endDate?: string;
  location: string;
  virtualLink?: string;
  type: 'reunion' | 'fundraising' | 'social' | 'academic' | 'memorial' | 'other';
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  coverImage?: string;
  registrationLink?: string;
  registrationDeadline?: string;
  maxAttendees?: number;
  currentAttendees?: number;
  organizers: string[];
  sponsors?: string[];
  gallery?: GalleryImage[];
  agenda?: EventAgenda[];
}

export interface EventAgenda {
  time: string;
  title: string;
  description?: string;
  speaker?: string;
}

export interface GalleryImage {
  id: string;
  url: string;
  thumbnailUrl: string;
  title: string;
  description?: string;
  tags?: string[];
  albumId?: string;
  uploadedBy?: string;
  uploadedDate: string;
  photographer?: string;
  event?: string;
  location?: string;
  featured?: boolean;
  width?: number;
  height?: number;
}

export interface Album {
  id: string;
  title: string;
  slug: string;
  description?: string;
  coverImage: string;
  images: GalleryImage[];
  createdDate: string;
  eventId?: string;
  chapterId?: string;
  setId?: string;
  tags?: string[];
  isPublic: boolean;
}

export interface Memorial {
  id: string;
  personId: string;
  person: Person;
  dateOfDeath: string;
  biography: string;
  achievements: string[];
  tributes: Tribute[];
  photos: string[];
  burialDetails?: string;
  familyContacts?: ContactInfo;
  memorialService?: {
    date: string;
    location: string;
    details: string;
  };
}

export interface Tribute {
  id: string;
  authorId: string;
  authorName: string;
  message: string;
  date: string;
  approved: boolean;
}

export interface ContactInfo {
  email?: string;
  phone?: string;
  whatsapp?: string;
  address?: string;
  website?: string;
  socialMedia?: SocialLinks;
}

export interface Document {
  id: string;
  title: string;
  description?: string;
  fileUrl: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
  category: 'constitution' | 'minutes' | 'report' | 'newsletter' | 'other';
  access: 'public' | 'members' | 'executives';
}

export interface SearchFilters {
  query?: string;
  chapter?: string;
  set?: string;
  year?: number;
  position?: string;
  location?: string;
  sortBy?: 'name' | 'year' | 'chapter' | 'recent';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasMore: boolean;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  priority: 'high' | 'medium' | 'low';
  startDate: string;
  endDate?: string;
  link?: string;
  linkText?: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  chapterId?: string;
}