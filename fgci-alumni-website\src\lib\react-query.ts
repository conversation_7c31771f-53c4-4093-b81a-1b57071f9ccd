import { QueryClient, QueryClientProvider, useQuery, useMutation, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { apiClient, shouldRetry, getRetryDelay, isApiError } from './api-client';
import { 
  Chapter, 
  Set, 
  Event, 
  Person, 
  GalleryImage, 
  GalleryAlbum, 
  Memorial, 
  Activity,
  ApiResponse,
  PaginatedResponse,
  SearchResponse,
  SearchFilters,
  ExcoBotFilters,
  ContactFormData,
  CondolenceFormData,
  AppError
} from './types';

// Enhanced caching strategies based on data type
const CACHE_TIMES = {
  // Static content (rarely changes)
  static: {
    staleTime: 30 * 60 * 1000, // 30 minutes
    gcTime: 60 * 60 * 1000,    // 1 hour
  },
  // Semi-static content (chapters, sets)
  semiStatic: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 30 * 60 * 1000,    // 30 minutes
  },
  // Dynamic content (events, activities)
  dynamic: {
    staleTime: 5 * 60 * 1000,  // 5 minutes
    gcTime: 10 * 60 * 1000,    // 10 minutes
  },
  // Search results (short-lived)
  search: {
    staleTime: 2 * 60 * 1000,  // 2 minutes
    gcTime: 5 * 60 * 1000,     // 5 minutes
  },
  // Real-time content (very short-lived)
  realtime: {
    staleTime: 30 * 1000,      // 30 seconds
    gcTime: 2 * 60 * 1000,     // 2 minutes
  },
} as const;

// Query Client Configuration with enhanced caching
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: CACHE_TIMES.dynamic.staleTime,
      gcTime: CACHE_TIMES.dynamic.gcTime,
      retry: (failureCount, error) => {
        if (failureCount >= 3) return false;
        return shouldRetry(error);
      },
      retryDelay: getRetryDelay,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      // Enable background refetching for better UX
      refetchOnMount: 'always',
      // Network mode for offline support
      networkMode: 'online',
    },
    mutations: {
      retry: (failureCount, error) => {
        if (failureCount >= 2) return false;
        return shouldRetry(error);
      },
      retryDelay: getRetryDelay,
      networkMode: 'online',
    },
  },
});

// Query Keys
export const queryKeys = {
  chapters: {
    all: ['chapters'] as const,
    lists: () => [...queryKeys.chapters.all, 'list'] as const,
    list: (filters?: SearchFilters) => [...queryKeys.chapters.lists(), { filters }] as const,
    details: () => [...queryKeys.chapters.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.chapters.details(), slug] as const,
    activities: (slug: string) => [...queryKeys.chapters.detail(slug), 'activities'] as const,
    gallery: (slug: string) => [...queryKeys.chapters.detail(slug), 'gallery'] as const,
  },
  sets: {
    all: ['sets'] as const,
    lists: () => [...queryKeys.sets.all, 'list'] as const,
    list: (filters?: SearchFilters) => [...queryKeys.sets.lists(), { filters }] as const,
    details: () => [...queryKeys.sets.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.sets.details(), slug] as const,
    activities: (slug: string) => [...queryKeys.sets.detail(slug), 'activities'] as const,
    gallery: (slug: string) => [...queryKeys.sets.detail(slug), 'gallery'] as const,
  },
  events: {
    all: ['events'] as const,
    lists: () => [...queryKeys.events.all, 'list'] as const,
    list: (filters?: SearchFilters) => [...queryKeys.events.lists(), { filters }] as const,
    details: () => [...queryKeys.events.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.events.details(), slug] as const,
  },
  gallery: {
    all: ['gallery'] as const,
    lists: () => [...queryKeys.gallery.all, 'list'] as const,
    list: (filters?: SearchFilters) => [...queryKeys.gallery.lists(), { filters }] as const,
    albums: () => [...queryKeys.gallery.all, 'albums'] as const,
    album: (id: string) => [...queryKeys.gallery.albums(), id] as const,
  },
  excobot: {
    all: ['excobot'] as const,
    search: (filters: ExcoBotFilters) => [...queryKeys.excobot.all, 'search', filters] as const,
  },
  memorial: {
    all: ['memorial'] as const,
    list: () => [...queryKeys.memorial.all, 'list'] as const,
    condolences: (id: string) => [...queryKeys.memorial.all, 'condolences', id] as const,
  },
} as const;

// Custom Hooks for Chapters with optimized caching
export function useChapters(filters?: SearchFilters) {
  return useQuery({
    queryKey: queryKeys.chapters.list(filters),
    queryFn: () => apiClient.getPaginated<Chapter>('/chapters', filters),
    enabled: true,
    // Semi-static content - chapters don't change frequently
    staleTime: CACHE_TIMES.semiStatic.staleTime,
    gcTime: CACHE_TIMES.semiStatic.gcTime,
  });
}

export function useChapter(slug: string) {
  return useQuery({
    queryKey: queryKeys.chapters.detail(slug),
    queryFn: () => apiClient.get<Chapter>(`/chapters/${slug}`),
    enabled: !!slug,
    // Semi-static content
    staleTime: CACHE_TIMES.semiStatic.staleTime,
    gcTime: CACHE_TIMES.semiStatic.gcTime,
  });
}

export function useChapterActivities(slug: string) {
  return useQuery({
    queryKey: queryKeys.chapters.activities(slug),
    queryFn: () => apiClient.get<Activity[]>(`/chapters/${slug}/activities`),
    enabled: !!slug,
    // Dynamic content - activities change more frequently
    staleTime: CACHE_TIMES.dynamic.staleTime,
    gcTime: CACHE_TIMES.dynamic.gcTime,
  });
}

export function useChapterGallery(slug: string) {
  return useQuery({
    queryKey: queryKeys.chapters.gallery(slug),
    queryFn: () => apiClient.get<GalleryImage[]>(`/chapters/${slug}/gallery`),
    enabled: !!slug,
    // Static content - gallery images rarely change
    staleTime: CACHE_TIMES.static.staleTime,
    gcTime: CACHE_TIMES.static.gcTime,
  });
}

// Custom Hooks for Sets
export function useSets(filters?: SearchFilters) {
  return useQuery({
    queryKey: queryKeys.sets.list(filters),
    queryFn: () => apiClient.getPaginated<Set>('/sets', filters),
    enabled: true,
  });
}

export function useSet(slug: string) {
  return useQuery({
    queryKey: queryKeys.sets.detail(slug),
    queryFn: () => apiClient.get<Set>(`/sets/${slug}`),
    enabled: !!slug,
  });
}

export function useSetActivities(slug: string) {
  return useQuery({
    queryKey: queryKeys.sets.activities(slug),
    queryFn: () => apiClient.get<Activity[]>(`/sets/${slug}/activities`),
    enabled: !!slug,
  });
}

export function useSetGallery(slug: string) {
  return useQuery({
    queryKey: queryKeys.sets.gallery(slug),
    queryFn: () => apiClient.get<GalleryAlbum[]>(`/sets/${slug}/gallery`),
    enabled: !!slug,
  });
}

// Custom Hooks for Events
export function useEvents(filters?: SearchFilters) {
  return useQuery({
    queryKey: queryKeys.events.list(filters),
    queryFn: () => apiClient.getPaginated<Event>('/events', filters),
    enabled: true,
  });
}

export function useEvent(slug: string) {
  return useQuery({
    queryKey: queryKeys.events.detail(slug),
    queryFn: () => apiClient.get<Event>(`/events/${slug}`),
    enabled: !!slug,
  });
}

// Custom Hooks for Gallery
export function useGallery(filters?: SearchFilters) {
  return useQuery({
    queryKey: queryKeys.gallery.list(filters),
    queryFn: () => apiClient.getPaginated<GalleryImage>('/gallery', filters),
    enabled: true,
  });
}

export function useGalleryAlbums() {
  return useQuery({
    queryKey: queryKeys.gallery.albums(),
    queryFn: () => apiClient.get<GalleryAlbum[]>('/gallery/albums'),
    enabled: true,
  });
}

export function useGalleryAlbum(id: string) {
  return useQuery({
    queryKey: queryKeys.gallery.album(id),
    queryFn: () => apiClient.get<GalleryAlbum>(`/gallery/albums/${id}`),
    enabled: !!id,
  });
}

// Custom Hooks for ExcoBot with optimized search caching
export function useExcoBotSearch(filters: ExcoBotFilters) {
  return useQuery({
    queryKey: queryKeys.excobot.search(filters),
    queryFn: () => apiClient.search<Person>('/excobot/search', filters.query || '', filters),
    enabled: !!filters.query && filters.query.length >= 2,
    // Search results caching
    staleTime: CACHE_TIMES.search.staleTime,
    gcTime: CACHE_TIMES.search.gcTime,
    // Debounce search requests
    refetchOnWindowFocus: false,
  });
}

// Custom Hooks for Memorial
export function useMemorial() {
  return useQuery({
    queryKey: queryKeys.memorial.list(),
    queryFn: () => apiClient.get<Memorial[]>('/memorial'),
    enabled: true,
  });
}

export function useMemorialCondolences(id: string) {
  return useQuery({
    queryKey: queryKeys.memorial.condolences(id),
    queryFn: () => apiClient.get<Memorial>(`/memorial/${id}/condolences`),
    enabled: !!id,
  });
}

// Mutation Hooks
export function useContactForm() {
  return useMutation({
    mutationFn: (data: ContactFormData) => apiClient.post<{ success: boolean }>('/contact', data),
    onSuccess: () => {
      // Could add toast notification here
      console.log('Contact form submitted successfully');
    },
    onError: (error: AppError) => {
      console.error('Contact form submission failed:', error);
    },
  });
}

export function useCondolenceForm(memorialId: string) {
  return useMutation({
    mutationFn: (data: CondolenceFormData) => 
      apiClient.post<{ success: boolean }>(`/memorial/${memorialId}/condolences`, data),
    onSuccess: () => {
      // Invalidate condolences query to refetch
      queryClient.invalidateQueries({ queryKey: queryKeys.memorial.condolences(memorialId) });
      console.log('Condolence submitted successfully');
    },
    onError: (error: AppError) => {
      console.error('Condolence submission failed:', error);
    },
  });
}

// Prefetch helpers
export function prefetchChapter(slug: string) {
  return queryClient.prefetchQuery({
    queryKey: queryKeys.chapters.detail(slug),
    queryFn: () => apiClient.get<Chapter>(`/chapters/${slug}`),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function prefetchSet(slug: string) {
  return queryClient.prefetchQuery({
    queryKey: queryKeys.sets.detail(slug),
    queryFn: () => apiClient.get<Set>(`/sets/${slug}`),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Error handling utilities
export function getErrorMessage(error: unknown): string {
  if (isApiError(error)) {
    return error.message;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
}

export function isNetworkError(error: unknown): boolean {
  return isApiError(error) && error.type === 'network';
}

export function isServerError(error: unknown): boolean {
  return isApiError(error) && error.type === 'server';
}

export function isClientError(error: unknown): boolean {
  return isApiError(error) && error.type === 'client';
}

// Loading state helpers
export function createLoadingState<T>(
  isLoading: boolean,
  data: T | undefined,
  error: unknown
) {
  return {
    data: data || null,
    loading: isLoading,
    error: error ? getErrorMessage(error) : null,
  };
}