@import "tailwindcss";

:root {
  /* Primary Colors */
  --color-maroon: #7B0F17;
  --color-maroon-900: #5C0B12;
  --color-maroon-800: #6A0D15;
  --color-maroon-700: #7B0F17;
  --color-maroon-600: #8C1119;
  --color-maroon-500: #9D131B;

  /* Gold Colors */
  --color-gold: #D4A017;
  --color-gold-200: #F7E6C0;
  --color-gold-300: #F0D898;
  --color-gold-400: #E8CA70;
  --color-gold-500: #D4A017;
  --color-gold-600: #B8900F;

  /* Neutral Colors */
  --gray-900: #0f172a;
  --gray-800: #1e293b;
  --gray-700: #334155;
  --gray-600: #475569;
  --gray-500: #64748b;
  --gray-400: #94a3b8;
  --gray-300: #cbd5e1;
  --gray-200: #e2e8f0;
  --gray-100: #f1f5f9;
  --gray-50: #f8fafc;

  /* Complementary Colors */
  --color-charcoal: #121315;
  --color-warm-ivory: #F8F5EF;
  --color-slate: #3B4252;
  --color-champagne: #F2E6C9;
  --color-deep-navy: #0E3A5B;

  /* Focus States */
  --focus: 3px solid rgba(212,160,23,0.25);

  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Font Families */
  /* Values are provided by next/font via CSS variables; these are fallbacks */
  --font-heading: 'Newsreader Variable', 'Newsreader', 'Merriweather', serif;
  --font-body: 'Inter Variable', 'Inter', 'Open Sans', sans-serif;

  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* Durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* Easings */
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* Transforms */
  --transform-hover: translateY(-6px);
  --shadow-hover: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-body);
  line-height: var(--leading-normal);
  color: var(--gray-900);
  background-color: var(--color-warm-ivory);
  scroll-behavior: smooth;
}

/* Improved typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

/* Better button styles */
.btn-premium {
  background: linear-gradient(135deg, #D4A017 0%, #B8900F 100%);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 2rem;
  font-weight: 600;
  font-size: 1.125rem;
  box-shadow: 0 4px 15px rgba(212, 160, 23, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(212, 160, 23, 0.4);
}

/* Card improvements */
.card-premium {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
  transition: all 0.3s ease;
}

.card-premium:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 3px solid var(--color-gold);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Enhanced focus styles for interactive elements */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[tabindex]:focus-visible {
  outline: 3px solid var(--color-gold);
  outline-offset: 2px;
  box-shadow: 0 0 0 1px rgba(212, 160, 23, 0.2);
}

/* Focus styles for cards and interactive containers */
.card-focus:focus-visible {
  outline: 3px solid var(--color-gold);
  outline-offset: 2px;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(212, 160, 23, 0.2);
}

/* Skip to content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-maroon);
  color: white;
  padding: 12px 16px;
  text-decoration: none;
  border-radius: 6px;
  z-index: 9999;
  font-weight: 500;
  font-size: 14px;
  transition: all var(--duration-fast) var(--ease-out);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.skip-link:focus {
  top: 6px;
  outline: 3px solid var(--color-gold);
  outline-offset: 2px;
}

/* Keyboard navigation indicators */
.keyboard-nav-active {
  position: relative;
}

.keyboard-nav-active::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--color-gold);
  border-radius: 6px;
  opacity: 0;
  transition: opacity var(--duration-fast) var(--ease-out);
  pointer-events: none;
}

.keyboard-nav-active:focus-visible::after {
  opacity: 1;
}

/* Roving tabindex styles */
.roving-tabindex {
  position: relative;
}

.roving-tabindex[tabindex="0"] {
  background-color: rgba(212, 160, 23, 0.1);
}

.roving-tabindex[tabindex="0"]:focus {
  outline: 3px solid var(--color-gold);
  outline-offset: 2px;
}

/* Gallery keyboard navigation */
.gallery-keyboard-nav {
  position: relative;
}

.gallery-keyboard-nav:focus-visible {
  outline: 3px solid var(--color-gold);
  outline-offset: 2px;
  z-index: 10;
}

/* Carousel keyboard navigation */
.carousel-keyboard-nav {
  position: relative;
}

.carousel-keyboard-nav:focus-visible {
  outline: 3px solid var(--color-gold);
  outline-offset: 4px;
}

/* Tab order management */
.tab-order-first {
  order: -1;
}

.tab-order-last {
  order: 9999;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Disable transform animations but keep opacity for accessibility */
  .motion-safe-transform {
    transform: none !important;
  }

  /* Alternative feedback for disabled animations */
  .hover-feedback:hover {
    background-color: var(--gray-50) !important;
    border-color: var(--color-gold) !important;
  }

  .focus-feedback:focus {
    background-color: var(--gold-200) !important;
    border-color: var(--color-gold) !important;
  }

  .active-feedback:active {
    background-color: var(--gold-300) !important;
  }
}

/* Global animation control classes */
.reduce-motion *,
.reduce-motion *::before,
.reduce-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.disable-animations *,
.disable-animations *::before,
.disable-animations *::after {
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

/* Alternative feedback styles for reduced motion */
.reduce-motion .btn-base:hover,
.disable-animations .btn-base:hover {
  background-color: var(--color-gold-600) !important;
  border-color: var(--color-gold-600) !important;
}

.reduce-motion .card-hover:hover,
.disable-animations .card-hover:hover {
  border-color: var(--color-gold) !important;
  background-color: var(--gray-50) !important;
}

/* Focus indicators for reduced motion */
.reduce-motion *:focus-visible,
.disable-animations *:focus-visible {
  outline: 3px solid var(--color-gold) !important;
  outline-offset: 2px !important;
  background-color: var(--gold-200) !important;
}

/* Loading states for reduced motion */
.reduce-motion .skeleton,
.disable-animations .skeleton {
  background: var(--gray-200) !important;
  animation: none !important;
}

.reduce-motion .skeleton::after,
.disable-animations .skeleton::after {
  content: 'Loading...' !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  font-size: 14px !important;
  color: var(--gray-600) !important;
}

/* Status indicators for reduced motion users */
.reduce-motion .status-indicator,
.disable-animations .status-indicator {
  position: relative;
}

.reduce-motion .status-indicator::before,
.disable-animations .status-indicator::before {
  content: attr(data-status) !important;
  position: absolute !important;
  top: -20px !important;
  left: 0 !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  color: var(--gray-700) !important;
  text-transform: uppercase !important;
}

/* Interactive element feedback */
.reduce-motion .interactive:hover,
.disable-animations .interactive:hover {
  border-left: 4px solid var(--color-gold) !important;
  padding-left: calc(1rem - 4px) !important;
}

.reduce-motion .interactive:focus,
.disable-animations .interactive:focus {
  border-left: 4px solid var(--color-maroon) !important;
  padding-left: calc(1rem - 4px) !important;
}

/* Pattern-based feedback for state changes */
.reduce-motion .state-success,
.disable-animations .state-success {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(16, 185, 129, 0.1) 2px,
    rgba(16, 185, 129, 0.1) 4px
  ) !important;
}

.reduce-motion .state-error,
.disable-animations .state-error {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(239, 68, 68, 0.1) 2px,
    rgba(239, 68, 68, 0.1) 4px
  ) !important;
}

.reduce-motion .state-warning,
.disable-animations .state-warning {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(245, 158, 11, 0.1) 2px,
    rgba(245, 158, 11, 0.1) 4px
  ) !important;
}

/* Screen reader only utility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    /* Enhanced contrast colors */
    --color-maroon: #5C0B12;
    --color-gold: #B8900F;
    --gray-600: #1e293b;
    --gray-500: #334155;
    --gray-400: #475569;
    --gray-300: #64748b;
  }

  /* Text contrast improvements */
  .text-gray-600 {
    color: var(--gray-900) !important;
  }

  .text-gray-500 {
    color: var(--gray-800) !important;
  }

  .text-gray-400 {
    color: var(--gray-700) !important;
  }

  /* Border contrast improvements */
  .border-gray-200 {
    border-color: var(--gray-700) !important;
  }

  .border-gray-300 {
    border-color: var(--gray-800) !important;
  }

  /* Background contrast improvements */
  .bg-gray-100 {
    background-color: var(--gray-200) !important;
  }

  .bg-gray-50 {
    background-color: var(--gray-100) !important;
  }

  /* Button contrast improvements */
  .btn-outline {
    border-width: 2px !important;
  }

  .btn-ghost {
    background-color: var(--gray-100) !important;
  }

  /* Card contrast improvements */
  .card-base {
    border: 2px solid var(--gray-300) !important;
  }

  /* Focus improvements */
  *:focus-visible {
    outline-width: 4px !important;
    outline-color: var(--color-maroon) !important;
  }
}

/* Forced colors mode (Windows High Contrast) */
@media (forced-colors: active) {
  .btn-base {
    border: 1px solid ButtonText;
  }

  .card-base {
    border: 1px solid ButtonText;
  }

  .skip-link {
    background: ButtonFace;
    color: ButtonText;
    border: 1px solid ButtonText;
  }

  *:focus-visible {
    outline: 2px solid Highlight;
  }
}

/* Color contrast utilities */
.contrast-aa {
  /* Ensures WCAG AA compliance (4.5:1 ratio) */
  color: var(--gray-900);
  background-color: white;
}

.contrast-aaa {
  /* Ensures WCAG AAA compliance (7:1 ratio) */
  color: black;
  background-color: white;
}

/* High contrast text utility */
.high-contrast-text {
  color: var(--gray-900);
  font-weight: 500;
}

@media (prefers-contrast: high) {
  .high-contrast-text {
    color: black !important;
    font-weight: 600 !important;
  }
}

/* Alternative indicators beyond color */
.status-success::before {
  content: '✓ ';
  font-weight: bold;
}

.status-error::before {
  content: '✗ ';
  font-weight: bold;
}

.status-warning::before {
  content: '⚠ ';
  font-weight: bold;
}

.status-info::before {
  content: 'ℹ ';
  font-weight: bold;
}

/* Pattern-based indicators */
.pattern-success {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(16, 185, 129, 0.1) 2px,
    rgba(16, 185, 129, 0.1) 4px
  );
}

.pattern-error {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(239, 68, 68, 0.1) 2px,
    rgba(239, 68, 68, 0.1) 4px
  );
}

.pattern-warning {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(245, 158, 11, 0.1) 2px,
    rgba(245, 158, 11, 0.1) 4px
  );
}

/* Ensure interactive elements have minimum touch target size */
button,
input,
select,
textarea,
a {
  min-height: 44px;
}

/* Exception for small buttons that are clearly labeled */
button.btn-sm,
.btn-sm {
  min-height: 32px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gold-600);
}

/* Utility classes */
.text-balance {
  text-wrap: balance;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.container-custom {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 3rem;
  }
}

/* Component base styles */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-in-out);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn-base:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.card-base {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all var(--duration-normal) var(--ease-in-out);
}

.card-hover:hover {
  transform: var(--transform-hover);
  box-shadow: var(--shadow-hover);
  border-color: var(--color-gold);
}

/* Typography utilities */
.heading-1 {
  font-family: var(--font-heading);
  font-size: var(--text-5xl);
  font-weight: 700;
  line-height: var(--leading-tight);
}

.heading-2 {
  font-family: var(--font-heading);
  font-size: var(--text-4xl);
  font-weight: 600;
  line-height: var(--leading-tight);
}

.heading-3 {
  font-family: var(--font-heading);
  font-size: var(--text-3xl);
  font-weight: 600;
  line-height: var(--leading-snug);
}

.body-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
  color: var(--gray-700);
}

.body-base {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--gray-700);
}

.body-small {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--gray-600);
}

/* Custom Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out forwards;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(8px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-slide-up {
  animation: slideUp 0.25s ease-out forwards;
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Enhanced containment for better performance */
.contain-layout {
  contain: layout;
}

.contain-style {
  contain: style;
}

.contain-paint {
  contain: paint;
}

.contain-size {
  contain: size;
}

.contain-strict {
  contain: strict;
}

/* Content visibility optimizations */
.content-visibility-auto {
  content-visibility: auto;
}

.content-visibility-hidden {
  content-visibility: hidden;
}

/* Gallery specific optimizations */
.gallery-grid {
  contain: layout style paint;
  content-visibility: auto;
}

.gallery-image {
  contain: layout style paint;
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Image loading optimizations */
.image-container {
  position: relative;
  overflow: hidden;
  contain: layout style paint;
}

.image-container img {
  will-change: opacity;
  transition: opacity 0.3s ease-in-out;
}

/* Responsive image containers */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-photo {
  aspect-ratio: 4 / 3;
}

.aspect-portrait {
  aspect-ratio: 3 / 4;
}

/* Layout shift prevention */
.prevent-layout-shift {
  min-height: 200px;
  background-color: var(--gray-100);
}

/* Core Web Vitals optimizations */
.optimize-cls {
  /* Prevent layout shift with explicit sizing */
  width: 100%;
  height: auto;
  aspect-ratio: attr(width) / attr(height);
}

.optimize-lcp {
  /* Optimize Largest Contentful Paint */
  content-visibility: auto;
  contain-intrinsic-size: 1200px 600px;
}

.optimize-fcp {
  /* Optimize First Contentful Paint */
  font-display: swap;
  text-rendering: optimizeSpeed;
}

/* Font loading handled by next/font. No local @font-face needed. */

/* Critical above-the-fold styles */
.hero-section {
  min-height: 60vh;
  background-color: var(--color-maroon);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navigation-header {
  height: 80px;
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

/* Skeleton loading states for better perceived performance */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-text {
  height: 1em;
  margin-bottom: 0.5em;
  border-radius: 4px;
}

.skeleton-text:last-child {
  width: 60%;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-card {
  height: 200px;
  border-radius: 8px;
}

/* Intersection observer optimizations */
.lazy-load-container {
  min-height: 200px;
  background-color: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
}

.lazy-load-placeholder {
  color: var(--gray-500);
  font-size: 14px;
}

/* Critical resource loading indicators */
.loading-critical {
  position: relative;
  overflow: hidden;
}

.loading-critical::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Optimize rendering performance */
.gpu-layer {
  transform: translateZ(0);
  will-change: transform;
}

.optimize-paint {
  contain: layout style paint;
}

.optimize-composite {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* Reduce paint complexity */
.simple-border {
  border: 1px solid var(--gray-300);
  box-shadow: none;
}

.simple-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Optimize animations for 60fps */
.smooth-animation {
  animation-duration: 0.3s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-fill-mode: both;
}

/* Critical path CSS inlining helpers */
.critical-above-fold {
  /* Styles for content above the fold */
  display: block;
  visibility: visible;
}

.non-critical-below-fold {
  /* Styles for content below the fold */
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* Critical resource hints */
.preload-image {
  position: absolute;
  top: -9999px;
  left: -9999px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Smooth scroll behavior */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* Image loading states */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Lightbox optimizations */
.lightbox-backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Intersection observer optimization */
.lazy-load-trigger {
  height: 1px;
  width: 1px;
  opacity: 0;
  pointer-events: none;
}