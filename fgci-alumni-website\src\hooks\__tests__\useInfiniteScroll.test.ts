import { renderHook } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useInfiniteScroll } from '../useInfiniteScroll';

// Mock IntersectionObserver
const mockIntersectionObserver = vi.fn();
mockIntersectionObserver.mockReturnValue({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe('useInfiniteScroll', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('creates intersection observer with correct options', () => {
    const mockOnLoadMore = vi.fn();
    
    renderHook(() => useInfiniteScroll({
      hasNextPage: true,
      isLoading: false,
      onLoadMore: mockOnLoadMore,
      threshold: 0.5,
      rootMargin: '100px'
    }));

    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      expect.objectContaining({
        threshold: 0.5,
        rootMargin: '100px',
        trackVisibility: true,
        delay: 100
      })
    );
  });

  it('does not create observer when disabled', () => {
    const mockOnLoadMore = vi.fn();
    
    renderHook(() => useInfiniteScroll({
      hasNextPage: true,
      isLoading: false,
      onLoadMore: mockOnLoadMore,
      enabled: false
    }));

    expect(mockIntersectionObserver).not.toHaveBeenCalled();
  });

  it('returns sentinel ref', () => {
    const mockOnLoadMore = vi.fn();
    
    const { result } = renderHook(() => useInfiniteScroll({
      hasNextPage: true,
      isLoading: false,
      onLoadMore: mockOnLoadMore
    }));

    expect(result.current).toBeDefined();
    expect(typeof result.current).toBe('object');
  });

  it('uses default values correctly', () => {
    const mockOnLoadMore = vi.fn();
    
    renderHook(() => useInfiniteScroll({
      hasNextPage: true,
      isLoading: false,
      onLoadMore: mockOnLoadMore
    }));

    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      expect.objectContaining({
        threshold: 0.1,
        rootMargin: '200px',
        trackVisibility: true,
        delay: 100
      })
    );
  });

  it('cleans up observer on unmount', () => {
    const mockObserver = {
      observe: vi.fn(),
      unobserve: vi.fn(),
      disconnect: vi.fn(),
    };
    mockIntersectionObserver.mockReturnValue(mockObserver);

    const mockOnLoadMore = vi.fn();
    
    const { unmount } = renderHook(() => useInfiniteScroll({
      hasNextPage: true,
      isLoading: false,
      onLoadMore: mockOnLoadMore
    }));

    unmount();

    expect(mockObserver.disconnect).toHaveBeenCalled();
  });
});