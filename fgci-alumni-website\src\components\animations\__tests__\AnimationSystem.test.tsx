import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AnimationProvider } from '@/components/providers/AnimationProvider';
import { PageTransition } from '../PageTransition';
import { HoverAnimation } from '../HoverAnimation';
import { LoadingSkeleton } from '../LoadingSkeleton';

// Mock Next.js router
vi.mock('next/navigation', () => ({
  usePathname: () => '/test',
}));

// Mock Framer Motion for testing
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <div>{children}</div>,
}));

describe('Animation System', () => {
  let mockMatchMedia: any;

  beforeEach(() => {
    // Mock matchMedia
    mockMatchMedia = vi.fn((query) => ({
      matches: query === '(prefers-reduced-motion: reduce)',
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }));

    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: mockMatchMedia,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('AnimationProvider', () => {
    it('should provide animation context', () => {
      const TestComponent = () => {
        return <div data-testid="test">Test</div>;
      };

      render(
        <AnimationProvider>
          <TestComponent />
        </AnimationProvider>
      );

      expect(screen.getByTestId('test')).toBeInTheDocument();
    });

    it('should detect reduced motion preference', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }));

      render(
        <AnimationProvider>
          <div>Test</div>
        </AnimationProvider>
      );

      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-reduced-motion: reduce)');
    });
  });

  describe('PageTransition', () => {
    it('should render children', () => {
      render(
        <AnimationProvider>
          <PageTransition>
            <div data-testid="page-content">Page Content</div>
          </PageTransition>
        </AnimationProvider>
      );

      expect(screen.getByTestId('page-content')).toBeInTheDocument();
    });

    it('should apply reduced motion when preferred', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }));

      render(
        <AnimationProvider>
          <PageTransition>
            <div data-testid="page-content">Page Content</div>
          </PageTransition>
        </AnimationProvider>
      );

      expect(screen.getByTestId('page-content')).toBeInTheDocument();
    });
  });

  describe('HoverAnimation', () => {
    it('should render children without animation when reduced motion is preferred', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }));

      render(
        <AnimationProvider>
          <HoverAnimation>
            <div data-testid="hover-content">Hover Content</div>
          </HoverAnimation>
        </AnimationProvider>
      );

      expect(screen.getByTestId('hover-content')).toBeInTheDocument();
    });

    it('should be disabled when disabled prop is true', () => {
      render(
        <AnimationProvider>
          <HoverAnimation disabled>
            <div data-testid="disabled-content">Disabled Content</div>
          </HoverAnimation>
        </AnimationProvider>
      );

      expect(screen.getByTestId('disabled-content')).toBeInTheDocument();
    });
  });

  describe('LoadingSkeleton', () => {
    it('should render skeleton with correct variant', () => {
      render(
        <AnimationProvider>
          <LoadingSkeleton variant="card" data-testid="skeleton" />
        </AnimationProvider>
      );

      const skeleton = screen.getByTestId('skeleton');
      expect(skeleton).toBeInTheDocument();
      expect(skeleton).toHaveClass('h-48', 'rounded-lg');
    });

    it('should render multiple lines for text variant', () => {
      render(
        <AnimationProvider>
          <LoadingSkeleton variant="text" lines={3} />
        </AnimationProvider>
      );

      // Should render 3 skeleton lines
      const skeletons = document.querySelectorAll('.h-4');
      expect(skeletons).toHaveLength(3);
    });

    it('should not animate when reduced motion is preferred', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }));

      render(
        <AnimationProvider>
          <LoadingSkeleton variant="card" />
        </AnimationProvider>
      );

      // Should render without motion components when reduced motion is preferred
      expect(document.querySelector('.bg-gray-200')).toBeInTheDocument();
    });
  });

  describe('CSS Classes', () => {
    it('should apply reduced motion classes when preference is set', () => {
      mockMatchMedia.mockImplementation((query) => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      }));

      render(
        <AnimationProvider>
          <div>Test</div>
        </AnimationProvider>
      );

      // Check if the class is added to document element
      // Note: In test environment, we can't easily test document.documentElement changes
      // This would be better tested in an E2E test
      expect(mockMatchMedia).toHaveBeenCalled();
    });
  });

  describe('Alternative Feedback', () => {
    it('should provide visual feedback for interactions', () => {
      const TestButton = () => (
        <button 
          className="hover-feedback focus-feedback"
          data-testid="feedback-button"
        >
          Test Button
        </button>
      );

      render(
        <AnimationProvider>
          <TestButton />
        </AnimationProvider>
      );

      const button = screen.getByTestId('feedback-button');
      expect(button).toHaveClass('hover-feedback', 'focus-feedback');
    });
  });

  describe('Accessibility', () => {
    it('should maintain accessibility when animations are disabled', () => {
      render(
        <AnimationProvider>
          <button aria-label="Test button">
            <HoverAnimation>
              <span>Click me</span>
            </HoverAnimation>
          </button>
        </AnimationProvider>
      );

      const button = screen.getByRole('button', { name: 'Test button' });
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', 'Test button');
    });

    it('should support keyboard navigation', () => {
      render(
        <AnimationProvider>
          <HoverAnimation>
            <button data-testid="keyboard-button">Keyboard Test</button>
          </HoverAnimation>
        </AnimationProvider>
      );

      const button = screen.getByTestId('keyboard-button');
      button.focus();
      expect(button).toHaveFocus();
    });
  });
});