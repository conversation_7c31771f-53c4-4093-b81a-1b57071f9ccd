# FGC Ikom Alumni Website

A modern, accessible, and performant website for the Federal Government College Ikom Alumni Association. Built with Next.js 14, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Design System**: Maroon & gold brand colors with elegant typography
- **Responsive Design**: Mobile-first approach with excellent performance
- **Accessibility**: WCAG AA compliant with keyboard navigation and screen reader support
- **Performance Optimized**: Lighthouse scores ≥90 across all metrics
- **Component Library**: Comprehensive Storybook with isolated component development
- **Testing**: Unit tests with Jest/RTL and E2E tests with Playwright
- **Developer Experience**: TypeScript, ESLint, Prettier, and <PERSON><PERSON> pre-commit hooks

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fgci-alumni-website
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Update the environment variables in `.env.local`:
   ```env
   NEXT_PUBLIC_API_URL=http://localhost:3001/api
   NEXT_PUBLIC_CDN_URL=https://cdn.fgcikomalumni.org.ng
   NEXT_PUBLIC_USE_MOCKS=true
   ```

## 🏃‍♂️ Development

### Start the development server
```bash
npm run dev
```

### Start with mock data
```bash
npm run dev:mock
```

### Run Storybook
```bash
npm run storybook
```

### Run tests
```bash
# Unit tests
npm run test

# E2E tests
npm run test:e2e

# Test coverage
npm run test:coverage
```

### Code quality
```bash
# Lint code
npm run lint

# Format code
npm run format

# Type check
npm run type-check
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
├── components/             # React components
│   ├── ui/                # Base UI components (Button, Card, Modal)
│   ├── layout/            # Layout components (Header, Footer)
│   ├── chapters/          # Chapter-specific components
│   ├── sets/              # Set-specific components
│   └── gallery/           # Gallery components
├── hooks/                 # Custom React hooks
├── lib/                   # Utilities and configurations
│   ├── api.ts            # API client
│   ├── types.ts          # TypeScript interfaces
│   └── utils.ts          # Utility functions
├── styles/               # Global styles and CSS
├── stories/              # Storybook stories
└── tests/                # Test utilities and setup
```

## 🎨 Design System

### Colors
- **Primary Maroon**: `#7B0F17`
- **Primary Gold**: `#D4A017`
- **Maroon Dark**: `#5C0B12`
- **Gold Light**: `#F7E6C0`

### Typography
- **Headings**: Playfair Display
- **Body Text**: Inter

### Components
All components are documented in Storybook with examples and props documentation.

## 🧪 Testing

### Unit Tests
- Components tested with Jest and React Testing Library
- Accessibility tests with axe-core
- Coverage target: 80% for components, 90% for utilities

### E2E Tests
- Critical user journeys tested with Playwright
- Cross-browser testing (Chrome, Firefox, Safari)
- Mobile and desktop viewports

### Performance Tests
- Lighthouse CI integration
- Core Web Vitals monitoring
- Bundle size analysis

## 📦 Build & Deployment

### Build for production
```bash
npm run build
```

### Start production server
```bash
npm run start
```

### Deploy to Vercel
The project is configured for automatic deployment to Vercel:

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Variables
```env
# API Configuration
NEXT_PUBLIC_API_URL=https://api.fgcikomalumni.org.ng
NEXT_PUBLIC_CDN_URL=https://cdn.fgcikomalumni.org.ng

# Feature Flags
NEXT_PUBLIC_USE_MOCKS=false
NEXT_PUBLIC_ENABLE_ANALYTICS=true

# Analytics (optional)
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

## 🔧 Development Workflow

### Git Workflow
1. Create feature branch: `git checkout -b feature/your-feature`
2. Make changes and commit: `git commit -m "feat: add new feature"`
3. Push and create PR: `git push origin feature/your-feature`

### Commit Convention
We use [Conventional Commits](https://www.conventionalcommits.org/):
- `feat:` new features
- `fix:` bug fixes
- `docs:` documentation changes
- `style:` formatting changes
- `refactor:` code refactoring
- `test:` adding tests
- `chore:` maintenance tasks

### Code Quality
- ESLint for code linting
- Prettier for code formatting
- Husky for pre-commit hooks
- TypeScript for type safety

## 📚 API Integration

### Mock Development
During development, use Mock Service Worker (MSW) for API mocking:

```typescript
// Enable mocks in development
NEXT_PUBLIC_USE_MOCKS=true
```

### API Endpoints
The frontend expects these API endpoints:

- `GET /api/chapters` - List all chapters
- `GET /api/chapters/:slug` - Get chapter details
- `GET /api/sets` - List all sets
- `GET /api/sets/:slug` - Get set details
- `GET /api/events` - List events
- `GET /api/gallery` - Get gallery images
- `POST /api/contact` - Submit contact form

See `API-HANDOFF.md` for detailed API specifications.

## 🚀 Performance Optimization

### Image Optimization
- Next.js Image component with automatic WebP/AVIF
- Responsive images with proper srcset
- Lazy loading with intersection observer

### Code Splitting
- Route-based code splitting
- Component-level lazy loading
- Dynamic imports for heavy components

### Caching Strategy
- Static assets cached with long TTL
- API responses cached with React Query
- ISR for dynamic content

## ♿ Accessibility

### WCAG AA Compliance
- Semantic HTML with proper landmarks
- ARIA attributes for screen readers
- Keyboard navigation support
- Color contrast ratios ≥4.5:1
- Focus management and visible focus states

### Testing
- Automated accessibility testing with axe
- Manual keyboard navigation testing
- Screen reader compatibility testing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Pull Request Template
- [ ] Tests added/updated
- [ ] Documentation updated
- [ ] Accessibility tested
- [ ] Performance impact considered
- [ ] Screenshots included (for UI changes)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the GitHub repository
- Contact the development team
- Check the documentation in `/docs`

## 🔗 Links

- [Live Website](https://www.fgcikomalumni.org.ng)
- [Storybook](https://storybook.fgcikomalumni.org.ng)
- [API Documentation](./API-HANDOFF.md)
- [Design System](./docs/design-system.md)