'use client';

import { useState } from 'react';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon,
  CalendarIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { Avatar } from '@/components/ui/Avatar';
import { Button } from '@/components/ui/Button';

interface ChapterExco {
  id: string;
  name: string;
  role?: string;
  phone?: string;
  email?: string;
  photo?: string;
  graduationYear?: number;
  chapterName: string;
  chapterRegion?: string;
  currentLocation?: string;
  bio?: string;
  termStartDate?: string;
  termEndDate?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
}

interface ChapterExcoListItemProps {
  executive: ChapterExco;
  isLast: boolean;
}

export function ChapterExcoListItem({ executive, isLast }: ChapterExcoListItemProps) {
  const [imageError, setImageError] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleCall = () => {
    if (executive.phone) {
      try {
        window.location.href = `tel:${executive.phone}`;
      } catch (error) {
        console.error('Failed to initiate call:', error);
      }
    }
  };

  const handleEmail = () => {
    if (executive.email) {
      try {
        window.location.href = `mailto:${executive.email}?subject=FGC Ikom Alumni - Contact from Website`;
      } catch (error) {
        console.error('Failed to open email client:', error);
      }
    }
  };

  const handleWhatsApp = () => {
    if (executive.phone) {
      try {
        // Clean phone number and ensure it starts with country code
        let cleanPhone = executive.phone.replace(/[^\d+]/g, '');
        
        // If phone doesn't start with +, assume it's Nigerian number and add +234
        if (!cleanPhone.startsWith('+')) {
          if (cleanPhone.startsWith('0')) {
            cleanPhone = '+234' + cleanPhone.substring(1);
          } else if (cleanPhone.startsWith('234')) {
            cleanPhone = '+' + cleanPhone;
          } else {
            cleanPhone = '+234' + cleanPhone;
          }
        }
        
        const message = encodeURIComponent(`Hello ${executive.name}, I found your contact through the FGC Ikom Alumni website.`);
        window.open(`https://wa.me/${cleanPhone}?text=${message}`, '_blank');
      } catch (error) {
        console.error('Failed to open WhatsApp:', error);
      }
    }
  };

  return (
    <div className={`${!isLast ? 'border-b border-gray-200' : ''}`}>
      <div className="p-4 hover:bg-gray-50 transition-colors">
        <div className="flex items-center justify-between">
          {/* Main Content */}
          <div className="flex items-center space-x-4 flex-1">
            {/* Avatar */}
            <Avatar
              src={!imageError ? executive.photo : undefined}
              alt={executive.name}
              size="md"
              fallback={executive.name}
              onError={() => setImageError(true)}
            />

            {/* Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-1">
                <h3 className="font-semibold text-gray-900 truncate">
                  {executive.name}
                </h3>
                {executive.role && (
                  <span className="bg-maroon-100 text-maroon-800 text-xs px-2 py-1 rounded-full whitespace-nowrap">
                    {executive.role}
                  </span>
                )}
              </div>
              
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                {executive.graduationYear && (
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-1 text-gray-400" />
                    <span>Class of {executive.graduationYear}</span>
                  </div>
                )}
                
                {executive.currentLocation && (
                  <div className="flex items-center">
                    <MapPinIcon className="h-4 w-4 mr-1 text-gray-400" />
                    <span>{executive.currentLocation}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            {/* Contact Buttons */}
            <div className="hidden sm:flex gap-2">
              {executive.phone && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCall}
                  aria-label={`Call ${executive.name}`}
                >
                  <PhoneIcon className="h-4 w-4" />
                </Button>
              )}
              
              {executive.email && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEmail}
                  aria-label={`Email ${executive.name}`}
                >
                  <EnvelopeIcon className="h-4 w-4" />
                </Button>
              )}
              
              {executive.phone && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleWhatsApp}
                  aria-label={`WhatsApp ${executive.name}`}
                >
                  WhatsApp
                </Button>
              )}
            </div>

            {/* Expand Button */}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              <ChevronRightIcon 
                className={`h-5 w-5 transition-transform ${isExpanded ? 'rotate-90' : ''}`} 
              />
            </button>
          </div>
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {/* Bio */}
              {executive.bio && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">About</h4>
                  <p className="text-sm text-gray-600">{executive.bio}</p>
                </div>
              )}

              {/* Additional Info */}
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Details</h4>
                <div className="space-y-2 text-sm text-gray-600">
                  {executive.termStartDate && (
                    <div>
                      <span className="font-medium">Term:</span>{' '}
                      {(() => {
                        const startYear = new Date(executive.termStartDate).getFullYear();
                        const endYear = executive.termEndDate ? new Date(executive.termEndDate).getFullYear() : null;
                        const currentYear = new Date().getFullYear();
                        
                        if (endYear) {
                          const isCurrentTerm = currentYear >= startYear && currentYear <= endYear;
                          return `${startYear} - ${endYear}${isCurrentTerm ? ' (Current)' : ''}`;
                        } else {
                          const isCurrentTerm = currentYear >= startYear;
                          return `${startYear} - Present${isCurrentTerm ? ' (Current)' : ''}`;
                        }
                      })()}
                    </div>
                  )}
                  
                  {executive.chapterRegion && (
                    <div>
                      <span className="font-medium">Region:</span> {executive.chapterRegion}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Mobile Contact Actions */}
            <div className="sm:hidden mt-4 flex gap-2">
              {executive.phone && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCall}
                  className="flex-1"
                  aria-label={`Call ${executive.name}`}
                >
                  <PhoneIcon className="h-4 w-4 mr-1" />
                  Call
                </Button>
              )}
              
              {executive.email && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEmail}
                  className="flex-1"
                  aria-label={`Email ${executive.name}`}
                >
                  <EnvelopeIcon className="h-4 w-4 mr-1" />
                  Email
                </Button>
              )}
              
              {executive.phone && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleWhatsApp}
                  className="flex-1"
                  aria-label={`WhatsApp ${executive.name}`}
                >
                  WhatsApp
                </Button>
              )}
            </div>

            {/* Social Links */}
            {executive.socialLinks && Object.keys(executive.socialLinks).length > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <h4 className="font-medium text-gray-900 mb-2">Connect</h4>
                <div className="flex gap-3">
                  {executive.socialLinks.linkedin && (
                    <a
                      href={executive.socialLinks.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                      aria-label={`${executive.name} LinkedIn profile`}
                    >
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                      </svg>
                    </a>
                  )}
                  
                  {executive.socialLinks.twitter && (
                    <a
                      href={executive.socialLinks.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-blue-400 transition-colors"
                      aria-label={`${executive.name} Twitter profile`}
                    >
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                    </a>
                  )}
                  
                  {executive.socialLinks.facebook && (
                    <a
                      href={executive.socialLinks.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-blue-700 transition-colors"
                      aria-label={`${executive.name} Facebook profile`}
                    >
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    </a>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}