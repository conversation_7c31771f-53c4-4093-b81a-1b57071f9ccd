'use client';

import { BuildingOfficeIcon, GlobeAltIcon, AcademicCapIcon } from '@heroicons/react/24/outline';

interface ChapterExcoGroupProps {
  chapterName: string;
  chapterRegion?: string;
  chapterType: 'regional' | 'diaspora' | 'school-based';
  executiveCount: number;
}

export function ChapterExcoGroup({
  chapterName,
  chapterRegion,
  chapterType,
  executiveCount
}: ChapterExcoGroupProps) {
  const getTypeIcon = () => {
    switch (chapterType) {
      case 'diaspora':
        return <GlobeAltIcon className="h-5 w-5" />;
      case 'school-based':
        return <AcademicCapIcon className="h-5 w-5" />;
      default:
        return <BuildingOfficeIcon className="h-5 w-5" />;
    }
  };

  const getTypeLabel = () => {
    switch (chapterType) {
      case 'diaspora':
        return 'Diaspora Chapter';
      case 'school-based':
        return 'School-Based Chapter';
      default:
        return 'Regional Chapter';
    }
  };

  const getTypeColor = () => {
    switch (chapterType) {
      case 'diaspora':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'school-based':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-maroon-600 bg-maroon-50 border-maroon-200';
    }
  };

  return (
    <div className="flex items-center justify-between py-4 border-b border-gray-200">
      <div className="flex items-center space-x-4">
        <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${getTypeColor()}`}>
          {getTypeIcon()}
          <span className="text-sm font-medium">{getTypeLabel()}</span>
        </div>
        
        <div>
          <h2 className="text-xl font-bold text-gray-900">{chapterName}</h2>
          {chapterRegion && (
            <p className="text-sm text-gray-600">{chapterRegion}</p>
          )}
        </div>
      </div>
      
      <div className="text-right">
        <div className="text-sm text-gray-500">
          {executiveCount} {executiveCount === 1 ? 'Executive' : 'Executives'}
        </div>
      </div>
    </div>
  );
}