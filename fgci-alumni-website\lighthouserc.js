module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000',
        'http://localhost:3000/chapters',
        'http://localhost:3000/sets',
        'http://localhost:3000/events',
        'http://localhost:3000/gallery',
        'http://localhost:3000/excobot',
        'http://localhost:3000/contact',
      ],
      startServerCommand: 'npm start',
      startServerReadyPattern: 'ready on',
      startServerReadyTimeout: 30000,
      numberOfRuns: 3,
      settings: {
        chromeFlags: '--no-sandbox --disable-dev-shm-usage',
      },
    },
    assert: {
      assertions: {
        'categories:performance': ['error', { minScore: 0.85 }],
        'categories:accessibility': ['error', { minScore: 0.95 }],
        'categories:best-practices': ['error', { minScore: 0.90 }],
        'categories:seo': ['error', { minScore: 0.95 }],
        'categories:pwa': ['warn', { minScore: 0.80 }],
        
        // Core Web Vitals
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        'first-meaningful-paint': ['error', { maxNumericValue: 2000 }],
        'speed-index': ['error', { maxNumericValue: 3000 }],
        'interactive': ['error', { maxNumericValue: 3500 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
        
        // Accessibility
        'color-contrast': 'error',
        'image-alt': 'error',
        'label': 'error',
        'link-name': 'error',
        'button-name': 'error',
        'document-title': 'error',
        'html-has-lang': 'error',
        'html-lang-valid': 'error',
        'meta-description': 'error',
        'heading-order': 'error',
        'landmark-one-main': 'error',
        'list': 'error',
        'listitem': 'error',
        'definition-list': 'error',
        'dlitem': 'error',
        'bypass': 'error',
        'focus-traps': 'error',
        'focusable-controls': 'error',
        'interactive-element-affordance': 'error',
        'logical-tab-order': 'error',
        'managed-focus': 'error',
        'offscreen-content-hidden': 'error',
        'use-landmarks': 'error',
        'visual-order-follows-dom': 'error',
        
        // SEO
        'meta-description': 'error',
        'document-title': 'error',
        'crawlable-anchors': 'error',
        'is-crawlable': 'error',
        'robots-txt': 'error',
        'hreflang': 'off',
        'canonical': 'error',
        
        // Best Practices
        'is-on-https': 'off', // Disabled for local development
        'uses-http2': 'off',
        'uses-passive-event-listeners': 'error',
        'no-document-write': 'error',
        'external-anchors-use-rel-noopener': 'error',
        'geolocation-on-start': 'error',
        'notification-on-start': 'error',
        'no-vulnerable-libraries': 'error',
        'js-libraries': 'error',
        'deprecations': 'error',
        'third-party-cookies': 'error',
        
        // Performance
        'unused-css-rules': ['warn', { maxLength: 2000 }],
        'unused-javascript': ['warn', { maxLength: 20000 }],
        'modern-image-formats': 'error',
        'uses-optimized-images': 'error',
        'uses-text-compression': 'error',
        'uses-responsive-images': 'error',
        'efficient-animated-content': 'error',
        'preload-lcp-image': 'error',
        'total-byte-weight': ['warn', { maxNumericValue: 1600000 }],
        'render-blocking-resources': 'error',
        'unminified-css': 'error',
        'unminified-javascript': 'error',
        'user-timings': 'off',
        'critical-request-chains': 'warn',
        'uses-rel-preconnect': 'warn',
        'uses-rel-preload': 'warn',
        'font-display': 'error',
        'third-party-summary': 'off',
        'third-party-facades': 'off',
        'largest-contentful-paint-element': 'off',
        'lcp-lazy-loaded': 'error',
        'layout-shift-elements': 'off',
        'uses-long-cache-ttl': 'warn',
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
        'max-potential-fid': ['error', { maxNumericValue: 130 }],
        'no-unload-listeners': 'error',
        'non-composited-animations': 'warn',
        'unsized-images': 'error',
        'valid-source-maps': 'off',
        'preload-fonts': 'warn',
        'viewport': 'error',
        'without-javascript': 'off',
        'resource-summary': 'off',
        'third-party-summary': 'off',
        'pwa-cross-browser': 'off',
        'pwa-page-transitions': 'off',
        'pwa-each-page-has-url': 'off',
        'accesskeys': 'error',
        'aria-allowed-attr': 'error',
        'aria-command-name': 'error',
        'aria-hidden-body': 'error',
        'aria-hidden-focus': 'error',
        'aria-input-field-name': 'error',
        'aria-meter-name': 'error',
        'aria-progressbar-name': 'error',
        'aria-required-attr': 'error',
        'aria-required-children': 'error',
        'aria-required-parent': 'error',
        'aria-roles': 'error',
        'aria-toggle-field-name': 'error',
        'aria-tooltip-name': 'error',
        'aria-treeitem-name': 'error',
        'aria-valid-attr-value': 'error',
        'aria-valid-attr': 'error',
        'duplicate-id-active': 'error',
        'duplicate-id-aria': 'error',
        'form-field-multiple-labels': 'error',
        'frame-title': 'error',
        'input-image-alt': 'error',
        'meta-refresh': 'error',
        'meta-viewport': 'error',
        'object-alt': 'error',
        'tabindex': 'error',
        'td-headers-attr': 'error',
        'th-has-data-cells': 'error',
        'valid-lang': 'error',
        'video-caption': 'error',
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
};
