'use client';

import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useRovingTabIndex } from '@/hooks/useKeyboardNavigation';
import { Lightbox } from './Lightbox';
import { OptimizedImage } from './OptimizedImage';
import type { GalleryImage } from '@/lib/types';

interface GalleryGridProps {
  images: GalleryImage[];
  columns?: 2 | 3 | 4;
  onImageClick?: (image: GalleryImage, index: number) => void;
  className?: string;
  showLightbox?: boolean;
}

interface LazyImageProps {
  image: GalleryImage;
  onClick: () => void;
  className?: string;
  priority?: boolean;
}

function LazyImage({ image, onClick, className, priority = false }: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(priority); // Load immediately if priority
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      onClick();
    }
  };

  useEffect(() => {
    if (priority) return; // Skip intersection observer for priority images

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '200px' // Increased for better performance
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority]);

  return (
    <div
      ref={imgRef}
      className={cn(
        "relative group cursor-pointer overflow-hidden rounded-lg bg-gray-200",
        "transition-all duration-300 hover:shadow-lg hover:scale-105",
        "aspect-square gallery-keyboard-nav",
        "focus:outline-none focus:ring-2 focus:ring-gold-500 focus:ring-offset-2",
        className
      )}
      onClick={onClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label={`View image: ${image.alt}`}
    >
      {isInView && !hasError ? (
        <>
          {/* Loading placeholder */}
          {!isLoaded && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-200 animate-pulse">
              <div className="w-8 h-8 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}

          {/* Optimized image component */}
          <OptimizedImage
            src={image.thumbnailUrl || image.url}
            alt={image.alt}
            className={cn(
              "absolute inset-0 transition-all duration-300",
              "group-hover:brightness-110 group-hover:scale-105",
              isLoaded ? "opacity-100" : "opacity-0"
            )}
            onLoad={() => setIsLoaded(true)}
            onError={() => {
              setHasError(true);
              setIsLoaded(true);
            }}
            priority={priority}
            quality={priority ? 90 : 75}
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 20vw"
          />

          {/* Overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />

          {/* Image info overlay */}
          <div className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            {image.caption && (
              <p className="text-white text-sm font-medium truncate">
                {image.caption}
              </p>
            )}
            {image.photographer && (
              <p className="text-white/80 text-xs truncate">
                by {image.photographer}
              </p>
            )}
          </div>
        </>
      ) : hasError ? (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200">
          <div className="text-center text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-xs">Failed to load</p>
          </div>
        </div>
      ) : (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
    </div>
  );
}

export function GalleryGrid({
  images,
  columns = 3,
  onImageClick,
  className,
  showLightbox = true
}: GalleryGridProps) {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const imageRefs = useRef<HTMLDivElement[]>([]);

  // Set up roving tabindex for keyboard navigation
  const { setActiveItem } = useRovingTabIndex(imageRefs.current, 'both');

  const gridClasses = {
    2: 'grid-cols-2',
    3: 'grid-cols-2 md:grid-cols-3',
    4: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
  };

  const handleImageClick = (image: GalleryImage, index: number) => {
    if (onImageClick) {
      onImageClick(image, index);
    }
    
    if (showLightbox) {
      setCurrentImageIndex(index);
      setLightboxOpen(true);
    }
  };

  const handleNext = () => {
    setCurrentImageIndex((prev) => 
      prev < images.length - 1 ? prev + 1 : 0
    );
  };

  const handlePrevious = () => {
    setCurrentImageIndex((prev) => 
      prev > 0 ? prev - 1 : images.length - 1
    );
  };

  const handleClose = () => {
    setLightboxOpen(false);
  };

  if (images.length === 0) {
    return (
      <div className="text-center py-12">
        <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
        <h3 className="text-lg font-semibold text-gray-600 mb-2">No Images</h3>
        <p className="text-gray-500">No images available in this gallery.</p>
      </div>
    );
  }

  return (
    <>
      <div 
        className={cn(
          "grid gap-4 gallery-grid",
          gridClasses[columns],
          className
        )}
        role="grid"
        aria-label="Photo gallery"
      >
        {images.map((image, index) => (
          <div
            key={image.id}
            ref={(el) => {
              if (el) imageRefs.current[index] = el;
            }}
            role="gridcell"
            aria-rowindex={Math.floor(index / columns) + 1}
            aria-colindex={(index % columns) + 1}
          >
            <LazyImage
              image={image}
              onClick={() => handleImageClick(image, index)}
              priority={index < 12} // Prioritize first 12 images for faster loading
            />
          </div>
        ))}
      </div>

      {/* Lightbox */}
      {showLightbox && (
        <div data-testid="lightbox">
          <Lightbox
            images={images}
            currentIndex={currentImageIndex}
            isOpen={lightboxOpen}
            onClose={handleClose}
            onNext={handleNext}
            onPrevious={handlePrevious}
          />
        </div>
      )}
    </>
  );
}