'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from './Button';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

export interface HeroSlide {
  id: string;
  title: string;
  subtitle?: string;
  description: string;
  backgroundImage: string;
  ctaText?: string;
  ctaLink?: string;
  ctaAction?: () => void;
  overlay?: 'light' | 'dark' | 'gradient';
}

export interface HeroCarouselProps {
  slides: HeroSlide[];
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showDots?: boolean;
  showArrows?: boolean;
  className?: string;
}

export function HeroCarousel({
  slides,
  autoPlay = true,
  autoPlayInterval = 5000,
  showDots = true,
  showArrows = true,
  className
}: HeroCarouselProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(autoPlay);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  }, [slides.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  }, [slides.length]);

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index);
  }, []);

  // Auto-play functionality with reduced motion support
  useEffect(() => {
    if (!isPlaying || slides.length <= 1) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) return;

    const interval = setInterval(nextSlide, autoPlayInterval);
    return () => clearInterval(interval);
  }, [isPlaying, nextSlide, autoPlayInterval, slides.length]);

  // Pause on hover
  const handleMouseEnter = () => setIsPlaying(false);
  const handleMouseLeave = () => setIsPlaying(autoPlay);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        prevSlide();
      } else if (event.key === 'ArrowRight') {
        nextSlide();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [nextSlide, prevSlide]);

  if (!slides.length) return null;

  const currentSlideData = slides[currentSlide];

  const getOverlayClasses = (overlay?: string) => {
    switch (overlay) {
      case 'light':
        return 'bg-white/20';
      case 'dark':
        return 'bg-black/40';
      case 'gradient':
        return 'bg-gradient-to-r from-black/60 via-black/30 to-transparent';
      default:
        return 'bg-black/30';
    }
  };

  // Inline style fallbacks so the hero still looks good even if Tailwind isn't loading
  const getOverlayStyle = (overlay?: string): React.CSSProperties => {
    switch (overlay) {
      case 'light':
        return { backgroundColor: 'rgba(255,255,255,0.2)' };
      case 'dark':
        return { backgroundColor: 'rgba(0,0,0,0.4)' };
      case 'gradient':
        return { background: 'linear-gradient(to right, rgba(0,0,0,0.6), rgba(0,0,0,0.3), rgba(0,0,0,0))' };
      default:
        return { backgroundColor: 'rgba(0,0,0,0.3)' };
    }
  };

  return (
    <div
      className={cn(
        'relative w-full h-[70vh] min-h-[500px] overflow-hidden rounded-none bg-gray-900',
        className
      )}
      style={{ backgroundColor: '#121315' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role="region"
      aria-label="Hero carousel"
      data-testid="hero-carousel"
    >
      {/* Background Images */}
      <div className="absolute inset-0">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={cn(
              'absolute inset-0 transition-opacity duration-1000 ease-in-out motion-reduce:transition-none',
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            )}
            data-testid="carousel-slide"
          >
            <div
              className="w-full h-full bg-cover bg-center bg-no-repeat"
              style={{ backgroundImage: `url(${slide.backgroundImage})` }}
              role="img"
              aria-label={slide.title}
            />
            <div className={cn('absolute inset-0', getOverlayClasses(slide.overlay))} style={getOverlayStyle(slide.overlay)} />
          </div>
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 flex items-center justify-center h-full px-4 sm:px-6 lg:px-8">
        <div className="text-center text-white max-w-4xl mx-auto" style={{ color: '#ffffff' }}>
          <div
            key={currentSlideData.id}
            className="animate-fade-in"
          >
            {currentSlideData.subtitle && (
              <p className="text-gold-300 text-lg sm:text-xl mb-2 font-medium">
                {currentSlideData.subtitle}
              </p>
            )}
            <h1 className="heading-1 text-4xl sm:text-5xl lg:text-6xl mb-6 text-white">
              {currentSlideData.title}
            </h1>
            <p className="body-large text-xl sm:text-2xl mb-8 text-gray-100 max-w-3xl mx-auto">
              {currentSlideData.description}
            </p>
            {currentSlideData.ctaText && (
              <Button
                variant="secondary"
                size="lg"
                onClick={currentSlideData.ctaAction}
                className="text-lg px-8 py-4"
              >
                {currentSlideData.ctaText}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Navigation Arrows */}
      {showArrows && slides.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 z-20 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500"
            aria-label="Previous slide"
          >
            <ChevronLeftIcon className="w-6 h-6" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 z-20 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500"
            aria-label="Next slide"
          >
            <ChevronRightIcon className="w-6 h-6" />
          </button>
        </>
      )}

      {/* Dots Indicator */}
      {showDots && slides.length > 1 && (
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-20 flex space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={cn(
                'w-3 h-3 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gold-500',
                index === currentSlide
                  ? 'bg-gold-500 scale-125'
                  : 'bg-white/50 hover:bg-white/70'
              )}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Progress Bar */}
      {autoPlay && isPlaying && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-white/20">
          <div
            className="h-full bg-gold-500 transition-all duration-100 ease-linear"
            style={{
              width: `${((Date.now() % autoPlayInterval) / autoPlayInterval) * 100}%`
            }}
          />
        </div>
      )}
    </div>
  );
}
