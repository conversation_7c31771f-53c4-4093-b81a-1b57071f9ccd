'use client';

import { useState, useMemo } from 'react';
import { CalendarDaysIcon, UsersIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui';
import { LoadingStates } from '@/components/ui/LoadingStates';
import { EventCard } from '@/components/events/EventCard';
import { EventFilters } from '@/components/events/EventFilters';
import { useEvents } from '@/hooks/useEvents';

export default function EventsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('upcoming');
  const [selectedType, setSelectedType] = useState('all');
  const [page, setPage] = useState(1);

  // Build query parameters
  const queryParams = useMemo(() => {
    const params: any = {
      page,
      limit: 12,
    };

    if (searchQuery.trim()) {
      params.search = searchQuery.trim();
    }

    if (selectedStatus !== 'all') {
      params.status = selectedStatus;
    }

    if (selectedType !== 'all') {
      params.type = selectedType;
    }

    return params;
  }, [searchQuery, selectedStatus, selectedType, page]);

  const { data: eventsResponse, isLoading, error } = useEvents(queryParams);

  const events = eventsResponse?.data || [];
  const meta = eventsResponse?.meta;

  const hasActiveFilters = searchQuery.trim() !== '' || selectedStatus !== 'all' || selectedType !== 'all';

  const handleClearFilters = () => {
    setSearchQuery('');
    setSelectedStatus('all');
    setSelectedType('all');
    setPage(1);
  };

  const handleLoadMore = () => {
    if (meta?.hasNext) {
      setPage(prev => prev + 1);
    }
  };

  // Calculate stats
  const totalEvents = meta?.total || 0;
  const upcomingEvents = events.filter(event => event.status === 'upcoming').length;

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <CalendarDaysIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Failed to Load Events</h3>
          <p className="text-gray-600 mb-4">
            We're having trouble loading the events. Please try again later.
          </p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b">
        <div className="container-custom py-12">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="heading-1 text-4xl sm:text-5xl mb-4">
              Alumni Events
            </h1>
            <p className="body-large text-gray-600 mb-8">
              Stay connected with your fellow alumni through our exciting events, reunions, 
              networking opportunities, and fundraising activities. Join us in building lasting 
              memories and strengthening our community bonds.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1">
                <CalendarDaysIcon className="w-4 h-4" />
                <span>{upcomingEvents} Upcoming Events</span>
              </div>
              <div className="flex items-center gap-1">
                <UsersIcon className="w-4 h-4" />
                <span>{totalEvents} Total Events</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <EventFilters
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        selectedStatus={selectedStatus}
        onStatusChange={setSelectedStatus}
        selectedType={selectedType}
        onTypeChange={setSelectedType}
        onClearFilters={handleClearFilters}
        hasActiveFilters={hasActiveFilters}
      />

      {/* Events Grid */}
      <div className="container-custom py-12">
        {isLoading && page === 1 ? (
          <LoadingStates.Grid />
        ) : events.length === 0 ? (
          <div className="text-center py-12">
            <CalendarDaysIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="heading-3 text-xl text-gray-600 mb-2">No Events Found</h3>
            <p className="text-gray-500 mb-4">
              {hasActiveFilters
                ? 'Try adjusting your search or filter criteria.'
                : 'No events are currently available.'}
            </p>
            {hasActiveFilters && (
              <Button
                variant="outline"
                onClick={handleClearFilters}
              >
                Clear Filters
              </Button>
            )}
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {events.map((event) => (
                <EventCard
                  key={event.id}
                  event={event}
                  showRegistration={true}
                />
              ))}
            </div>

            {/* Load More Button */}
            {meta?.hasNext && (
              <div className="text-center mt-12">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={isLoading}
                  className="px-8"
                >
                  {isLoading ? 'Loading...' : 'Load More Events'}
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
