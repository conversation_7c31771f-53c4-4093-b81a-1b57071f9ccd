'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Set } from '../../lib/types';
import { Button } from '../ui/Button';
import { Lightbox } from '../gallery/Lightbox';
import { Camera, Grid, Filter } from 'lucide-react';

interface SetGalleryProps {
  set: Set;
  className?: string;
}

export function SetGallery({ set, className = '' }: SetGalleryProps) {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [filter, setFilter] = useState<string>('all');

  // Convert gallery URLs to GalleryImage format for lightbox
  const galleryImages = set.gallery.map((url, index) => ({
    id: `set-${set.id}-img-${index}`,
    url,
    thumbnailUrl: url,
    alt: `${set.name} gallery image ${index + 1}`,
    caption: `${set.name} - Image ${index + 1}`,
    photographer: 'Alumni Contributor',
    date: new Date().toISOString(),
    album: set.name,
    tags: [set.year.toString(), 'set-gallery'],
    width: 800,
    height: 600,
    fileSize: 1024000
  }));

  const handleImageClick = (index: number) => {
    setCurrentImageIndex(index);
    setLightboxOpen(true);
  };

  const handleCloseLightbox = () => {
    setLightboxOpen(false);
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === galleryImages.length - 1 ? 0 : prev + 1
    );
  };

  const handlePreviousImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? galleryImages.length - 1 : prev - 1
    );
  };

  if (!set.gallery || set.gallery.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Camera className="w-16 h-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No Photos Available
        </h3>
        <p className="text-gray-500">
          This set hasn't uploaded any photos yet.
        </p>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Gallery Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-maroon/10 p-2 rounded-lg">
            <Camera className="w-5 h-5 text-maroon" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900">
              Set Gallery
            </h3>
            <p className="text-sm text-gray-500">
              {set.gallery.length} photo{set.gallery.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setFilter(filter === 'all' ? 'recent' : 'all')}
          >
            <Filter className="w-4 h-4 mr-1" />
            {filter === 'all' ? 'All Photos' : 'Recent'}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleImageClick(0)}
          >
            <Grid className="w-4 h-4 mr-1" />
            View All
          </Button>
        </div>
      </div>

      {/* Gallery Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {galleryImages.map((image, index) => (
          <div
            key={image.id}
            className="group relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer"
            onClick={() => handleImageClick(index)}
          >
            <Image
              src={image.url}
              alt={image.alt}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-110"
              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
            />
            
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
            
            {/* Hover Icon */}
            <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="bg-white/90 p-2 rounded-full">
                <Grid className="w-5 h-5 text-gray-700" />
              </div>
            </div>

            {/* Image Number */}
            <div className="absolute top-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded">
              {index + 1}
            </div>
          </div>
        ))}
      </div>

      {/* View More Button */}
      {set.gallery.length > 8 && (
        <div className="text-center mt-6">
          <Button
            variant="outline"
            onClick={() => handleImageClick(0)}
          >
            View All {set.gallery.length} Photos
          </Button>
        </div>
      )}

      {/* Lightbox */}
      <Lightbox
        images={galleryImages}
        currentIndex={currentImageIndex}
        isOpen={lightboxOpen}
        onClose={handleCloseLightbox}
        onNext={handleNextImage}
        onPrevious={handlePreviousImage}
      />
    </div>
  );
}