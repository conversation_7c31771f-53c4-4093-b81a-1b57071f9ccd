import { render, screen, fireEvent } from '@testing-library/react';
import { Lightbox } from '../Lightbox';
import type { GalleryImage } from '../../../lib/types';

// Mock the dependencies
jest.mock('../../../lib/utils', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' ')
}));

jest.mock('../../ui', () => ({
  Modal: ({ isOpen, children, onClose }: { isOpen: boolean; children: React.ReactNode; onClose: () => void }) => 
    isOpen ? (
      <div data-testid="modal" onClick={onClose}>
        {children}
      </div>
    ) : null
}));

jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, onLoad }: { src: string; alt: string; onLoad: () => void }) => {
    // Simulate image load
    setTimeout(() => onLoad?.(), 0);
    return <img src={src} alt={alt} data-testid="next-image" />;
  }
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const mockImages: GalleryImage[] = [
  {
    id: '1',
    url: 'https://example.com/image1.jpg',
    thumbnailUrl: 'https://example.com/thumb1.jpg',
    alt: 'Test image 1',
    caption: 'Beautiful sunset over the mountains',
    photographer: 'John Doe',
    date: '2023-12-15'
  },
  {
    id: '2',
    url: 'https://example.com/image2.jpg',
    thumbnailUrl: 'https://example.com/thumb2.jpg',
    alt: 'Test image 2',
    caption: 'City skyline at night',
    photographer: 'Jane Smith',
    date: '2023-12-16'
  },
  {
    id: '3',
    url: 'https://example.com/image3.jpg',
    thumbnailUrl: 'https://example.com/thumb3.jpg',
    alt: 'Test image 3',
    caption: 'Forest pathway in autumn',
    photographer: 'Bob Johnson',
    date: '2023-12-17'
  }
];

describe('Lightbox', () => {
  const defaultProps = {
    images: mockImages,
    currentIndex: 0,
    isOpen: true,
    onClose: jest.fn(),
    onNext: jest.fn(),
    onPrevious: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders when open', () => {
    render(<Lightbox {...defaultProps} />);
    
    expect(screen.getByTestId('modal')).toBeInTheDocument();
    expect(screen.getByTestId('next-image')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<Lightbox {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
  });

  it('displays current image information', () => {
    render(<Lightbox {...defaultProps} />);
    
    expect(screen.getByText('Beautiful sunset over the mountains')).toBeInTheDocument();
    expect(screen.getByText('Photo by John Doe')).toBeInTheDocument();
    expect(screen.getByText('1 of 3')).toBeInTheDocument();
  });

  it('displays formatted date', () => {
    render(<Lightbox {...defaultProps} />);
    
    // Check if date is displayed (format may vary by locale)
    expect(screen.getByText(/12\/15\/2023|15\/12\/2023|2023-12-15/)).toBeInTheDocument();
  });

  it('handles keyboard navigation - Escape key', () => {
    render(<Lightbox {...defaultProps} />);
    
    fireEvent.keyDown(document, { key: 'Escape' });
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation - Arrow keys', () => {
    render(<Lightbox {...defaultProps} />);
    
    fireEvent.keyDown(document, { key: 'ArrowLeft' });
    expect(defaultProps.onPrevious).toHaveBeenCalledTimes(1);
    
    fireEvent.keyDown(document, { key: 'ArrowRight' });
    expect(defaultProps.onNext).toHaveBeenCalledTimes(1);
  });

  it('handles keyboard navigation - Fullscreen toggle', () => {
    render(<Lightbox {...defaultProps} />);
    
    fireEvent.keyDown(document, { key: 'f' });
    // Fullscreen state should toggle (we can't easily test the state change without more complex setup)
    
    fireEvent.keyDown(document, { key: 'F' });
    // Should also work with uppercase F
  });

  it('shows navigation buttons when multiple images', () => {
    render(<Lightbox {...defaultProps} />);
    
    expect(screen.getByLabelText('Previous image')).toBeInTheDocument();
    expect(screen.getByLabelText('Next image')).toBeInTheDocument();
  });

  it('hides navigation buttons when single image', () => {
    render(<Lightbox {...defaultProps} images={[mockImages[0]]} />);
    
    expect(screen.queryByLabelText('Previous image')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Next image')).not.toBeInTheDocument();
  });

  it('calls onNext when next button is clicked', () => {
    render(<Lightbox {...defaultProps} />);
    
    fireEvent.click(screen.getByLabelText('Next image'));
    
    expect(defaultProps.onNext).toHaveBeenCalledTimes(1);
  });

  it('calls onPrevious when previous button is clicked', () => {
    render(<Lightbox {...defaultProps} />);
    
    fireEvent.click(screen.getByLabelText('Previous image'));
    
    expect(defaultProps.onPrevious).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when close button is clicked', () => {
    render(<Lightbox {...defaultProps} />);
    
    fireEvent.click(screen.getByLabelText('Close lightbox'));
    
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('shows thumbnail strip for reasonable number of images', () => {
    render(<Lightbox {...defaultProps} />);
    
    // Should show thumbnails for 3 images (less than 20)
    const thumbnails = screen.getAllByRole('button').filter(button => 
      button.querySelector('img')
    );
    expect(thumbnails.length).toBeGreaterThan(0);
  });

  it('shows keyboard shortcuts help', () => {
    render(<Lightbox {...defaultProps} />);
    
    expect(screen.getByText('← → Navigate')).toBeInTheDocument();
    expect(screen.getByText('ESC Close')).toBeInTheDocument();
    expect(screen.getByText('F Fullscreen')).toBeInTheDocument();
  });

  it('handles missing image data gracefully', () => {
    const imageWithoutOptionalData = {
      ...mockImages[0],
      caption: undefined,
      photographer: undefined,
      date: undefined
    };
    
    render(<Lightbox {...defaultProps} images={[imageWithoutOptionalData]} />);
    
    // Should show default caption
    expect(screen.getByText('Image 1')).toBeInTheDocument();
    
    // Should not show photographer or date
    expect(screen.queryByText(/Photo by/)).not.toBeInTheDocument();
  });

  it('prevents body scroll when open', () => {
    const originalOverflow = document.body.style.overflow;
    
    const { unmount } = render(<Lightbox {...defaultProps} />);
    
    expect(document.body.style.overflow).toBe('hidden');
    
    unmount();
    
    expect(document.body.style.overflow).toBe('unset');
    
    // Restore original value
    document.body.style.overflow = originalOverflow;
  });

  it('does not handle keyboard events when closed', () => {
    render(<Lightbox {...defaultProps} isOpen={false} />);
    
    fireEvent.keyDown(document, { key: 'Escape' });
    fireEvent.keyDown(document, { key: 'ArrowLeft' });
    fireEvent.keyDown(document, { key: 'ArrowRight' });
    
    expect(defaultProps.onClose).not.toHaveBeenCalled();
    expect(defaultProps.onPrevious).not.toHaveBeenCalled();
    expect(defaultProps.onNext).not.toHaveBeenCalled();
  });

  it('handles fullscreen toggle button', () => {
    render(<Lightbox {...defaultProps} />);
    
    const fullscreenButton = screen.getByLabelText('Enter fullscreen');
    expect(fullscreenButton).toBeInTheDocument();
    
    fireEvent.click(fullscreenButton);
    
    // After clicking, the label should change (though we can't easily test the state change)
    // The button should still be present
    expect(screen.getByRole('button', { name: /fullscreen/i })).toBeInTheDocument();
  });
});